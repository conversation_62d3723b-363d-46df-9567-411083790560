<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Trial;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Trials resource for managing Freemius trial periods.
 * 
 * Handles CRUD operations and specialized methods for trial management.
 */
class Trials extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'trials';
    }
    
    protected function getEntityClass(): string
    {
        return Trial::class;
    }
    
    /**
     * Get active trials only.
     */
    public function active(): self
    {
        return $this->where('is_active', true);
    }
    
    /**
     * Get inactive trials only.
     */
    public function inactive(): self
    {
        return $this->where('is_active', false);
    }
    
    /**
     * Get expired trials only.
     */
    public function expired(): self
    {
        return $this->where('is_expired', true);
    }
    
    /**
     * Get non-expired trials only.
     */
    public function notExpired(): self
    {
        return $this->where('is_expired', false);
    }
    
    /**
     * Get cancelled trials only.
     */
    public function cancelled(): self
    {
        return $this->where('is_cancelled', true);
    }
    
    /**
     * Get non-cancelled trials only.
     */
    public function notCancelled(): self
    {
        return $this->where('is_cancelled', false);
    }
    
    /**
     * Get valid trials (active, not expired, not cancelled).
     */
    public function valid(): self
    {
        return $this->active()
                   ->notExpired()
                   ->notCancelled();
    }
    
    /**
     * Get trials by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get trials by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get trials by license ID.
     */
    public function byLicense(int $licenseId): self
    {
        return $this->where('license_id', $licenseId);
    }
    
    /**
     * Get trials by trial period length.
     */
    public function byTrialPeriod(int $days): self
    {
        return $this->where('trial_period', $days);
    }
    
    /**
     * Get trials with period greater than specified days.
     */
    public function periodGreaterThan(int $days): self
    {
        return $this->where('trial_period', '>', $days);
    }
    
    /**
     * Get trials with period less than specified days.
     */
    public function periodLessThan(int $days): self
    {
        return $this->where('trial_period', '<', $days);
    }
    
    /**
     * Get trials created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get trials created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get trials started after a specific date.
     */
    public function startedAfter(string $date): self
    {
        return $this->where('started_at', '>', $date);
    }
    
    /**
     * Get trials started before a specific date.
     */
    public function startedBefore(string $date): self
    {
        return $this->where('started_at', '<', $date);
    }
    
    /**
     * Get trials expiring before a specific date.
     */
    public function expiringBefore(string $date): self
    {
        return $this->where('expires_at', '<', $date);
    }
    
    /**
     * Get trials expiring after a specific date.
     */
    public function expiringAfter(string $date): self
    {
        return $this->where('expires_at', '>', $date);
    }
    
    /**
     * Get trials expiring soon (within specified days).
     */
    public function expiringSoon(int $days = 7): self
    {
        $date = date('Y-m-d H:i:s', strtotime("+{$days} days"));
        return $this->where('expires_at', '<=', $date)
                   ->where('expires_at', '>', date('Y-m-d H:i:s'));
    }
    
    /**
     * Start a trial.
     */
    public function start(int $trialId, array $startData = [])
    {
        $endpoint = $this->buildEndpoint($trialId) . '/start';
        $response = $this->httpClient->post($endpoint, $startData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Trial($data);
    }
    
    /**
     * Cancel a trial.
     */
    public function cancel(int $trialId, array $cancellationData = [])
    {
        $endpoint = $this->buildEndpoint($trialId) . '/cancel';
        $response = $this->httpClient->post($endpoint, $cancellationData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Trial($data);
    }
    
    /**
     * Extend a trial period.
     */
    public function extend(int $trialId, int $additionalDays)
    {
        $endpoint = $this->buildEndpoint($trialId) . '/extend';
        $response = $this->httpClient->post($endpoint, ['additional_days' => $additionalDays]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Trial($data);
    }
    
    /**
     * Convert trial to paid subscription.
     */
    public function convert(int $trialId, array $conversionData = []): array
    {
        $endpoint = $this->buildEndpoint($trialId) . '/convert';
        $response = $this->httpClient->post($endpoint, $conversionData);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Reactivate a cancelled trial.
     */
    public function reactivate(int $trialId)
    {
        $endpoint = $this->buildEndpoint($trialId) . '/reactivate';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Trial($data);
    }
    
    /**
     * Get trial usage statistics.
     */
    public function getUsageStats(int $trialId): array
    {
        $endpoint = $this->buildEndpoint($trialId) . '/usage';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Send trial reminder email.
     */
    public function sendReminder(int $trialId, string $reminderType = 'expiring'): bool
    {
        $endpoint = $this->buildEndpoint($trialId) . '/reminder';
        $response = $this->httpClient->post($endpoint, ['type' => $reminderType]);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Get trial conversion statistics.
     */
    public function getConversionStats(array $filters = []): array
    {
        $endpoint = $this->buildEndpoint() . '/conversion-stats';
        if (!empty($filters)) {
            $endpoint .= '?' . http_build_query($filters);
        }
        
        $response = $this->httpClient->get($endpoint);
        return json_decode($response->getBody()->getContents(), true);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'plugin_id', 'trial_ends_at'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a trial");
            }
        }
        
        // Validate numeric fields
        $numericFields = ['user_id', 'plugin_id', 'payment_id', 'subscription_id', 'license_id'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        $numericFields = ['user_id', 'plugin_id', 'payment_id', 'subscription_id', 'license_id'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
    }
}