<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Integration;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\FreemiusSDK;
use Freemius\SDK\Configuration;
use Freemius\SDK\Exceptions\NotFoundException;
use Freemius\SDK\Exceptions\AuthenticationException;

/**
 * Integration tests for additional resource classes
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Resources\Plans::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Resources\Carts::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Resources\Coupons::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Resources\Tags::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Resources\Trials::class)]
#[\PHPUnit\Framework\Attributes\Group('integration')]
class ResourcesIntegrationTest extends TestCase
{
    private FreemiusSDK $sdk;
    private Configuration $config;

    protected function setUp(): void
    {
        // Skip integration tests if no bearer token is provided
        $bearerToken = $_ENV['FREEMIUS_TEST_BEARER_TOKEN'] ?? '';
        if (empty($bearerToken)) {
            $this->markTestSkipped('FREEMIUS_TEST_BEARER_TOKEN environment variable not set');
        }

        // Configure SDK for sandbox environment
        $this->config = new Configuration([
            'bearerToken' => $bearerToken,
            'sandbox' => true,
            'timeout' => 30,
            'retryAttempts' => 2,
            'logging' => false,
            'productScope' => 1, // Use test product ID for sandbox
        ]);

        $this->sdk = new FreemiusSDK($this->config);
    }

    public function testPlansListEndpoint(): void
    {
        try {
            $plans = $this->sdk->plans()->all();

            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
            $this->assertGreaterThanOrEqual(0, $plans->count());

            if ($plans->count() > 0) {
                $firstPlan = $plans->first();
                if ($firstPlan !== null) {
                    $this->assertInstanceOf(\Freemius\SDK\Entities\Plan::class, $firstPlan);
                    $this->assertIsInt($firstPlan->getId());
                    $this->assertIsString($firstPlan->getTitle());
                } else {
                    $this->markTestSkipped('Plans collection returned null first item');
                }
            } else {
                $this->markTestSkipped('No plans available for testing');
            }
        } catch (\Exception $e) {
            $this->markTestSkipped('Plans endpoint not accessible: ' . $e->getMessage());
        }
    }

    public function testPlansGetEndpoint(): void
    {
        try {
            // First get a list of plans to find a valid ID
            $plans = $this->sdk->plans()->all();

            if ($plans->count() > 0) {
                $firstPlan = $plans->first();
                if ($firstPlan !== null) {
                    $planId = $firstPlan->getId();

                    $plan = $this->sdk->plans()->get($planId);

                    $this->assertInstanceOf(\Freemius\SDK\Entities\Plan::class, $plan);
                    $this->assertEquals($planId, $plan->getId());
                    $this->assertIsString($plan->getTitle());
                    $this->assertIsString($plan->getName());
                } else {
                    $this->markTestSkipped('Plans collection returned null first item');
                }
            } else {
                $this->markTestSkipped('No plans available in sandbox for testing');
            }
        } catch (\Exception $e) {
            $this->markTestSkipped('Plans endpoint not accessible: ' . $e->getMessage());
        }
    }

    public function testCartsListEndpoint(): void
    {
        $carts = $this->sdk->carts()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $carts);
        $this->assertGreaterThanOrEqual(0, $carts->count());
        
        if ($carts->count() > 0) {
            $firstCart = $carts->first();
            $this->assertInstanceOf(\Freemius\SDK\Entities\Cart::class, $firstCart);
            $this->assertIsInt($firstCart->getId());
        }
    }

    public function testCouponsListEndpoint(): void
    {
        $coupons = $this->sdk->coupons()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $coupons);
        $this->assertGreaterThanOrEqual(0, $coupons->count());
        
        if ($coupons->count() > 0) {
            $firstCoupon = $coupons->first();
            $this->assertInstanceOf(\Freemius\SDK\Entities\Coupon::class, $firstCoupon);
            $this->assertIsInt($firstCoupon->getId());
            $this->assertIsString($firstCoupon->getCode());
        }
    }

    public function testCouponsGetEndpoint(): void
    {
        // First get a list of coupons to find a valid ID
        $coupons = $this->sdk->coupons()->all();
        
        if ($coupons->count() > 0) {
            $firstCoupon = $coupons->first();
            $couponId = $firstCoupon->getId();
            
            $coupon = $this->sdk->coupons()->get($couponId);
            
            $this->assertInstanceOf(\Freemius\SDK\Entities\Coupon::class, $coupon);
            $this->assertEquals($couponId, $coupon->getId());
            $this->assertIsString($coupon->getCode());
        } else {
            $this->markTestSkipped('No coupons available in sandbox for testing');
        }
    }

    public function testTagsListEndpoint(): void
    {
        $tags = $this->sdk->tags()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $tags);
        $this->assertGreaterThanOrEqual(0, $tags->count());
        
        if ($tags->count() > 0) {
            $firstTag = $tags->first();
            $this->assertInstanceOf(\Freemius\SDK\Entities\Tag::class, $firstTag);
            $this->assertIsInt($firstTag->getId());
            $this->assertIsString($firstTag->getName());
        }
    }

    public function testTrialsListEndpoint(): void
    {
        $trials = $this->sdk->trials()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $trials);
        $this->assertGreaterThanOrEqual(0, $trials->count());
        
        if ($trials->count() > 0) {
            $firstTrial = $trials->first();
            $this->assertInstanceOf(\Freemius\SDK\Entities\Trial::class, $firstTrial);
            $this->assertIsInt($firstTrial->getId());
        }
    }

    public function testTrialsGetEndpoint(): void
    {
        // First get a list of trials to find a valid ID
        $trials = $this->sdk->trials()->all();
        
        if ($trials->count() > 0) {
            $firstTrial = $trials->first();
            $trialId = $firstTrial->getId();
            
            $trial = $this->sdk->trials()->get($trialId);
            
            $this->assertInstanceOf(\Freemius\SDK\Entities\Trial::class, $trial);
            $this->assertEquals($trialId, $trial->getId());
        } else {
            $this->markTestSkipped('No trials available in sandbox for testing');
        }
    }

    public function testResourceNotFoundHandling(): void
    {
        // Test 404 handling for different resources
        $this->expectException(NotFoundException::class);
        
        // Try to get a plan that doesn't exist
        $this->sdk->plans()->get(999999);
    }

    public function testResourcePaginationHandling(): void
    {
        // Test pagination with different resources
        $plans = $this->sdk->plans()->all(['limit' => 2]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
        $this->assertLessThanOrEqual(2, $plans->count());
        
        // Test pagination metadata
        $pagination = $plans->getPagination();
        $this->assertIsArray($pagination);
    }

    public function testResourceFiltering(): void
    {
        // Test filtering capabilities on different resources
        $plans = $this->sdk->plans()->all([
            'limit' => 5,
            'offset' => 0,
        ]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
        $this->assertLessThanOrEqual(5, $plans->count());
        
        $coupons = $this->sdk->coupons()->all([
            'limit' => 3,
        ]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $coupons);
        $this->assertLessThanOrEqual(3, $coupons->count());
    }

    public function testResourceEntityDataIntegrity(): void
    {
        // Test that entities have proper data structure
        $plans = $this->sdk->plans()->all();
        
        if ($plans->count() > 0) {
            $plan = $plans->first();
            
            // Test that entity has expected methods
            $this->assertIsInt($plan->getId());
            $this->assertIsString($plan->getTitle());
            $this->assertIsString($plan->getName());
            $this->assertInstanceOf(\DateTime::class, $plan->getCreatedAt());
            
            // Test that entity can be converted to array and JSON
            $array = $plan->toArray();
            $this->assertIsArray($array);
            $this->assertArrayHasKey('id', $array);
            
            $json = $plan->toJson();
            $this->assertJson($json);
        }
        
        $coupons = $this->sdk->coupons()->all();
        
        if ($coupons->count() > 0) {
            $coupon = $coupons->first();
            
            // Test coupon-specific methods
            $this->assertIsInt($coupon->getId());
            $this->assertIsString($coupon->getCode());
            $this->assertInstanceOf(\DateTime::class, $coupon->getCreatedAt());
            
            // Test discount-related methods
            if ($coupon->getDiscountPercentage() !== null) {
                $this->assertIsFloat($coupon->getDiscountPercentage());
            }
            
            if ($coupon->getDiscountAmount() !== null) {
                $this->assertIsFloat($coupon->getDiscountAmount());
            }
        }
    }

    public function testResourceMethodChaining(): void
    {
        // Test method chaining capabilities on different resources
        $plans = $this->sdk->plans()
            ->where('is_active', true)
            ->limit(5)
            ->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
        $this->assertLessThanOrEqual(5, $plans->count());
        
        $coupons = $this->sdk->coupons()
            ->limit(3)
            ->offset(0)
            ->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $coupons);
        $this->assertLessThanOrEqual(3, $coupons->count());
    }

    public function testResourceScopeManagement(): void
    {
        // Test that resources work with product scope
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            // Set product scope
            $this->sdk->setProductScope($productId);
            
            // Test that scoped requests work for different resources
            $plans = $this->sdk->plans()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
            
            $coupons = $this->sdk->coupons()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $coupons);
            
            $trials = $this->sdk->trials()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $trials);
        } else {
            $this->markTestSkipped('No products available for scope testing');
        }
    }

    public function testCompleteResourceWorkflow(): void
    {
        // Test a complete workflow across multiple resources
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            // Set scope
            $this->sdk->setProductScope($productId);
            
            // Get related resources
            $plans = $this->sdk->plans()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $plans);
            
            $coupons = $this->sdk->coupons()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $coupons);
            
            $trials = $this->sdk->trials()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $trials);
            
            $tags = $this->sdk->tags()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $tags);
            
            // If we have plans, test getting individual plan details
            if ($plans->count() > 0) {
                $plan = $this->sdk->plans()->get($plans->first()->getId());
                $this->assertInstanceOf(\Freemius\SDK\Entities\Plan::class, $plan);
                $this->assertEquals($productId, $plan->getProductId());
            }
        } else {
            $this->markTestSkipped('No products available for workflow testing');
        }
    }

    public function testResourceErrorScenarios(): void
    {
        // Test various error scenarios across resources
        
        // Test invalid resource ID
        try {
            $this->sdk->plans()->get(999999);
            $this->fail('Expected NotFoundException was not thrown');
        } catch (NotFoundException $e) {
            $this->assertStringContains('not found', strtolower($e->getMessage()));
        }
        
        try {
            $this->sdk->coupons()->get(999999);
            $this->fail('Expected NotFoundException was not thrown');
        } catch (NotFoundException $e) {
            $this->assertStringContains('not found', strtolower($e->getMessage()));
        }
        
        try {
            $this->sdk->trials()->get(999999);
            $this->fail('Expected NotFoundException was not thrown');
        } catch (NotFoundException $e) {
            $this->assertStringContains('not found', strtolower($e->getMessage()));
        }
    }
}
