<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\User;

/**
 * Affiliate program webhook event
 * 
 * Triggered for affiliate-related events like approval, blocking, creation, etc.
 */
class AffiliateEvent extends WebhookEvent
{
    private ?User $affiliate = null;

    /**
     * Get the affiliate user associated with this event
     *
     * @return User|null The affiliate user entity
     */
    public function getAffiliate(): ?User
    {
        if ($this->affiliate === null) {
            $affiliateData = $this->getNestedData('affiliate') ?? $this->getNestedData('user');
            if ($affiliateData) {
                $this->affiliate = $this->createEntity($affiliateData, User::class);
            }
        }

        return $this->affiliate;
    }

    /**
     * Get the affiliate ID
     *
     * @return int|null The affiliate ID
     */
    public function getAffiliateId(): ?int
    {
        $affiliateId = $this->get('affiliate_id') ?? $this->get('user_id');
        return $affiliateId ? (int) $affiliateId : null;
    }

    /**
     * Get the payout amount (for payout events)
     *
     * @return float|null The payout amount
     */
    public function getPayoutAmount(): ?float
    {
        $amount = $this->get('payout_amount') ?? $this->get('amount');
        return $amount ? (float) $amount : null;
    }

    /**
     * Check if this is an affiliate approval
     *
     * @return bool True if affiliate was approved
     */
    public function isApproval(): bool
    {
        return $this->eventType === 'affiliate.approved';
    }

    /**
     * Check if this is an affiliate blocking
     *
     * @return bool True if affiliate was blocked
     */
    public function isBlocking(): bool
    {
        return $this->eventType === 'affiliate.blocked';
    }

    /**
     * Check if this is an affiliate creation
     *
     * @return bool True if affiliate was created
     */
    public function isCreation(): bool
    {
        return $this->eventType === 'affiliate.created';
    }

    /**
     * Check if this is a payout event
     *
     * @return bool True if this is a payout event
     */
    public function isPayout(): bool
    {
        return $this->eventType === 'affiliate.payout.pending';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'affiliate.approved' => 'approved',
            'affiliate.blocked' => 'blocked',
            'affiliate.created' => 'created',
            'affiliate.deleted' => 'deleted',
            'affiliate.payout.pending' => 'payout pending',
            'affiliate.paypal.updated' => 'PayPal updated',
            'affiliate.rejected' => 'rejected',
            'affiliate.suspended' => 'suspended',
            'affiliate.unapproved' => 'unapproved',
            'affiliate.updated' => 'updated',
            default => 'changed'
        };

        $parts = ["Affiliate {$action}"];

        if ($affiliateId = $this->getAffiliateId()) {
            $parts[] = "affiliate:{$affiliateId}";
        }

        if ($this->isPayout() && ($amount = $this->getPayoutAmount())) {
            $parts[] = "amount:{$amount}";
        }

        return implode(' ', $parts);
    }
}
