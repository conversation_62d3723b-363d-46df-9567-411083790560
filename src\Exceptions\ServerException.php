<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Server exception for Freemius API server errors (5xx)
 * 
 * Thrown when the Freemius API server encounters an internal error,
 * is temporarily unavailable, or experiences other server-side issues.
 */
class ServerException extends FreemiusException
{
    /**
     * Create a new server exception
     *
     * @param string $message Exception message
     * @param int $code Exception code (typically HTTP status code)
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Server error occurred',
        int $code = 500,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for internal server error (500)
     *
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function internalServerError(string $message = '', array $context = []): self
    {
        $defaultMessage = 'Internal server error occurred';
        $finalMessage = $message ?: $defaultMessage;

        return new self($finalMessage, 500, null, $context);
    }

    /**
     * Create an exception for bad gateway error (502)
     *
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function badGateway(string $message = '', array $context = []): self
    {
        $defaultMessage = 'Bad gateway - upstream server error';
        $finalMessage = $message ?: $defaultMessage;

        return new self($finalMessage, 502, null, $context);
    }

    /**
     * Create an exception for service unavailable error (503)
     *
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function serviceUnavailable(string $message = '', array $context = []): self
    {
        $defaultMessage = 'Service temporarily unavailable';
        $finalMessage = $message ?: $defaultMessage;

        return new self($finalMessage, 503, null, $context);
    }

    /**
     * Create an exception for gateway timeout error (504)
     *
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function gatewayTimeout(string $message = '', array $context = []): self
    {
        $defaultMessage = 'Gateway timeout - server took too long to respond';
        $finalMessage = $message ?: $defaultMessage;

        return new self($finalMessage, 504, null, $context);
    }

    /**
     * Create a server exception based on HTTP status code
     *
     * @param int $statusCode HTTP status code
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function fromStatusCode(
        int $statusCode,
        string $message = '',
        array $context = []
    ): self {
        $context['status_code'] = $statusCode;

        switch ($statusCode) {
            case 500:
                return self::internalServerError($message, $context);
            case 502:
                return self::badGateway($message, $context);
            case 503:
                return self::serviceUnavailable($message, $context);
            case 504:
                return self::gatewayTimeout($message, $context);
            default:
                $defaultMessage = "Server error (HTTP {$statusCode})";
                $finalMessage = $message ?: $defaultMessage;
                return new self($finalMessage, $statusCode, null, $context);
        }
    }

    /**
     * Check if the error is likely temporary and retryable
     *
     * @return bool True if the error might be temporary
     */
    public function isRetryable(): bool
    {
        $retryableCodes = [502, 503, 504];
        return in_array($this->getCode(), $retryableCodes, true);
    }

    /**
     * Get a user-friendly error message
     *
     * @return string User-friendly message
     */
    public function getUserFriendlyMessage(): string
    {
        switch ($this->getCode()) {
            case 500:
                return 'The Freemius service is experiencing technical difficulties. Please try again later.';
            case 502:
                return 'The Freemius service is temporarily unavailable due to a gateway error. Please try again later.';
            case 503:
                return 'The Freemius service is temporarily unavailable for maintenance. Please try again later.';
            case 504:
                return 'The Freemius service is taking longer than expected to respond. Please try again later.';
            default:
                return 'The Freemius service is experiencing technical difficulties. Please try again later.';
        }
    }

    /**
     * Get suggested retry delay in seconds
     *
     * @return int Suggested delay before retrying
     */
    public function getSuggestedRetryDelay(): int
    {
        switch ($this->getCode()) {
            case 500:
                return 30; // 30 seconds for internal server errors
            case 502:
                return 15; // 15 seconds for bad gateway
            case 503:
                return 60; // 1 minute for service unavailable
            case 504:
                return 45; // 45 seconds for gateway timeout
            default:
                return 30; // Default 30 seconds
        }
    }
}