<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Coupon entity representing a Freemius discount coupon.
 * 
 * A coupon provides discount codes that can be applied to purchases
 * to reduce the total cost for customers.
 */
class Coupon extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'product_id' => 'integer',
        'plan_id' => 'integer',
        'discount' => 'integer',
        'usage_limit' => 'integer',
        'usage_count' => 'integer',
        'minimum_amount' => 'float',
        'is_active' => 'boolean',
        'is_single_use' => 'boolean',
        'is_recurring' => 'boolean',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'end_date',
        'start_date',
    ];

    /**
     * Get the coupon ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the product ID.
     */
    public function getProductId(): int
    {
        return $this->getAttribute('product_id');
    }

    /**
     * Get the plan ID (if coupon is plan-specific).
     */
    public function getPlanId(): ?int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the coupon code.
     */
    public function getCode(): string
    {
        return $this->getAttribute('code', '');
    }

    /**
     * Get the coupon title.
     */
    public function getTitle(): string
    {
        return $this->getAttribute('title', '');
    }

    /**
     * Get the coupon description.
     */
    public function getDescription(): string
    {
        return $this->getAttribute('description', '');
    }

    /**
     * Get the discount type.
     */
    public function getDiscountType(): string
    {
        return $this->getAttribute('discount_type', 'percentage');
    }

    /**
     * Get the discount value.
     */
    public function getDiscount(): int
    {
        return $this->getAttribute('discount', 0);
    }

    /**
     * Get the currency code.
     */
    public function getCurrency(): string
    {
        return $this->getAttribute('currency', 'USD');
    }

    /**
     * Get the usage limit.
     */
    public function getUsageLimit(): ?int
    {
        return $this->getAttribute('usage_limit');
    }

    /**
     * Get the current usage count.
     */
    public function getUsageCount(): int
    {
        return $this->getAttribute('usage_count', 0);
    }

    /**
     * Get the minimum purchase amount required.
     */
    public function getMinimumAmount(): float
    {
        return $this->getAttribute('minimum_amount', 0.0);
    }

    /**
     * Check if the coupon is active.
     */
    public function isActive(): bool
    {
        return $this->getAttribute('is_active', true);
    }

    /**
     * Check if the coupon is single use.
     */
    public function isSingleUse(): bool
    {
        return $this->getAttribute('is_single_use', false);
    }

    /**
     * Check if the coupon applies to recurring payments.
     */
    public function isRecurring(): bool
    {
        return $this->getAttribute('is_recurring', false);
    }

    /**
     * Check if the coupon has expired.
     */
    public function isExpired(): bool
    {
        $endDate = $this->getEndDate();
        return $endDate && $endDate < new DateTime();
    }

    /**
     * Check if the coupon has started.
     */
    public function hasStarted(): bool
    {
        $startDate = $this->getStartDate();
        return !$startDate || $startDate <= new DateTime();
    }

    /**
     * Check if the coupon is currently valid.
     */
    public function isValid(): bool
    {
        return $this->isActive() && $this->hasStarted() && !$this->isExpired();
    }

    /**
     * Check if the coupon usage limit has been reached.
     */
    public function isUsageLimitReached(): bool
    {
        $limit = $this->getUsageLimit();
        return $limit !== null && $this->getUsageCount() >= $limit;
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the end date.
     */
    public function getEndDate(): ?DateTime
    {
        return $this->getAttribute('end_date');
    }

    /**
     * Get the start date.
     */
    public function getStartDate(): ?DateTime
    {
        return $this->getAttribute('start_date');
    }
}