<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Authorization exception for Freemius API authorization failures
 * 
 * Thrown when the user is authenticated but lacks sufficient permissions
 * to access a specific resource or perform a specific action.
 */
class AuthorizationException extends FreemiusException
{
    /**
     * Create a new authorization exception
     *
     * @param string $message Exception message
     * @param int $code Exception code (typically HTTP status code)
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Access denied',
        int $code = 403,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for insufficient permissions to access a resource
     *
     * @param string $resource The resource being accessed
     * @param string $action The action being attempted
     * @param array $context Additional context
     * @return self
     */
    public static function insufficientPermissions(
        string $resource = '',
        string $action = '',
        array $context = []
    ): self {
        $message = 'Insufficient permissions';
        
        if ($resource && $action) {
            $message .= " to {$action} {$resource}";
        } elseif ($resource) {
            $message .= " for {$resource}";
        }

        $context = array_merge([
            'resource' => $resource,
            'action' => $action,
        ], $context);

        return new self($message, 403, null, $context);
    }

    /**
     * Create an exception for product scope access denied
     *
     * @param int $productId The product ID that was denied
     * @param array $context Additional context
     * @return self
     */
    public static function productAccessDenied(int $productId, array $context = []): self
    {
        $context = array_merge([
            'product_id' => $productId,
        ], $context);

        return new self(
            "Access denied to product {$productId}",
            403,
            null,
            $context
        );
    }

    /**
     * Create an exception for plan or subscription access denied
     *
     * @param string $planType The type of plan (e.g., 'premium', 'professional')
     * @param array $context Additional context
     * @return self
     */
    public static function planAccessDenied(string $planType = '', array $context = []): self
    {
        $message = 'Access denied';
        
        if ($planType) {
            $message .= " - {$planType} plan required";
            $context['required_plan'] = $planType;
        }

        return new self($message, 403, null, $context);
    }

    /**
     * Create an exception for API endpoint access denied
     *
     * @param string $endpoint The API endpoint that was denied
     * @param string $method The HTTP method used
     * @param array $context Additional context
     * @return self
     */
    public static function endpointAccessDenied(
        string $endpoint = '',
        string $method = '',
        array $context = []
    ): self {
        $message = 'Access denied to API endpoint';
        
        if ($method && $endpoint) {
            $message .= " {$method} {$endpoint}";
        } elseif ($endpoint) {
            $message .= " {$endpoint}";
        }

        $context = array_merge([
            'endpoint' => $endpoint,
            'method' => $method,
        ], $context);

        return new self($message, 403, null, $context);
    }
}