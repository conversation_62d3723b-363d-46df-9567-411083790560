<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

/**
 * Unknown webhook event
 * 
 * Used for webhook events that are not specifically supported by the SDK.
 * Provides access to raw event data without specific parsing.
 */
class UnknownEvent extends WebhookEvent
{
    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $parts = ["Unknown event: {$this->eventType}"];

        if ($webhookId = $this->getWebhookId()) {
            $parts[] = "webhook:{$webhookId}";
        }

        if ($productId = $this->getProductId()) {
            $parts[] = "product:{$productId}";
        }

        if ($userId = $this->getUserId()) {
            $parts[] = "user:{$userId}";
        }

        return implode(' ', $parts);
    }

    /**
     * Get available data keys
     *
     * @return array List of available data keys
     */
    public function getAvailableKeys(): array
    {
        return array_keys($this->data);
    }

    /**
     * Check if this event has a specific data key
     *
     * @param string $key The data key to check
     * @return bool True if key exists
     */
    public function hasKey(string $key): bool
    {
        return array_key_exists($key, $this->data);
    }

    /**
     * Get all nested keys in dot notation
     *
     * @param array $data The data array (defaults to event data)
     * @param string $prefix The key prefix
     * @return array List of nested keys
     */
    public function getNestedKeys(array $data = null, string $prefix = ''): array
    {
        $data = $data ?? $this->data;
        $keys = [];

        foreach ($data as $key => $value) {
            $fullKey = $prefix ? "{$prefix}.{$key}" : $key;
            $keys[] = $fullKey;

            if (is_array($value)) {
                $keys = array_merge($keys, $this->getNestedKeys($value, $fullKey));
            }
        }

        return $keys;
    }

    /**
     * Convert to array with additional metadata
     *
     * @return array The event data with metadata
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        $data['is_unknown_event'] = true;
        $data['available_keys'] = $this->getAvailableKeys();
        $data['nested_keys'] = $this->getNestedKeys();

        return $data;
    }
}