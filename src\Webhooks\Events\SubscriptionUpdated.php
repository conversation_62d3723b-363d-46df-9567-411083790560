<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\Subscription;
use Freemius\SDK\Entities\User;
use Freemius\SDK\Entities\License;
use DateTime;

/**
 * Subscription update webhook event
 * 
 * Triggered when a subscription is created, updated, cancelled, expired, or renewed.
 */
class SubscriptionUpdated extends WebhookEvent
{
    private ?Subscription $subscription = null;
    private ?User $user = null;
    private ?License $license = null;

    /**
     * Get the subscription associated with this event
     *
     * @return Subscription|null The subscription entity
     */
    public function getSubscription(): ?Subscription
    {
        if ($this->subscription === null) {
            $subscriptionData = $this->getNestedData('subscription');
            if ($subscriptionData) {
                $this->subscription = $this->createEntity($subscriptionData, Subscription::class);
            }
        }

        return $this->subscription;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the license associated with this event
     *
     * @return License|null The license entity
     */
    public function getLicense(): ?License
    {
        if ($this->license === null) {
            $licenseData = $this->getNestedData('license');
            if ($licenseData) {
                $this->license = $this->createEntity($licenseData, License::class);
            }
        }

        return $this->license;
    }

    /**
     * Get the subscription ID
     *
     * @return int|null The subscription ID
     */
    public function getSubscriptionId(): ?int
    {
        $subscriptionId = $this->get('subscription_id');
        return $subscriptionId ? (int) $subscriptionId : null;
    }

    /**
     * Get the plan ID
     *
     * @return int|null The plan ID
     */
    public function getPlanId(): ?int
    {
        $planId = $this->getNestedData('subscription.plan_id') ?? $this->get('plan_id');
        return $planId ? (int) $planId : null;
    }

    /**
     * Check if this is a subscription creation event
     *
     * @return bool True if this is a creation
     */
    public function isCreation(): bool
    {
        return $this->eventType === 'subscription.created';
    }

    /**
     * Check if this is a subscription update event
     *
     * @return bool True if this is an update
     */
    public function isUpdate(): bool
    {
        return $this->eventType === 'subscription.updated';
    }

    /**
     * Check if this is a subscription cancellation event
     *
     * @return bool True if this is a cancellation
     */
    public function isCancellation(): bool
    {
        return $this->eventType === 'subscription.canceled';
    }

    /**
     * Check if this is a subscription expiration event
     *
     * @return bool True if this is an expiration
     */
    public function isExpiration(): bool
    {
        return $this->eventType === 'subscription.expired';
    }

    /**
     * Check if this is a subscription renewal event
     *
     * @return bool True if this is a renewal
     */
    public function isRenewal(): bool
    {
        return $this->eventType === 'subscription.renewed';
    }

    /**
     * Get the subscription status
     *
     * @return string|null The subscription status
     */
    public function getSubscriptionStatus(): ?string
    {
        return $this->getNestedData('subscription.status') ?? $this->get('status');
    }

    /**
     * Get the subscription billing cycle
     *
     * @return string|null The billing cycle (monthly, annual, etc.)
     */
    public function getBillingCycle(): ?string
    {
        return $this->getNestedData('subscription.billing_cycle') ?? 
               $this->getNestedData('subscription.cycle') ?? 
               $this->get('billing_cycle');
    }

    /**
     * Get the subscription amount
     *
     * @return float|null The subscription amount
     */
    public function getAmount(): ?float
    {
        $amount = $this->getNestedData('subscription.amount') ?? 
                  $this->getNestedData('subscription.total') ?? 
                  $this->get('amount');
        return $amount ? (float) $amount : null;
    }

    /**
     * Get the subscription currency
     *
     * @return string|null The currency code
     */
    public function getCurrency(): ?string
    {
        return $this->getNestedData('subscription.currency') ?? $this->get('currency');
    }

    /**
     * Get the next billing date
     *
     * @return DateTime|null The next billing date
     */
    public function getNextBillingDate(): ?DateTime
    {
        $nextBilling = $this->getNestedData('subscription.next_payment') ?? 
                       $this->getNestedData('subscription.next_billing') ?? 
                       $this->get('next_billing_date');

        if (!$nextBilling) {
            return null;
        }

        if (is_numeric($nextBilling)) {
            return new DateTime('@' . $nextBilling);
        }

        if (is_string($nextBilling)) {
            return new DateTime($nextBilling);
        }

        return null;
    }

    /**
     * Get the subscription expiration date
     *
     * @return DateTime|null The expiration date
     */
    public function getExpirationDate(): ?DateTime
    {
        $expiration = $this->getNestedData('subscription.expires_at') ?? 
                      $this->getNestedData('subscription.expiration') ?? 
                      $this->get('expires_at');

        if (!$expiration) {
            return null;
        }

        if (is_numeric($expiration)) {
            return new DateTime('@' . $expiration);
        }

        if (is_string($expiration)) {
            return new DateTime($expiration);
        }

        return null;
    }

    /**
     * Check if the subscription is active
     *
     * @return bool True if active
     */
    public function isActive(): bool
    {
        $status = $this->getSubscriptionStatus();
        return in_array($status, ['active', 'trialing'], true);
    }

    /**
     * Check if the subscription is cancelled
     *
     * @return bool True if cancelled
     */
    public function isCancelled(): bool
    {
        $status = $this->getSubscriptionStatus();
        return $status === 'cancelled';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'subscription.created' => 'created',
            'subscription.updated' => 'updated',
            'subscription.canceled' => 'canceled',
            'subscription.expired' => 'expired',
            'subscription.renewed' => 'renewed',
            default => 'changed'
        };

        $parts = ["Subscription {$action}"];

        if ($subscriptionId = $this->getSubscriptionId()) {
            $parts[] = "subscription:{$subscriptionId}";
        }

        if ($amount = $this->getAmount()) {
            $currency = $this->getCurrency() ?? 'USD';
            $parts[] = "{$currency}{$amount}";
        }

        if ($cycle = $this->getBillingCycle()) {
            $parts[] = $cycle;
        }

        return implode(' ', $parts);
    }

    /**
     * Convert to array with subscription-specific data
     *
     * @return array The event data
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        $data['subscription_id'] = $this->getSubscriptionId();
        $data['plan_id'] = $this->getPlanId();
        $data['status'] = $this->getSubscriptionStatus();
        $data['billing_cycle'] = $this->getBillingCycle();
        $data['amount'] = $this->getAmount();
        $data['currency'] = $this->getCurrency();
        $data['next_billing_date'] = $this->getNextBillingDate()?->format('c');
        $data['expiration_date'] = $this->getExpirationDate()?->format('c');
        $data['is_active'] = $this->isActive();
        $data['is_cancelled'] = $this->isCancelled();
        $data['action'] = match ($this->eventType) {
            'subscription.created' => 'created',
            'subscription.updated' => 'updated',
            'subscription.canceled' => 'canceled',
            'subscription.expired' => 'expired',
            'subscription.renewed' => 'renewed',
            default => 'unknown'
        };

        return $data;
    }
}