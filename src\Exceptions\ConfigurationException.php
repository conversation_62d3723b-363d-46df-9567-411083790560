<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Configuration exception for SDK configuration errors
 * 
 * Thrown when the SDK is misconfigured, missing required settings,
 * or has invalid configuration values.
 */
class ConfigurationException extends FreemiusException
{
    /**
     * Create a new configuration exception
     *
     * @param string $message Exception message
     * @param int $code Exception code
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Configuration error',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for missing required configuration
     *
     * @param string $configKey The missing configuration key
     * @param array $context Additional context
     * @return self
     */
    public static function missingRequired(string $configKey, array $context = []): self
    {
        $context = array_merge([
            'config_key' => $configKey,
            'error_type' => 'missing_required',
        ], $context);

        return new self(
            "Required configuration '{$configKey}' is missing",
            0,
            null,
            $context
        );
    }

    /**
     * Create an exception for missing API credentials
     *
     * @param array $missingCredentials List of missing credential keys
     * @param array $context Additional context
     * @return self
     */
    public static function missingCredentials(array $missingCredentials = [], array $context = []): self
    {
        $message = 'Required API credentials are missing';
        
        if (!empty($missingCredentials)) {
            $message .= ': ' . implode(', ', $missingCredentials);
        }

        $context = array_merge([
            'missing_credentials' => $missingCredentials,
            'error_type' => 'missing_credentials',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for invalid configuration value
     *
     * @param string $configKey The invalid configuration key
     * @param mixed $value The invalid value
     * @param string $expectedType Expected type or format
     * @param array $context Additional context
     * @return self
     */
    public static function invalidValue(
        string $configKey,
        $value,
        string $expectedType = '',
        array $context = []
    ): self {
        $message = "Invalid value for configuration '{$configKey}'";
        
        if ($expectedType) {
            $message .= ", expected {$expectedType}";
        }

        $context = array_merge([
            'config_key' => $configKey,
            'invalid_value' => $value,
            'expected_type' => $expectedType,
            'error_type' => 'invalid_value',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for invalid Bearer token format
     *
     * @param string $token The invalid token (will be redacted)
     * @param array $context Additional context
     * @return self
     */
    public static function invalidBearerTokenFormat(string $token = '', array $context = []): self
    {
        $context = array_merge([
            'token_length' => strlen($token),
            'token_prefix' => $token ? substr($token, 0, 8) . '...' : 'empty',
            'error_type' => 'invalid_token_format',
        ], $context);

        return new self(
            'Bearer token format is invalid',
            0,
            null,
            $context
        );
    }

    /**
     * Create an exception for invalid base URL
     *
     * @param string $url The invalid URL
     * @param array $context Additional context
     * @return self
     */
    public static function invalidBaseUrl(string $url = '', array $context = []): self
    {
        $context = array_merge([
            'invalid_url' => $url,
            'error_type' => 'invalid_base_url',
        ], $context);

        return new self(
            "Invalid base URL: {$url}",
            0,
            null,
            $context
        );
    }

    /**
     * Create an exception for invalid timeout value
     *
     * @param mixed $timeout The invalid timeout value
     * @param array $context Additional context
     * @return self
     */
    public static function invalidTimeout($timeout, array $context = []): self
    {
        $context = array_merge([
            'invalid_timeout' => $timeout,
            'error_type' => 'invalid_timeout',
        ], $context);

        return new self(
            "Invalid timeout value: {$timeout}. Must be a positive integer",
            0,
            null,
            $context
        );
    }

    /**
     * Create an exception for invalid environment setting
     *
     * @param string $environment The invalid environment
     * @param array $validEnvironments List of valid environments
     * @param array $context Additional context
     * @return self
     */
    public static function invalidEnvironment(
        string $environment,
        array $validEnvironments = [],
        array $context = []
    ): self {
        $message = "Invalid environment: {$environment}";
        
        if (!empty($validEnvironments)) {
            $message .= '. Valid options: ' . implode(', ', $validEnvironments);
        }

        $context = array_merge([
            'invalid_environment' => $environment,
            'valid_environments' => $validEnvironments,
            'error_type' => 'invalid_environment',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for conflicting configuration options
     *
     * @param array $conflictingOptions The conflicting configuration keys
     * @param string $reason Reason for the conflict
     * @param array $context Additional context
     * @return self
     */
    public static function conflictingOptions(
        array $conflictingOptions,
        string $reason = '',
        array $context = []
    ): self {
        $message = 'Conflicting configuration options: ' . implode(', ', $conflictingOptions);
        
        if ($reason) {
            $message .= ". {$reason}";
        }

        $context = array_merge([
            'conflicting_options' => $conflictingOptions,
            'reason' => $reason,
            'error_type' => 'conflicting_options',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Get configuration suggestions based on the error
     *
     * @return array Suggestions for fixing the configuration
     */
    public function getConfigurationSuggestions(): array
    {
        $errorType = $this->getContext()['error_type'] ?? '';
        
        switch ($errorType) {
            case 'missing_credentials':
                return [
                    'Set your Bearer token using Configuration::setBearerToken()',
                    'Or pass credentials in the constructor: new FreemiusSDK($config)',
                    'Check that your .env file contains FREEMIUS_BEARER_TOKEN',
                ];
                
            case 'invalid_token_format':
                return [
                    'Ensure your Bearer token is a valid string',
                    'Check that the token is not empty or null',
                    'Verify the token format with Freemius documentation',
                ];
                
            case 'invalid_base_url':
                return [
                    'Use https://api.freemius.com/v1/ for production',
                    'Use https://docs.freemius.com/_mock/api/ for sandbox/testing',
                    'Ensure the URL includes the protocol (https://)',
                ];
                
            case 'invalid_timeout':
                return [
                    'Timeout must be a positive integer (seconds)',
                    'Recommended values: 30-120 seconds',
                    'Use 0 for no timeout (not recommended)',
                ];
                
            default:
                return [
                    'Check the SDK documentation for configuration options',
                    'Verify all required configuration values are set',
                    'Ensure configuration values are of the correct type',
                ];
        }
    }
}