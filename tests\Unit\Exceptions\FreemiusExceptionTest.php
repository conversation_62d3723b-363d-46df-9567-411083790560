<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Exceptions;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Exceptions\FreemiusException;
use Freemius\SDK\Exceptions\AuthenticationException;
use Freemius\SDK\Exceptions\ValidationException;
use Freemius\SDK\Exceptions\NotFoundException;
use Freemius\SDK\Exceptions\RateLimitException;
use Exception;

/**
 * Unit tests for Freemius exceptions
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\FreemiusException::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\AuthenticationException::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\ValidationException::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\NotFoundException::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\RateLimitException::class)]
class FreemiusExceptionTest extends TestCase
{
    public function testFreemiusExceptionBasic(): void
    {
        $message = 'Test error message';
        $code = 500;
        
        $exception = new FreemiusException($message, $code);
        
        $this->assertEquals($message, $exception->getMessage());
        $this->assertEquals($code, $exception->getCode());
        $this->assertNull($exception->getPrevious());
        $this->assertEquals([], $exception->getContext());
    }

    public function testFreemiusExceptionWithPrevious(): void
    {
        $previousException = new Exception('Previous error');
        $exception = new FreemiusException('Current error', 400, $previousException);
        
        $this->assertEquals('Current error', $exception->getMessage());
        $this->assertEquals(400, $exception->getCode());
        $this->assertSame($previousException, $exception->getPrevious());
    }

    public function testFreemiusExceptionWithContext(): void
    {
        $context = [
            'request_id' => '12345',
            'endpoint' => '/api/products',
            'method' => 'GET',
        ];
        
        $exception = new FreemiusException('Error with context', 500, null, $context);
        
        $this->assertEquals($context, $exception->getContext());
    }

    public function testFreemiusExceptionWithAllParameters(): void
    {
        $message = 'Complete error';
        $code = 422;
        $previous = new Exception('Root cause');
        $context = ['user_id' => 789, 'action' => 'create'];
        
        $exception = new FreemiusException($message, $code, $previous, $context);
        
        $this->assertEquals($message, $exception->getMessage());
        $this->assertEquals($code, $exception->getCode());
        $this->assertSame($previous, $exception->getPrevious());
        $this->assertEquals($context, $exception->getContext());
    }

    public function testAuthenticationException(): void
    {
        $exception = new AuthenticationException('Invalid credentials');
        
        $this->assertInstanceOf(FreemiusException::class, $exception);
        $this->assertEquals('Invalid credentials', $exception->getMessage());
    }

    public function testAuthenticationExceptionWithContext(): void
    {
        $context = ['token_type' => 'bearer', 'expired' => true];
        $exception = new AuthenticationException('Token expired', 401, null, $context);
        
        $this->assertEquals($context, $exception->getContext());
        $this->assertEquals(401, $exception->getCode());
    }

    public function testValidationException(): void
    {
        $errors = [
            'email' => ['Email is required'],
            'name' => ['Name must be at least 2 characters'],
        ];
        
        $exception = new ValidationException('Validation failed', $errors);
        
        $this->assertInstanceOf(FreemiusException::class, $exception);
        $this->assertEquals('Validation failed', $exception->getMessage());
        $this->assertEquals($errors, $exception->getErrors());
    }

    public function testValidationExceptionWithCode(): void
    {
        $errors = ['field' => ['error']];
        $exception = new ValidationException('Validation error', $errors, 422);
        
        $this->assertEquals(422, $exception->getCode());
        $this->assertEquals($errors, $exception->getErrors());
    }

    public function testValidationExceptionWithPrevious(): void
    {
        $previous = new Exception('Root validation error');
        $errors = ['test' => ['test error']];
        $exception = new ValidationException('Validation failed', $errors, 400, $previous);
        
        $this->assertSame($previous, $exception->getPrevious());
        $this->assertEquals($errors, $exception->getErrors());
    }

    public function testValidationExceptionWithContext(): void
    {
        $errors = ['name' => ['Required']];
        $exception = new ValidationException('Form validation failed', $errors, 422, null);
        
        $this->assertEquals($errors, $exception->getErrors());
        // ValidationException doesn't support context in its constructor
        $this->assertEquals([], $exception->getContext());
    }

    public function testNotFoundException(): void
    {
        $exception = new NotFoundException('Resource not found');
        
        $this->assertInstanceOf(FreemiusException::class, $exception);
        $this->assertEquals('Resource not found', $exception->getMessage());
        $this->assertEquals(404, $exception->getCode());
    }

    public function testNotFoundExceptionWithCustomCode(): void
    {
        $exception = new NotFoundException('Custom not found', 410);
        
        $this->assertEquals(410, $exception->getCode());
    }

    public function testRateLimitException(): void
    {
        $exception = new RateLimitException('Rate limit exceeded');
        
        $this->assertInstanceOf(FreemiusException::class, $exception);
        $this->assertEquals('Rate limit exceeded', $exception->getMessage());
        $this->assertEquals(429, $exception->getCode());
        $this->assertNull($exception->getRetryAfter());
    }

    public function testRateLimitExceptionWithCustomCode(): void
    {
        $exception = new RateLimitException('Custom rate limit', 503);
        
        $this->assertEquals(503, $exception->getCode());
        $this->assertNull($exception->getRetryAfter());
    }

    public function testRateLimitExceptionWithPrevious(): void
    {
        $previous = new Exception('HTTP 429 error');
        $exception = new RateLimitException('Rate limited', 429, $previous);
        
        $this->assertSame($previous, $exception->getPrevious());
        $this->assertNull($exception->getRetryAfter());
    }

    public function testRateLimitExceptionWithContext(): void
    {
        $context = ['limit' => 1000, 'remaining' => 0, 'reset_time' => 1623456789];
        $exception = new RateLimitException('API limit reached', 429, null, $context);
        
        $this->assertEquals($context, $exception->getContext());
        $this->assertNull($exception->getRetryAfter());
    }

    public function testExceptionInheritance(): void
    {
        $exceptions = [
            new AuthenticationException('Auth error'),
            new ValidationException('Validation error', []),
            new NotFoundException('Not found'),
            new RateLimitException('Rate limit', 60),
        ];

        foreach ($exceptions as $exception) {
            $this->assertInstanceOf(FreemiusException::class, $exception);
            $this->assertInstanceOf(Exception::class, $exception);
        }
    }

    public function testExceptionSerialization(): void
    {
        $context = ['key' => 'value', 'number' => 123];
        $exception = new FreemiusException('Serialization test', 500, null, $context);
        
        $serialized = serialize($exception);
        $unserialized = unserialize($serialized);
        
        $this->assertEquals($exception->getMessage(), $unserialized->getMessage());
        $this->assertEquals($exception->getCode(), $unserialized->getCode());
        $this->assertEquals($exception->getContext(), $unserialized->getContext());
    }

    public function testExceptionToString(): void
    {
        $exception = new FreemiusException('Test exception', 500);
        $string = (string) $exception;
        
        $this->assertStringContainsString('Test exception', $string);
        $this->assertStringContainsString('FreemiusException', $string);
    }

    public function testContextImmutability(): void
    {
        $originalContext = ['key' => 'value'];
        $exception = new FreemiusException('Test', 500, null, $originalContext);
        
        $retrievedContext = $exception->getContext();
        $retrievedContext['new_key'] = 'new_value';
        
        // Original context should remain unchanged
        $this->assertEquals(['key' => 'value'], $exception->getContext());
    }

    public function testValidationErrorsImmutability(): void
    {
        $originalErrors = ['field' => ['error']];
        $exception = new ValidationException('Test', $originalErrors);
        
        $retrievedErrors = $exception->getErrors();
        $retrievedErrors['new_field'] = ['new_error'];
        
        // Original errors should remain unchanged
        $this->assertEquals(['field' => ['error']], $exception->getErrors());
    }

    public function testEmptyContext(): void
    {
        $exception = new FreemiusException('Test', 500, null, []);
        $this->assertEquals([], $exception->getContext());
    }

    public function testEmptyValidationErrors(): void
    {
        $exception = new ValidationException('Test', []);
        $this->assertEquals([], $exception->getErrors());
    }

    public function testRateLimitExceptionWithRetryInfo(): void
    {
        $exception = RateLimitException::withRetryInfo(60, [
            'X-RateLimit-Limit' => '1000',
            'X-RateLimit-Remaining' => '0',
            'X-RateLimit-Reset' => '1623456789',
        ]);
        
        $this->assertEquals(60, $exception->getRetryAfter());
        $this->assertEquals(1000, $exception->getRateLimitLimit());
        $this->assertEquals(0, $exception->getRateLimitRemaining());
        $this->assertEquals(1623456789, $exception->getRateLimitReset());
        $this->assertTrue($exception->hasRetryInfo());
    }

    public function testRateLimitExceptionSetters(): void
    {
        $exception = new RateLimitException('Test');
        
        $exception->setRetryAfter(120);
        $exception->setRateLimitLimit(500);
        $exception->setRateLimitRemaining(10);
        $exception->setRateLimitReset(1623456789);
        
        $this->assertEquals(120, $exception->getRetryAfter());
        $this->assertEquals(500, $exception->getRateLimitLimit());
        $this->assertEquals(10, $exception->getRateLimitRemaining());
        $this->assertEquals(1623456789, $exception->getRateLimitReset());
        $this->assertTrue($exception->hasRetryInfo());
    }

    public function testRateLimitExceptionRetryDescription(): void
    {
        $exception = new RateLimitException('Test');
        
        // No retry info
        $this->assertEquals('Retry timing not available', $exception->getRetryDescription());
        
        // Seconds
        $exception->setRetryAfter(30);
        $this->assertEquals('Retry in 30 seconds', $exception->getRetryDescription());
        
        // Minutes
        $exception->setRetryAfter(120);
        $this->assertEquals('Retry in 2 minutes', $exception->getRetryDescription());
    }

    public function testRateLimitExceptionStatus(): void
    {
        $exception = new RateLimitException('Test');
        $exception->setRetryAfter(60);
        $exception->setRateLimitLimit(1000);
        $exception->setRateLimitRemaining(0);
        $exception->setRateLimitReset(1623456789);
        
        $status = $exception->getRateLimitStatus();
        
        $this->assertEquals([
            'limit' => 1000,
            'remaining' => 0,
            'reset' => 1623456789,
            'retry_after' => 60,
        ], $status);
    }
}