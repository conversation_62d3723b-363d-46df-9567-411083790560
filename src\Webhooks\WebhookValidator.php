<?php

declare(strict_types=1);

namespace <PERSON>mius\SDK\Webhooks;

use Freemius\SDK\Exceptions\WebhookException;

/**
 * Webhook signature validator for Freemius webhooks
 * 
 * Validates webhook signatures using Freemius webhook secret and implements
 * timestamp validation to prevent replay attacks.
 */
class WebhookValidator
{
    private string $webhookSecret;
    private int $timestampTolerance;

    /**
     * Create a new webhook validator
     *
     * @param string $webhookSecret The webhook secret from <PERSON>mius
     * @param int $timestampTolerance Timestamp tolerance in seconds (default: 300 = 5 minutes)
     */
    public function __construct(string $webhookSecret, int $timestampTolerance = 300)
    {
        if (empty($webhookSecret)) {
            throw WebhookException::missingWebhookSecret();
        }

        $this->webhookSecret = $webhookSecret;
        $this->timestampTolerance = $timestampTolerance;
    }

    /**
     * Validate webhook signature and timestamp
     *
     * @param string $payload The raw webhook payload
     * @param string $signature The signature from webhook headers
     * @param int|null $timestamp The timestamp from webhook headers (optional)
     * @return bool True if validation passes
     * @throws WebhookException If validation fails
     */
    public function validate(string $payload, string $signature, ?int $timestamp = null): bool
    {
        // Validate signature is present
        if (empty($signature)) {
            throw WebhookException::missingSignature();
        }

        // Validate timestamp if provided
        if ($timestamp !== null) {
            $this->validateTimestamp($timestamp);
        }

        // Validate signature
        $this->validateSignature($payload, $signature);

        return true;
    }

    /**
     * Validate webhook signature
     *
     * @param string $payload The raw webhook payload
     * @param string $signature The signature from webhook headers
     * @return bool True if signature is valid
     * @throws WebhookException If signature validation fails
     */
    public function validateSignature(string $payload, string $signature): bool
    {
        if (empty($signature)) {
            throw WebhookException::missingSignature();
        }

        $expectedSignature = $this->generateSignature($payload);

        if (!hash_equals($expectedSignature, $signature)) {
            throw WebhookException::invalidSignature($expectedSignature, $signature);
        }

        return true;
    }

    /**
     * Validate webhook timestamp to prevent replay attacks
     *
     * @param int $timestamp The timestamp from webhook headers
     * @return bool True if timestamp is valid
     * @throws WebhookException If timestamp validation fails
     */
    public function validateTimestamp(int $timestamp): bool
    {
        $currentTime = time();
        $timeDifference = abs($currentTime - $timestamp);

        if ($timeDifference > $this->timestampTolerance) {
            throw WebhookException::timestampValidationFailed(
                $timestamp,
                $currentTime,
                $this->timestampTolerance
            );
        }

        return true;
    }

    /**
     * Generate expected signature for payload
     *
     * @param string $payload The raw webhook payload
     * @return string The expected signature
     */
    public function generateSignature(string $payload): string
    {
        // Freemius uses HMAC-SHA256 for webhook signatures
        return hash_hmac('sha256', $payload, $this->webhookSecret);
    }

    /**
     * Verify webhook signature without throwing exceptions
     *
     * @param string $payload The raw webhook payload
     * @param string $signature The signature from webhook headers
     * @return bool True if signature is valid, false otherwise
     */
    public function verifySignature(string $payload, string $signature): bool
    {
        try {
            return $this->validateSignature($payload, $signature);
        } catch (WebhookException $e) {
            return false;
        }
    }

    /**
     * Verify webhook timestamp without throwing exceptions
     *
     * @param int $timestamp The timestamp from webhook headers
     * @return bool True if timestamp is valid, false otherwise
     */
    public function verifyTimestamp(int $timestamp): bool
    {
        try {
            return $this->validateTimestamp($timestamp);
        } catch (WebhookException $e) {
            return false;
        }
    }

    /**
     * Get the current timestamp tolerance
     *
     * @return int Timestamp tolerance in seconds
     */
    public function getTimestampTolerance(): int
    {
        return $this->timestampTolerance;
    }

    /**
     * Set the timestamp tolerance
     *
     * @param int $tolerance Timestamp tolerance in seconds
     * @return self
     */
    public function setTimestampTolerance(int $tolerance): self
    {
        $this->timestampTolerance = $tolerance;
        return $this;
    }

    /**
     * Extract signature from webhook headers
     *
     * Freemius uses HTTP_X_SIGNATURE header for webhook signatures
     *
     * @param array $headers HTTP headers array
     * @return string|null The signature if found
     */
    public static function extractSignature(array $headers): ?string
    {
        // Freemius uses HTTP_X_SIGNATURE header
        $signatureHeaders = [
            'HTTP_X_SIGNATURE',
            'X-Signature',
            'X_SIGNATURE',
            'HTTP-X-SIGNATURE',
        ];

        foreach ($signatureHeaders as $headerName) {
            // Check both original case and lowercase
            $value = $headers[$headerName] ?? $headers[strtolower($headerName)] ?? null;
            
            if ($value !== null) {
                // Handle array values (some HTTP libraries return arrays)
                if (is_array($value)) {
                    $value = $value[0] ?? null;
                }
                
                if (is_string($value) && !empty($value)) {
                    // Freemius sends raw hex signature without prefixes
                    return $value;
                }
            }
        }

        return null;
    }

    /**
     * Extract timestamp from webhook headers
     *
     * Note: Freemius doesn't typically send timestamp headers,
     * but this method is provided for completeness
     *
     * @param array $headers HTTP headers array
     * @return int|null The timestamp if found
     */
    public static function extractTimestamp(array $headers): ?int
    {
        // Freemius doesn't typically send timestamp headers
        // but check common patterns just in case
        $timestampHeaders = [
            'HTTP_X_TIMESTAMP',
            'X-Timestamp',
            'X_TIMESTAMP',
            'Timestamp',
        ];

        foreach ($timestampHeaders as $headerName) {
            // Check both original case and lowercase
            $value = $headers[$headerName] ?? $headers[strtolower($headerName)] ?? null;
            
            if ($value !== null) {
                // Handle array values
                if (is_array($value)) {
                    $value = $value[0] ?? null;
                }
                
                if (is_string($value) || is_numeric($value)) {
                    $timestamp = (int) $value;
                    if ($timestamp > 0) {
                        return $timestamp;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Create validator from headers and validate in one step
     *
     * @param string $webhookSecret The webhook secret
     * @param string $payload The raw webhook payload
     * @param array $headers HTTP headers array
     * @param int $timestampTolerance Timestamp tolerance in seconds
     * @return bool True if validation passes
     * @throws WebhookException If validation fails
     */
    public static function validateFromHeaders(
        string $webhookSecret,
        string $payload,
        array $headers,
        int $timestampTolerance = 300
    ): bool {
        $validator = new self($webhookSecret, $timestampTolerance);
        
        $signature = self::extractSignature($headers);
        if ($signature === null) {
            throw WebhookException::missingSignature();
        }

        $timestamp = self::extractTimestamp($headers);
        
        return $validator->validate($payload, $signature, $timestamp);
    }
}