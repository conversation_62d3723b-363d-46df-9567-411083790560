<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Network exception for connectivity and network-related errors
 * 
 * Thrown when network connectivity issues prevent communication
 * with the Freemius API, such as DNS resolution failures,
 * connection timeouts, or SSL/TLS errors.
 */
class NetworkException extends FreemiusException
{
    /**
     * Create a new network exception
     *
     * @param string $message Exception message
     * @param int $code Exception code
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Network error occurred',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for connection timeout
     *
     * @param int $timeout The timeout value in seconds
     * @param string $host The host that timed out
     * @param array $context Additional context
     * @return self
     */
    public static function connectionTimeout(
        int $timeout = 0,
        string $host = '',
        array $context = []
    ): self {
        $message = 'Connection timeout';
        
        if ($host) {
            $message .= " connecting to {$host}";
        }
        
        if ($timeout > 0) {
            $message .= " after {$timeout} seconds";
        }

        $context = array_merge([
            'timeout' => $timeout,
            'host' => $host,
            'error_type' => 'connection_timeout',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for DNS resolution failure
     *
     * @param string $host The host that failed to resolve
     * @param array $context Additional context
     * @return self
     */
    public static function dnsResolutionFailed(string $host = '', array $context = []): self
    {
        $message = 'DNS resolution failed';
        
        if ($host) {
            $message .= " for {$host}";
        }

        $context = array_merge([
            'host' => $host,
            'error_type' => 'dns_resolution',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for SSL/TLS certificate errors
     *
     * @param string $host The host with certificate issues
     * @param string $sslError Specific SSL error message
     * @param array $context Additional context
     * @return self
     */
    public static function sslCertificateError(
        string $host = '',
        string $sslError = '',
        array $context = []
    ): self {
        $message = 'SSL certificate error';
        
        if ($host) {
            $message .= " for {$host}";
        }
        
        if ($sslError) {
            $message .= ": {$sslError}";
        }

        $context = array_merge([
            'host' => $host,
            'ssl_error' => $sslError,
            'error_type' => 'ssl_certificate',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for connection refused
     *
     * @param string $host The host that refused connection
     * @param int $port The port that was attempted
     * @param array $context Additional context
     * @return self
     */
    public static function connectionRefused(
        string $host = '',
        int $port = 0,
        array $context = []
    ): self {
        $message = 'Connection refused';
        
        if ($host) {
            $message .= " by {$host}";
            
            if ($port > 0) {
                $message .= ":{$port}";
            }
        }

        $context = array_merge([
            'host' => $host,
            'port' => $port,
            'error_type' => 'connection_refused',
        ], $context);

        return new self($message, 0, null, $context);
    }

    /**
     * Create an exception for general connectivity issues
     *
     * @param string $message Custom error message
     * @param array $context Additional context
     * @return self
     */
    public static function connectivityIssue(string $message = '', array $context = []): self
    {
        $defaultMessage = 'Network connectivity issue';
        $finalMessage = $message ?: $defaultMessage;

        $context = array_merge([
            'error_type' => 'connectivity',
        ], $context);

        return new self($finalMessage, 0, null, $context);
    }

    /**
     * Create a network exception from a cURL error
     *
     * @param int $curlError cURL error code
     * @param string $curlMessage cURL error message
     * @param array $context Additional context
     * @return self
     */
    public static function fromCurlError(
        int $curlError,
        string $curlMessage = '',
        array $context = []
    ): self {
        $context = array_merge([
            'curl_error_code' => $curlError,
            'curl_error_message' => $curlMessage,
            'error_type' => 'curl',
        ], $context);

        // Map common cURL errors to specific exception types
        switch ($curlError) {
            case CURLE_OPERATION_TIMEDOUT:
                return self::connectionTimeout(0, '', $context);
            case CURLE_COULDNT_RESOLVE_HOST:
                return self::dnsResolutionFailed('', $context);
            case CURLE_SSL_CERTPROBLEM:
            case CURLE_SSL_CACERT:
            case CURLE_SSL_CONNECT_ERROR:
                return self::sslCertificateError('', $curlMessage, $context);
            case CURLE_COULDNT_CONNECT:
                return self::connectionRefused('', 0, $context);
            default:
                $message = $curlMessage ?: "cURL error {$curlError}";
                return new self($message, $curlError, null, $context);
        }
    }

    /**
     * Check if the error is likely temporary and retryable
     *
     * @return bool True if the error might be temporary
     */
    public function isRetryable(): bool
    {
        $errorType = $this->getContext()['error_type'] ?? '';
        
        $retryableTypes = [
            'connection_timeout',
            'connectivity',
            'connection_refused',
        ];

        return in_array($errorType, $retryableTypes, true);
    }

    /**
     * Get a user-friendly error message
     *
     * @return string User-friendly message
     */
    public function getUserFriendlyMessage(): string
    {
        $errorType = $this->getContext()['error_type'] ?? '';

        switch ($errorType) {
            case 'connection_timeout':
                return 'The connection to Freemius timed out. Please check your internet connection and try again.';
            case 'dns_resolution':
                return 'Unable to connect to Freemius. Please check your internet connection and DNS settings.';
            case 'ssl_certificate':
                return 'There was a security certificate issue connecting to Freemius. Please try again later.';
            case 'connection_refused':
                return 'Connection to Freemius was refused. The service may be temporarily unavailable.';
            default:
                return 'Unable to connect to Freemius. Please check your internet connection and try again.';
        }
    }

    /**
     * Get suggested retry delay in seconds
     *
     * @return int Suggested delay before retrying
     */
    public function getSuggestedRetryDelay(): int
    {
        $errorType = $this->getContext()['error_type'] ?? '';

        switch ($errorType) {
            case 'connection_timeout':
                return 10; // 10 seconds for timeout
            case 'dns_resolution':
                return 30; // 30 seconds for DNS issues
            case 'ssl_certificate':
                return 60; // 1 minute for SSL issues
            case 'connection_refused':
                return 15; // 15 seconds for connection refused
            default:
                return 20; // Default 20 seconds
        }
    }
}