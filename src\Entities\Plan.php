<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Plan entity representing a Freemius pricing plan.
 * 
 * A plan defines the pricing structure, features, and billing cycle
 * for a product in the Freemius marketplace.
 */
class Plan extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'product_id' => 'integer',
        'parent_plan_id' => 'integer',
        'trial_period' => 'integer',
        'annual_discount' => 'integer',
        'max_installs' => 'integer',
        'is_block_features' => 'boolean',
        'is_https_support' => 'boolean',
        'is_success_manager' => 'boolean',
        'is_featured' => 'boolean',
        'is_free' => 'boolean',
        'is_hidden' => 'boolean',
        'is_deleted' => 'boolean',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
    ];

    /**
     * Get the plan ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the product ID.
     */
    public function getProductId(): int
    {
        return $this->getAttribute('product_id');
    }

    /**
     * Get the parent plan ID (for add-on plans).
     */
    public function getParentPlanId(): ?int
    {
        return $this->getAttribute('parent_plan_id');
    }

    /**
     * Get the plan name.
     */
    public function getName(): string
    {
        return $this->getAttribute('name', '');
    }

    /**
     * Get the plan title.
     */
    public function getTitle(): string
    {
        return $this->getAttribute('title', '');
    }

    /**
     * Get the plan description.
     */
    public function getDescription(): string
    {
        return $this->getAttribute('description', '');
    }

    /**
     * Get the pricing model.
     */
    public function getPricingModel(): string
    {
        return $this->getAttribute('pricing_model', 'per-site');
    }

    /**
     * Get the trial period in days.
     */
    public function getTrialPeriod(): int
    {
        return $this->getAttribute('trial_period', 0);
    }

    /**
     * Get the annual discount percentage.
     */
    public function getAnnualDiscount(): int
    {
        return $this->getAttribute('annual_discount', 0);
    }

    /**
     * Get the maximum number of installs allowed.
     */
    public function getMaxInstalls(): ?int
    {
        return $this->getAttribute('max_installs');
    }

    /**
     * Check if features are blocked for this plan.
     */
    public function isBlockFeatures(): bool
    {
        return $this->getAttribute('is_block_features', false);
    }

    /**
     * Check if HTTPS support is included.
     */
    public function isHttpsSupport(): bool
    {
        return $this->getAttribute('is_https_support', false);
    }

    /**
     * Check if success manager is included.
     */
    public function isSuccessManager(): bool
    {
        return $this->getAttribute('is_success_manager', false);
    }

    /**
     * Check if the plan is featured.
     */
    public function isFeatured(): bool
    {
        return $this->getAttribute('is_featured', false);
    }

    /**
     * Check if the plan is free.
     */
    public function isFree(): bool
    {
        return $this->getAttribute('is_free', false);
    }

    /**
     * Check if the plan is hidden.
     */
    public function isHidden(): bool
    {
        return $this->getAttribute('is_hidden', false);
    }

    /**
     * Check if the plan is deleted.
     */
    public function isDeleted(): bool
    {
        return $this->getAttribute('is_deleted', false);
    }

    /**
     * Check if the plan has unlimited installs.
     */
    public function hasUnlimitedInstalls(): bool
    {
        return $this->getMaxInstalls() === null || $this->getMaxInstalls() === 0;
    }

    /**
     * Check if the plan has a trial period.
     */
    public function hasTrial(): bool
    {
        return $this->getTrialPeriod() > 0;
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }
}