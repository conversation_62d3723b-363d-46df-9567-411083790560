<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Tag;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Tags resource for managing Freemius product tags/versions.
 * 
 * Handles CRUD operations and specialized methods for tag management.
 */
class Tags extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'tags';
    }
    
    protected function getEntityClass(): string
    {
        return Tag::class;
    }
    
    /**
     * Get released tags only.
     */
    public function released(): self
    {
        return $this->where('release_mode', 'released');
    }
    
    /**
     * Get pending tags only.
     */
    public function pending(): self
    {
        return $this->where('release_mode', 'pending');
    }
    
    /**
     * Get beta tags only.
     */
    public function beta(): self
    {
        return $this->where('release_mode', 'beta');
    }
    
    /**
     * Get tags with free version.
     */
    public function withFree(): self
    {
        return $this->where('has_free', true);
    }
    
    /**
     * Get tags with premium version.
     */
    public function withPremium(): self
    {
        return $this->where('has_premium', true);
    }
    
    /**
     * Get incremental tags only.
     */
    public function incremental(): self
    {
        return $this->where('is_incremental', true);
    }
    
    /**
     * Get tags by developer ID.
     */
    public function byDeveloper(int $developerId): self
    {
        return $this->where('developer_id', $developerId);
    }
    
    /**
     * Get tags by version.
     */
    public function byVersion(string $version): self
    {
        return $this->where('version', $version);
    }
    
    /**
     * Get tags created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get tags created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Search tags by version.
     */
    public function searchByVersion(string $version): self
    {
        return $this->where('version', 'like', '%' . $version . '%');
    }
    
    /**
     * Get the latest tag.
     */
    public function latest(): ?Tag
    {
        $collection = $this->orderBy('created', 'desc')->limit(1)->all();
        return $collection->first();
    }
    
    /**
     * Get the latest released tag.
     */
    public function latestReleased(): ?Tag
    {
        $collection = $this->released()
                          ->orderBy('created', 'desc')
                          ->limit(1)
                          ->all();
        return $collection->first();
    }
    
    /**
     * Release a tag.
     */
    public function release(int $tagId, array $releaseData = [])
    {
        $releaseData['release_mode'] = 'released';
        $endpoint = $this->buildEndpoint($tagId);
        $response = $this->httpClient->put($endpoint, $releaseData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Tag($data);
    }
    
    /**
     * Set tag to beta.
     */
    public function setBeta(int $tagId)
    {
        $endpoint = $this->buildEndpoint($tagId);
        $response = $this->httpClient->put($endpoint, ['release_mode' => 'beta']);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Tag($data);
    }
    
    /**
     * Set tag to pending.
     */
    public function setPending(int $tagId)
    {
        $endpoint = $this->buildEndpoint($tagId);
        $response = $this->httpClient->put($endpoint, ['release_mode' => 'pending']);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Tag($data);
    }
    
    /**
     * Get tag download URL.
     */
    public function getDownloadUrl(int $tagId, bool $isPremium = false): string
    {
        $endpoint = $this->buildEndpoint($tagId) . '.zip';
        if ($isPremium) {
            $endpoint .= '?is_premium=true';
        }
        
        // This returns the download URL, not the file content
        return $this->httpClient->getBaseUri() . '/' . ltrim($endpoint, '/');
    }
    
    /**
     * Get latest tag info with download link.
     */
    public function getLatestInfo(): array
    {
        $endpoint = $this->buildEndpoint() . '/latest';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['version'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a tag");
            }
        }
        
        // Validate numeric fields
        $numericFields = ['plugin_id', 'developer_id', 'downloaded', 'uniques', 'limit'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate version format (basic semantic versioning)
        if (isset($data['version']) && !preg_match('/^\d+\.\d+(\.\d+)?/', $data['version'])) {
            throw new ValidationException("Version must follow semantic versioning format (e.g., 1.0.0)");
        }
        
        // Validate release mode
        if (isset($data['release_mode'])) {
            $validModes = ['released', 'pending', 'beta'];
            if (!in_array($data['release_mode'], $validModes)) {
                throw new ValidationException("Release mode must be one of: " . implode(', ', $validModes));
            }
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        $numericFields = ['plugin_id', 'developer_id', 'downloaded', 'uniques', 'limit'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate version format if provided
        if (isset($data['version']) && !preg_match('/^\d+\.\d+(\.\d+)?/', $data['version'])) {
            throw new ValidationException("Version must follow semantic versioning format (e.g., 1.0.0)");
        }
        
        // Validate release mode if provided
        if (isset($data['release_mode'])) {
            $validModes = ['released', 'pending', 'beta'];
            if (!in_array($data['release_mode'], $validModes)) {
                throw new ValidationException("Release mode must be one of: " . implode(', ', $validModes));
            }
        }
    }
}