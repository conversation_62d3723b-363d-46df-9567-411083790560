<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

use Freemius\SDK\Http\HttpClientInterface;
use Freemius\SDK\Resources\AbstractResource;
use Closure;

/**
 * Paginated collection class for handling automatic pagination.
 * 
 * Provides automatic fetching of additional pages and memory-efficient
 * processing of large datasets from the Freemius API.
 */
class PaginatedCollection extends Collection
{
    private ?HttpClientInterface $httpClient;
    private ?AbstractResource $resource;
    private QueryBuilder $queryBuilder;
    private string $endpoint;
    private string $entityClass;
    private bool $autoFetch;
    private int $maxPages;
    private int $currentPageNumber;
    
    public function __construct(
        array $items = [],
        array $pagination = [],
        ?HttpClientInterface $httpClient = null,
        ?AbstractResource $resource = null,
        ?QueryBuilder $queryBuilder = null,
        string $endpoint = '',
        string $entityClass = '',
        bool $autoFetch = false,
        int $maxPages = 100
    ) {
        parent::__construct($items, $pagination);
        
        $this->httpClient = $httpClient;
        $this->resource = $resource;
        $this->queryBuilder = $queryBuilder ?? new QueryBuilder();
        $this->endpoint = $endpoint;
        $this->entityClass = $entityClass;
        $this->autoFetch = $autoFetch;
        $this->maxPages = $maxPages;
        $this->currentPageNumber = $this->calculateCurrentPage();
    }
    
    /**
     * Get the next page of results.
     */
    public function nextPage(): ?self
    {
        if (!$this->hasNextPage() || !$this->canFetchMore()) {
            return null;
        }
        
        $nextOffset = $this->getNextOffset();
        if ($nextOffset === null) {
            return null;
        }
        
        return $this->fetchPage($nextOffset);
    }
    
    /**
     * Get the previous page of results.
     */
    public function previousPage(): ?self
    {
        if (!$this->hasPreviousPage() || !$this->canFetchMore()) {
            return null;
        }
        
        $previousOffset = $this->getPreviousOffset();
        if ($previousOffset === null) {
            return null;
        }
        
        return $this->fetchPage($previousOffset);
    }
    
    /**
     * Fetch all remaining pages and return a combined collection.
     */
    public function fetchAll(): self
    {
        $allItems = $this->all();
        $currentCollection = $this;
        $pageCount = 1;
        
        while ($currentCollection->hasNextPage() && $pageCount < $this->maxPages) {
            $currentCollection = $currentCollection->nextPage();
            if ($currentCollection === null) {
                break;
            }
            
            $allItems = array_merge($allItems, $currentCollection->all());
            $pageCount++;
        }
        
        return new static($allItems, $this->pagination, $this->httpClient, $this->resource, 
                         $this->queryBuilder, $this->endpoint, $this->entityClass, false);
    }
    
    /**
     * Process items in chunks to manage memory efficiently.
     */
    public function chunk(int $size, Closure $callback): void
    {
        $currentCollection = $this;
        $pageCount = 1;
        
        do {
            $items = $currentCollection->all();
            $chunks = array_chunk($items, $size);
            
            foreach ($chunks as $chunk) {
                $callback(new Collection($chunk));
            }
            
            if (!$currentCollection->hasNextPage() || $pageCount >= $this->maxPages) {
                break;
            }
            
            $currentCollection = $currentCollection->nextPage();
            $pageCount++;
        } while ($currentCollection !== null);
    }
    
    /**
     * Iterate through all pages automatically.
     */
    public function each(Closure $callback): void
    {
        $currentCollection = $this;
        $pageCount = 1;
        
        do {
            foreach ($currentCollection->all() as $index => $item) {
                $callback($item, $index);
            }
            
            if (!$currentCollection->hasNextPage() || $pageCount >= $this->maxPages) {
                break;
            }
            
            $currentCollection = $currentCollection->nextPage();
            $pageCount++;
        } while ($currentCollection !== null);
    }
    
    /**
     * Fetch a specific page by offset.
     */
    private function fetchPage(int $offset): ?self
    {
        if (!$this->httpClient || !$this->resource) {
            return null;
        }
        
        try {
            $queryBuilder = $this->queryBuilder->clone();
            $queryBuilder->offset($offset);
            
            $response = $this->httpClient->get($this->endpoint, $queryBuilder->toArray());
            
            if (!isset($response['data'])) {
                return null;
            }
            
            $items = [];
            foreach ($response['data'] as $itemData) {
                if ($this->entityClass && class_exists($this->entityClass)) {
                    $items[] = new $this->entityClass($itemData);
                } else {
                    $items[] = $itemData;
                }
            }
            
            $pagination = $response['pagination'] ?? [];
            
            return new static($items, $pagination, $this->httpClient, $this->resource,
                             $this->queryBuilder, $this->endpoint, $this->entityClass, $this->autoFetch);
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Calculate current page number.
     */
    private function calculateCurrentPage(): int
    {
        $offset = $this->queryBuilder->getOffset() ?? 0;
        $count = $this->queryBuilder->getCount() ?? 25;
        
        return (int) floor($offset / $count) + 1;
    }
    
    /**
     * Get the next offset for pagination.
     */
    private function getNextOffset(): ?int
    {
        if (!$this->hasNextPage()) {
            return null;
        }
        
        $currentOffset = $this->queryBuilder->getOffset() ?? 0;
        $count = $this->queryBuilder->getCount() ?? 25;
        
        return $currentOffset + $count;
    }
    
    /**
     * Get the previous offset for pagination.
     */
    private function getPreviousOffset(): ?int
    {
        $currentOffset = $this->queryBuilder->getOffset() ?? 0;
        $count = $this->queryBuilder->getCount() ?? 25;
        
        $previousOffset = $currentOffset - $count;
        
        return $previousOffset >= 0 ? $previousOffset : null;
    }
    
    /**
     * Check if we can fetch more pages.
     */
    private function canFetchMore(): bool
    {
        return $this->httpClient !== null && 
               $this->resource !== null && 
               !empty($this->endpoint) &&
               $this->currentPageNumber < $this->maxPages;
    }
    
    /**
     * Get current page number.
     */
    public function getCurrentPage(): int
    {
        return $this->currentPageNumber;
    }
    
    /**
     * Get maximum pages allowed.
     */
    public function getMaxPages(): int
    {
        return $this->maxPages;
    }
    
    /**
     * Set maximum pages allowed.
     */
    public function setMaxPages(int $maxPages): self
    {
        $this->maxPages = $maxPages;
        return $this;
    }
    
    /**
     * Check if auto-fetch is enabled.
     */
    public function isAutoFetch(): bool
    {
        return $this->autoFetch;
    }
    
    /**
     * Enable or disable auto-fetch.
     */
    public function setAutoFetch(bool $autoFetch): self
    {
        $this->autoFetch = $autoFetch;
        return $this;
    }
    
    /**
     * Get items from all pages as a generator for memory efficiency.
     */
    public function lazy(): \Generator
    {
        $currentCollection = $this;
        
        do {
            foreach ($currentCollection->all() as $item) {
                yield $item;
            }
            
            $currentCollection = $currentCollection->nextPage();
        } while ($currentCollection !== null && $this->canFetchMore());
    }
    
    /**
     * Iterate through all pages lazily.
     */
    public function eachPage(Closure $callback): void
    {
        $currentCollection = $this;
        $pageNumber = 1;
        
        do {
            $callback($currentCollection, $pageNumber);
            $currentCollection = $currentCollection->nextPage();
            $pageNumber++;
        } while ($currentCollection !== null && $this->canFetchMore());
    }
    
    /**
     * Get the total number of items across all pages.
     */
    public function getTotalCount(): ?int
    {
        // Try to get from pagination metadata
        if (isset($this->pagination['total'])) {
            return (int) $this->pagination['total'];
        }
        
        return null; // Cannot determine total without metadata
    }
    
    /**
     * Check if there are more items beyond the current collection.
     */
    public function hasMoreItems(): bool
    {
        $total = $this->getTotalCount();
        if ($total !== null) {
            $currentOffset = $this->queryBuilder->getOffset() ?? 0;
            return ($currentOffset + $this->count()) < $total;
        }
        
        // If we can't determine total, check if current page is full
        $perPage = $this->queryBuilder->getCount() ?? 25;
        return $this->count() >= $perPage;
    }
}
