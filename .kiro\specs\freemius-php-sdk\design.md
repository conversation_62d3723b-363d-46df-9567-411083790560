# Design Document

## Overview

The Freemius PHP SDK is a modernized, production-grade PHP library that provides seamless integration with the Freemius REST API. The SDK enables developers who use Freemius for licensing their WordPress products to build custom SaaS platforms, reporting tools, and integrations by accessing their product and user data programmatically.

The SDK follows a clean, object-oriented architecture with method chaining support, comprehensive error handling, and strong type safety. It covers all available Freemius REST API endpoints and provides specialized tools for webhook processing.

## Architecture

### Core Components

```
FreemiusSDK/
├── Client/
│   ├── FreemiusClient.php          # Main API client
│   ├── Authentication/
│   │   └── BearerAuth.php          # Bearer token authentication
│   └── Http/
│       ├── HttpClient.php          # HTTP client wrapper
│       └── RequestBuilder.php      # Request building utilities
├── Resources/
│   ├── Products.php                # Product operations
│   ├── Users.php                   # User operations
│   ├── Licenses.php                # License operations
│   ├── Subscriptions.php           # Subscription operations
│   ├── Payments.php                # Payment operations
│   ├── Installations.php           # Installation operations
│   ├── Carts.php                   # Cart operations
│   ├── Coupons.php                 # Coupon operations
│   ├── Plans.php                   # Plan operations
│   ├── Addons.php                  # Addon operations
│   ├── Deployments.php             # Deployment operations
│   └── Trials.php                  # Trial operations
├── Entities/
│   ├── Product.php                 # Product entity
│   ├── User.php                    # User entity
│   ├── License.php                 # License entity
│   ├── Subscription.php            # Subscription entity
│   ├── Payment.php                 # Payment entity
│   ├── Installation.php            # Installation entity
│   └── [Other entities...]
├── Webhooks/
│   ├── WebhookHandler.php          # Webhook processing
│   ├── WebhookValidator.php        # Signature validation
│   └── Events/
│       ├── InstallActivated.php    # Install activation event
│       ├── SubscriptionUpdated.php # Subscription update event
│       └── [Other events...]
├── Exceptions/
│   ├── FreemiusException.php       # Base exception
│   ├── AuthenticationException.php # Auth errors
│   ├── ValidationException.php     # Validation errors
│   ├── NotFoundException.php       # 404 errors
│   └── RateLimitException.php      # Rate limiting errors
├── Support/
│   ├── Configuration.php           # SDK configuration
│   ├── Logger.php                  # Logging utilities
│   └── Validator.php               # Data validation
└── FreemiusSDK.php                 # Main SDK facade
```

### Design Patterns

1. **Facade Pattern**: `FreemiusSDK` class provides a simple interface to all SDK functionality
2. **Resource Pattern**: Each API resource (Products, Users, etc.) is encapsulated in its own class
3. **Entity Pattern**: API responses are mapped to strongly-typed entity objects
4. **Builder Pattern**: Request building with method chaining support
5. **Strategy Pattern**: Different authentication strategies (Bearer token, API keys)

## Components and Interfaces

### 1. Main SDK Facade

```php
class FreemiusSDK
{
    public function __construct(Configuration $config);
    public function products(): Products;
    public function users(): Users;
    public function licenses(): Licenses;
    public function subscriptions(): Subscriptions;
    public function payments(): Payments;
    public function installations(): Installations;
    public function webhooks(): WebhookHandler;
    public function setProductScope(int $productId): self;
}
```

### 2. HTTP Client Layer

```php
interface HttpClientInterface
{
    public function get(string $uri, array $params = []): ResponseInterface;
    public function post(string $uri, array $data = []): ResponseInterface;
    public function put(string $uri, array $data = []): ResponseInterface;
    public function delete(string $uri): ResponseInterface;
}

class HttpClient implements HttpClientInterface
{
    private GuzzleHttp\Client $client;
    private AuthenticationInterface $auth;
    private LoggerInterface $logger;
}
```

### 3. Authentication System

```php
interface AuthenticationInterface
{
    public function authenticate(RequestInterface $request): RequestInterface;
}

class BearerAuth implements AuthenticationInterface
{
    public function __construct(string $bearerToken);
    public function authenticate(RequestInterface $request): RequestInterface;
}
```

### 4. Resource Classes

Each resource class follows this pattern:

```php
class Products extends AbstractResource
{
    public function get(int $productId): Product;
    public function list(array $params = []): Collection;
    public function update(int $productId, array $data): Product;
    
    // Nested resources
    public function users(int $productId): Users;
    public function licenses(int $productId): Licenses;
    public function installations(int $productId): Installations;
    
    // Method chaining support
    public function where(string $field, $value): self;
    public function limit(int $count): self;
    public function offset(int $offset): self;
}
```

### 5. Entity System

```php
abstract class AbstractEntity
{
    protected array $attributes = [];
    
    public function __construct(array $data);
    public function getAttribute(string $key, $default = null);
    public function setAttribute(string $key, $value): self;
    public function toArray(): array;
    public function toJson(): string;
}

class Product extends AbstractEntity
{
    public function getId(): int;
    public function getTitle(): string;
    public function getSlug(): string;
    public function getType(): string;
    public function isActive(): bool;
    public function getCreatedAt(): DateTime;
    public function getUpdatedAt(): DateTime;
}
```

### 6. Webhook System

```php
class WebhookHandler
{
    public function __construct(string $webhookSecret);
    public function handle(string $payload, string $signature): WebhookEvent;
    public function validateSignature(string $payload, string $signature): bool;
}

abstract class WebhookEvent
{
    protected array $data;
    
    public function __construct(array $data);
    public function getEventType(): string;
    public function getTimestamp(): DateTime;
    public function getData(): array;
}

class InstallActivated extends WebhookEvent
{
    public function getInstall(): Installation;
    public function getUser(): User;
    public function getLicense(): ?License;
}
```

## Data Models

### Core Entities

Based on the Freemius OpenAPI specification, the main entities are:

1. **Product** - WordPress plugins, themes, or add-ons
2. **User** - Customers who use the products
3. **Installation** - Product installations on specific sites
4. **License** - License keys for paid products
5. **Subscription** - Recurring billing subscriptions
6. **Payment** - Individual payment transactions
7. **Plan** - Pricing plans for products
8. **Addon** - Add-on products
9. **Cart** - Shopping cart for checkout process
10. **Coupon** - Discount coupons
11. **Deployment** - Product version deployments
12. **Trial** - Free trial periods

### Entity Relationships

```
Product (1) ──── (N) User
Product (1) ──── (N) Installation
Product (1) ──── (N) License
Product (1) ──── (N) Plan
Product (1) ──── (N) Addon

User (1) ──── (N) Installation
User (1) ──── (N) License
User (1) ──── (N) Payment

License (1) ──── (N) Subscription
License (1) ──── (N) Installation

Plan (1) ──── (N) License
Plan (1) ──── (N) Pricing
```

### Data Validation

Each entity implements validation based on Freemius API schemas:

```php
class ProductValidator
{
    public function validate(array $data): array;
    public function validateForCreation(array $data): array;
    public function validateForUpdate(array $data): array;
}
```

## Error Handling

### Exception Hierarchy

```php
FreemiusException (base)
├── AuthenticationException     # 401 errors
├── AuthorizationException      # 403 errors  
├── NotFoundException          # 404 errors
├── ValidationException        # 400 validation errors
├── RateLimitException        # 429 rate limiting
├── ServerException           # 5xx server errors
└── NetworkException          # Network/connectivity issues
```

### Error Response Mapping

The SDK maps Freemius API error responses to appropriate exceptions:

```php
class ErrorHandler
{
    public function handleResponse(ResponseInterface $response): void
    {
        $statusCode = $response->getStatusCode();
        $body = json_decode($response->getBody(), true);
        
        switch ($statusCode) {
            case 400:
                throw new ValidationException($body['error']['message'], $body);
            case 401:
                throw new AuthenticationException($body['error']['message']);
            case 404:
                throw new NotFoundException($body['error']['message']);
            case 429:
                throw new RateLimitException($body['error']['message'], $response->getHeader('Retry-After'));
            // ... other cases
        }
    }
}
```

## Testing Strategy

### Unit Testing

- **Resource Classes**: Mock HTTP responses and test method behavior
- **Entity Classes**: Test data mapping and validation
- **Authentication**: Test token handling and request signing
- **Webhook Processing**: Test signature validation and event parsing

### Integration Testing

- **Sandbox API**: Use Freemius mock endpoints for integration tests
- **Real API Scenarios**: Test common workflows with sandbox data
- **Error Handling**: Test API error responses and exception mapping

### Test Structure

```php
tests/
├── Unit/
│   ├── Resources/
│   ├── Entities/
│   ├── Authentication/
│   └── Webhooks/
├── Integration/
│   ├── ProductsTest.php
│   ├── LicensesTest.php
│   └── WebhooksTest.php
└── Fixtures/
    ├── api_responses/
    └── webhook_payloads/
```

### Mock Data

Test fixtures based on actual Freemius API response formats:

```php
class ApiResponseFixtures
{
    public static function product(): array;
    public static function productCollection(): array;
    public static function license(): array;
    public static function webhookPayload(string $eventType): array;
}
```

## Configuration Management

### Configuration Options

```php
class Configuration
{
    private string $bearerToken;
    private string $baseUrl = 'https://api.freemius.com/v1/';
    private bool $sandbox = false;
    private int $timeout = 30;
    private int $retryAttempts = 3;
    private bool $logging = false;
    private string $logLevel = 'info';
    
    public function __construct(array $options = []);
    public static function fromEnvironment(): self;
    public function useSandbox(bool $sandbox = true): self;
    public function setTimeout(int $seconds): self;
    public function enableLogging(string $level = 'info'): self;
}
```

### Environment Variables

```
FREEMIUS_BEARER_TOKEN=your_bearer_token
FREEMIUS_SANDBOX=true
FREEMIUS_TIMEOUT=30
FREEMIUS_LOG_LEVEL=debug
```

## Logging and Monitoring

### Logging Integration

```php
class Logger
{
    private LoggerInterface $logger;
    
    public function logRequest(RequestInterface $request): void;
    public function logResponse(ResponseInterface $response): void;
    public function logError(Exception $exception): void;
}
```

### Request/Response Logging

- Log all API requests and responses (with sensitive data redacted)
- Include timing information for performance monitoring
- Support different log levels (debug, info, warning, error)

## Security Considerations

### Authentication Security

- Bearer tokens are never logged or exposed in error messages
- Support for token rotation and refresh
- Secure storage recommendations in documentation

### Webhook Security

- Mandatory signature validation for all webhook payloads
- Protection against replay attacks with timestamp validation
- Secure webhook secret management

### Data Protection

- Automatic redaction of sensitive data in logs
- No storage of authentication credentials in memory longer than necessary
- GDPR compliance considerations for user data handling

## Performance Optimization

### HTTP Client Optimization

- Connection pooling and keep-alive
- Gzip compression support
- Configurable timeouts and retry policies

### Caching Strategy

- Optional response caching for read-only operations
- Cache invalidation on write operations
- PSR-6 cache interface support

### Rate Limiting

- Automatic rate limit detection and backoff
- Configurable retry strategies
- Rate limit status exposure to applications

## Method Chaining Implementation

### Fluent Interface Design

```php
$users = $freemius->products()
    ->setScope(12345)
    ->users()
    ->where('email', 'like', '%@example.com')
    ->limit(50)
    ->offset(100)
    ->get();

$license = $freemius->licenses()
    ->find(67890)
    ->activate($installId)
    ->sendWelcomeEmail();
```

### Query Builder Pattern

```php
class QueryBuilder
{
    private array $wheres = [];
    private ?int $limit = null;
    private ?int $offset = null;
    
    public function where(string $field, string $operator, $value): self;
    public function limit(int $count): self;
    public function offset(int $offset): self;
    public function toArray(): array;
}
```

This design provides a solid foundation for implementing a production-grade Freemius PHP SDK that covers all API operations while maintaining clean architecture, strong type safety, and excellent developer experience.