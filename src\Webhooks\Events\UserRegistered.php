<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\User;

/**
 * User registration webhook event
 * 
 * Triggered when a user is registered, updated, or deleted.
 */
class UserRegistered extends WebhookEvent
{
    private ?User $user = null;

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Check if this is a user registration
     *
     * @return bool True if user was registered
     */
    public function isRegistration(): bool
    {
        return $this->eventType === 'user.registered';
    }

    /**
     * Check if this is a user update
     *
     * @return bool True if user was updated
     */
    public function isUpdate(): bool
    {
        return $this->eventType === 'user.updated';
    }

    /**
     * Check if this is a user deletion
     *
     * @return bool True if user was deleted
     */
    public function isDeletion(): bool
    {
        return $this->eventType === 'user.deleted';
    }

    /**
     * Get the user email
     *
     * @return string|null The user email
     */
    public function getUserEmail(): ?string
    {
        return $this->getNestedData('user.email') ?? $this->get('email');
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'user.registered' => 'registered',
            'user.updated' => 'updated',
            'user.deleted' => 'deleted',
            default => 'changed'
        };

        $parts = ["User {$action}"];

        if ($userId = $this->getUserId()) {
            $parts[] = "user:{$userId}";
        }

        if ($email = $this->getUserEmail()) {
            $parts[] = "email:{$email}";
        }

        return implode(' ', $parts);
    }
}