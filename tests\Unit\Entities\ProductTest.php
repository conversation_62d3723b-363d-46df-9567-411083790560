<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Entities;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Entities\Product;
use DateTime;

/**
 * Unit tests for Product entity
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Entities\Product::class)]
class ProductTest extends TestCase
{
    private Product $product;

    protected function setUp(): void
    {
        $this->product = new Product();
    }

    public function testGetId(): void
    {
        $this->product->setAttribute('id', '123');
        $this->assertEquals(123, $this->product->getId());
    }

    public function testGetTitle(): void
    {
        $this->product->setAttribute('title', 'My WordPress Plugin');
        $this->assertEquals('My WordPress Plugin', $this->product->getTitle());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals('', $emptyProduct->getTitle());
    }

    public function testGetSlug(): void
    {
        $this->product->setAttribute('slug', 'my-wordpress-plugin');
        $this->assertEquals('my-wordpress-plugin', $this->product->getSlug());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals('', $emptyProduct->getSlug());
    }

    public function testGetType(): void
    {
        $this->product->setAttribute('type', 'theme');
        $this->assertEquals('theme', $this->product->getType());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals('plugin', $emptyProduct->getType());
    }

    public function testGetDeveloperId(): void
    {
        $this->product->setAttribute('developer_id', '456');
        $this->assertEquals(456, $this->product->getDeveloperId());
    }

    public function testGetStoreId(): void
    {
        $this->product->setAttribute('store_id', '789');
        $this->assertEquals(789, $this->product->getStoreId());
    }

    public function testGetParentPluginId(): void
    {
        $this->product->setAttribute('parent_plugin_id', '100');
        $this->assertEquals(100, $this->product->getParentPluginId());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getParentPluginId());
    }

    public function testIsAddon(): void
    {
        // Test with parent plugin ID (is addon)
        $this->product->setAttribute('parent_plugin_id', '100');
        $this->assertTrue($this->product->isAddon());
        
        // Test without parent plugin ID (not addon)
        $standaloneProduct = new Product();
        $this->assertFalse($standaloneProduct->isAddon());
    }

    public function testGetIcon(): void
    {
        $iconUrl = 'https://example.com/icon.png';
        $this->product->setAttribute('icon', $iconUrl);
        $this->assertEquals($iconUrl, $this->product->getIcon());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getIcon());
    }

    public function testGetDefaultPlanId(): void
    {
        $this->product->setAttribute('default_plan_id', '200');
        $this->assertEquals(200, $this->product->getDefaultPlanId());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getDefaultPlanId());
    }

    public function testGetPlans(): void
    {
        $this->product->setAttribute('plans', '1,2,3,4');
        $expected = [1, 2, 3, 4];
        $this->assertEquals($expected, $this->product->getPlans());
        
        // Test empty plans
        $emptyProduct = new Product();
        $this->assertEquals([], $emptyProduct->getPlans());
        
        // Test single plan
        $this->product->setAttribute('plans', '5');
        $this->assertEquals([5], $this->product->getPlans());
    }

    public function testGetFeatures(): void
    {
        $this->product->setAttribute('features', '10,20,30');
        $expected = [10, 20, 30];
        $this->assertEquals($expected, $this->product->getFeatures());
        
        // Test empty features
        $emptyProduct = new Product();
        $this->assertEquals([], $emptyProduct->getFeatures());
    }

    public function testGetMoneyBackPeriod(): void
    {
        $this->product->setAttribute('money_back_period', '30');
        $this->assertEquals(30, $this->product->getMoneyBackPeriod());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals(0, $emptyProduct->getMoneyBackPeriod());
    }

    public function testGetRefundPolicy(): void
    {
        $this->product->setAttribute('refund_policy', 'strict');
        $this->assertEquals('strict', $this->product->getRefundPolicy());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals('flexible', $emptyProduct->getRefundPolicy());
    }

    public function testGetAnnualRenewalsDiscount(): void
    {
        $this->product->setAttribute('annual_renewals_discount', '20');
        $this->assertEquals(20, $this->product->getAnnualRenewalsDiscount());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getAnnualRenewalsDiscount());
    }

    public function testGetRenewalsDiscountType(): void
    {
        $this->product->setAttribute('renewals_discount_type', 'fixed');
        $this->assertEquals('fixed', $this->product->getRenewalsDiscountType());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals('percentage', $emptyProduct->getRenewalsDiscountType());
    }

    public function testBooleanFlags(): void
    {
        // Test isReleased
        $this->product->setAttribute('is_released', 'false');
        $this->assertFalse($this->product->isReleased());
        
        $emptyProduct = new Product();
        $this->assertTrue($emptyProduct->isReleased()); // Default true
        
        // Test isSdkRequired
        $this->product->setAttribute('is_sdk_required', 'false');
        $this->assertFalse($this->product->isSdkRequired());
        
        $this->assertTrue($emptyProduct->isSdkRequired()); // Default true
        
        // Test isPricingVisible
        $this->product->setAttribute('is_pricing_visible', 'false');
        $this->assertFalse($this->product->isPricingVisible());
        
        $this->assertTrue($emptyProduct->isPricingVisible()); // Default true
        
        // Test isWpOrgCompliant
        $this->product->setAttribute('is_wp_org_compliant', 'false');
        $this->assertFalse($this->product->isWpOrgCompliant());
        
        $this->assertTrue($emptyProduct->isWpOrgCompliant()); // Default true
        
        // Test isStatic
        $this->product->setAttribute('is_static', 'true');
        $this->assertTrue($this->product->isStatic());
        
        $this->assertFalse($emptyProduct->isStatic()); // Default false
    }

    public function testCountFields(): void
    {
        $counts = [
            'installs_count' => 1000,
            'active_installs_count' => 800,
            'free_releases_count' => 5,
            'premium_releases_count' => 10,
            'total_purchases' => 500,
            'total_subscriptions' => 300,
            'total_renewals' => 200,
            'total_failed_purchases' => 25,
        ];

        foreach ($counts as $field => $value) {
            $this->product->setAttribute($field, (string) $value);
        }

        $this->assertEquals(1000, $this->product->getInstallsCount());
        $this->assertEquals(800, $this->product->getActiveInstallsCount());
        $this->assertEquals(5, $this->product->getFreeReleasesCount());
        $this->assertEquals(10, $this->product->getPremiumReleasesCount());
        $this->assertEquals(500, $this->product->getTotalPurchases());
        $this->assertEquals(300, $this->product->getTotalSubscriptions());
        $this->assertEquals(200, $this->product->getTotalRenewals());
        $this->assertEquals(25, $this->product->getTotalFailedPurchases());

        // Test default values
        $emptyProduct = new Product();
        $this->assertEquals(0, $emptyProduct->getInstallsCount());
        $this->assertEquals(0, $emptyProduct->getActiveInstallsCount());
        $this->assertEquals(0, $emptyProduct->getFreeReleasesCount());
        $this->assertEquals(0, $emptyProduct->getPremiumReleasesCount());
        $this->assertEquals(0, $emptyProduct->getTotalPurchases());
        $this->assertEquals(0, $emptyProduct->getTotalSubscriptions());
        $this->assertEquals(0, $emptyProduct->getTotalRenewals());
        $this->assertEquals(0, $emptyProduct->getTotalFailedPurchases());
    }

    public function testGetEarnings(): void
    {
        $this->product->setAttribute('earnings', '1234.56');
        $this->assertEquals(1234.56, $this->product->getEarnings());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals(0.0, $emptyProduct->getEarnings());
    }

    public function testGetEnvironment(): void
    {
        $this->product->setAttribute('environment', '1');
        $this->assertEquals(1, $this->product->getEnvironment());
        
        // Test default value
        $emptyProduct = new Product();
        $this->assertEquals(0, $emptyProduct->getEnvironment());
    }

    public function testIsSandbox(): void
    {
        // Test sandbox environment
        $this->product->setAttribute('environment', '1');
        $this->assertTrue($this->product->isSandbox());
        
        // Test production environment
        $this->product->setAttribute('environment', '0');
        $this->assertFalse($this->product->isSandbox());
        
        // Test default (production)
        $emptyProduct = new Product();
        $this->assertFalse($emptyProduct->isSandbox());
    }

    public function testGetCreatedAt(): void
    {
        $dateString = '2023-01-15 10:30:00';
        $this->product->setAttribute('created', $dateString);
        
        $result = $this->product->getCreatedAt();
        $this->assertInstanceOf(DateTime::class, $result);
    }

    public function testGetUpdatedAt(): void
    {
        $dateString = '2023-06-15 14:45:00';
        $this->product->setAttribute('updated', $dateString);
        
        $result = $this->product->getUpdatedAt();
        $this->assertInstanceOf(DateTime::class, $result);
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getUpdatedAt());
    }

    public function testGetSecretKey(): void
    {
        $secretKey = 'sk_test_123456789';
        $this->product->setAttribute('secret_key', $secretKey);
        $this->assertEquals($secretKey, $this->product->getSecretKey());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getSecretKey());
    }

    public function testGetPublicKey(): void
    {
        $publicKey = 'pk_test_987654321';
        $this->product->setAttribute('public_key', $publicKey);
        $this->assertEquals($publicKey, $this->product->getPublicKey());
        
        // Test null value
        $emptyProduct = new Product();
        $this->assertNull($emptyProduct->getPublicKey());
    }

    public function testCompleteProductData(): void
    {
        $productData = [
            'id' => '12345',
            'title' => 'Awesome WordPress Plugin',
            'slug' => 'awesome-wp-plugin',
            'type' => 'plugin',
            'developer_id' => '67890',
            'store_id' => '11111',
            'parent_plugin_id' => null,
            'icon' => 'https://example.com/icon.png',
            'default_plan_id' => '22222',
            'plans' => '1,2,3',
            'features' => '10,20,30',
            'money_back_period' => '30',
            'refund_policy' => 'flexible',
            'annual_renewals_discount' => '15',
            'renewals_discount_type' => 'percentage',
            'is_released' => 'true',
            'is_sdk_required' => 'true',
            'is_pricing_visible' => 'true',
            'is_wp_org_compliant' => 'true',
            'installs_count' => '5000',
            'active_installs_count' => '4500',
            'free_releases_count' => '8',
            'premium_releases_count' => '12',
            'total_purchases' => '1200',
            'total_subscriptions' => '800',
            'total_renewals' => '600',
            'total_failed_purchases' => '50',
            'earnings' => '25000.75',
            'is_static' => 'false',
            'environment' => '0',
            'created' => '2023-01-01 12:00:00',
            'updated' => '2023-06-15 14:30:00',
            'secret_key' => 'sk_live_abcdef123456',
            'public_key' => 'pk_live_fedcba654321',
        ];

        $product = new Product($productData);

        // Test all getters with the complete data
        $this->assertEquals(12345, $product->getId());
        $this->assertEquals('Awesome WordPress Plugin', $product->getTitle());
        $this->assertEquals('awesome-wp-plugin', $product->getSlug());
        $this->assertEquals('plugin', $product->getType());
        $this->assertEquals(67890, $product->getDeveloperId());
        $this->assertEquals(11111, $product->getStoreId());
        $this->assertNull($product->getParentPluginId());
        $this->assertFalse($product->isAddon());
        $this->assertEquals('https://example.com/icon.png', $product->getIcon());
        $this->assertEquals(22222, $product->getDefaultPlanId());
        $this->assertEquals([1, 2, 3], $product->getPlans());
        $this->assertEquals([10, 20, 30], $product->getFeatures());
        $this->assertEquals(30, $product->getMoneyBackPeriod());
        $this->assertEquals('flexible', $product->getRefundPolicy());
        $this->assertEquals(15, $product->getAnnualRenewalsDiscount());
        $this->assertEquals('percentage', $product->getRenewalsDiscountType());
        $this->assertTrue($product->isReleased());
        $this->assertTrue($product->isSdkRequired());
        $this->assertTrue($product->isPricingVisible());
        $this->assertTrue($product->isWpOrgCompliant());
        $this->assertEquals(5000, $product->getInstallsCount());
        $this->assertEquals(4500, $product->getActiveInstallsCount());
        $this->assertEquals(8, $product->getFreeReleasesCount());
        $this->assertEquals(12, $product->getPremiumReleasesCount());
        $this->assertEquals(1200, $product->getTotalPurchases());
        $this->assertEquals(800, $product->getTotalSubscriptions());
        $this->assertEquals(600, $product->getTotalRenewals());
        $this->assertEquals(50, $product->getTotalFailedPurchases());
        $this->assertEquals(25000.75, $product->getEarnings());
        $this->assertFalse($product->isStatic());
        $this->assertEquals(0, $product->getEnvironment());
        $this->assertFalse($product->isSandbox());
        $this->assertInstanceOf(DateTime::class, $product->getCreatedAt());
        $this->assertInstanceOf(DateTime::class, $product->getUpdatedAt());
        $this->assertEquals('sk_live_abcdef123456', $product->getSecretKey());
        $this->assertEquals('pk_live_fedcba654321', $product->getPublicKey());
    }
}