{"name": "skpassegna/freemius-php-sdk", "description": "An unofficial but fully refactored and modernized Freemius PHP SDK for seamless integration with the Freemius platform.", "keywords": ["<PERSON><PERSON><PERSON>", "sdk", "php", "licensing", "distribution", "analytics"], "homepage": "https://github.com/skpassegna/freemius-php-sdk", "license": "GPL-2.0+", "version": "1.0.0", "authors": [{"name": "skpassegna", "email": "<EMAIL>", "homepage": "https://github.com/skpassegna"}], "require": {"php": "^8.0", "ext-curl": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.9", "monolog/monolog": "^3.9"}, "autoload": {"psr-4": {"Freemius\\SDK\\": "src/"}}, "require-dev": {"phpunit/phpunit": "^11.5", "symfony/var-dumper": "^7.1", "friendsofphp/php-cs-fixer": "^3.64"}}