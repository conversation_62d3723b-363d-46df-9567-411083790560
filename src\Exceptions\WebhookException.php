<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Webhook exception for webhook processing errors
 * 
 * Thrown when webhook signature validation fails, payload is malformed,
 * or other webhook-specific security or processing issues occur.
 */
class WebhookException extends FreemiusException
{
    /**
     * Create a new webhook exception
     *
     * @param string $message Exception message
     * @param int $code Exception code
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Webhook processing error',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for webhook signature validation failure
     *
     * @param string $expectedSignature The expected signature
     * @param string $receivedSignature The received signature
     * @param array $context Additional context
     * @return self
     */
    public static function invalidSignature(
        string $expectedSignature = '',
        string $receivedSignature = '',
        array $context = []
    ): self {
        $context = array_merge([
            'expected_signature' => $expectedSignature ? substr($expectedSignature, 0, 16) . '...' : 'empty',
            'received_signature' => $receivedSignature ? substr($receivedSignature, 0, 16) . '...' : 'empty',
            'error_type' => 'invalid_signature',
        ], $context);

        return new self(
            'Webhook signature validation failed',
            401,
            null,
            $context
        );
    }

    /**
     * Create an exception for missing webhook signature
     *
     * @param array $context Additional context
     * @return self
     */
    public static function missingSignature(array $context = []): self
    {
        $context = array_merge([
            'error_type' => 'missing_signature',
        ], $context);

        return new self(
            'Webhook signature is missing from request headers',
            400,
            null,
            $context
        );
    }

    /**
     * Create an exception for malformed webhook payload
     *
     * @param string $payload The malformed payload (will be truncated)
     * @param string $parseError The parsing error message
     * @param array $context Additional context
     * @return self
     */
    public static function malformedPayload(
        string $payload = '',
        string $parseError = '',
        array $context = []
    ): self {
        $context = array_merge([
            'payload_length' => strlen($payload),
            'payload_preview' => substr($payload, 0, 200) . (strlen($payload) > 200 ? '...' : ''),
            'parse_error' => $parseError,
            'error_type' => 'malformed_payload',
        ], $context);

        $message = 'Webhook payload is malformed';
        if ($parseError) {
            $message .= ": {$parseError}";
        }

        return new self($message, 400, null, $context);
    }

    /**
     * Create an exception for unsupported webhook event type
     *
     * @param string $eventType The unsupported event type
     * @param array $supportedTypes List of supported event types
     * @param array $context Additional context
     * @return self
     */
    public static function unsupportedEventType(
        string $eventType,
        array $supportedTypes = [],
        array $context = []
    ): self {
        $message = "Unsupported webhook event type: {$eventType}";
        
        if (!empty($supportedTypes)) {
            $message .= '. Supported types: ' . implode(', ', $supportedTypes);
        }

        $context = array_merge([
            'event_type' => $eventType,
            'supported_types' => $supportedTypes,
            'error_type' => 'unsupported_event_type',
        ], $context);

        return new self($message, 400, null, $context);
    }

    /**
     * Create an exception for webhook timestamp validation failure
     *
     * @param int $timestamp The webhook timestamp
     * @param int $currentTime Current timestamp
     * @param int $tolerance Allowed time tolerance in seconds
     * @param array $context Additional context
     * @return self
     */
    public static function timestampValidationFailed(
        int $timestamp,
        int $currentTime,
        int $tolerance = 300,
        array $context = []
    ): self {
        $timeDiff = abs($currentTime - $timestamp);
        
        $context = array_merge([
            'webhook_timestamp' => $timestamp,
            'current_timestamp' => $currentTime,
            'time_difference' => $timeDiff,
            'tolerance' => $tolerance,
            'error_type' => 'timestamp_validation',
        ], $context);

        return new self(
            "Webhook timestamp validation failed. Time difference: {$timeDiff}s, tolerance: {$tolerance}s",
            400,
            null,
            $context
        );
    }

    /**
     * Create an exception for missing webhook secret
     *
     * @param array $context Additional context
     * @return self
     */
    public static function missingWebhookSecret(array $context = []): self
    {
        $context = array_merge([
            'error_type' => 'missing_webhook_secret',
        ], $context);

        return new self(
            'Webhook secret is required for signature validation but not configured',
            500,
            null,
            $context
        );
    }

    /**
     * Create an exception for webhook processing timeout
     *
     * @param int $timeout The timeout value in seconds
     * @param array $context Additional context
     * @return self
     */
    public static function processingTimeout(int $timeout, array $context = []): self
    {
        $context = array_merge([
            'timeout' => $timeout,
            'error_type' => 'processing_timeout',
        ], $context);

        return new self(
            "Webhook processing timed out after {$timeout} seconds",
            408,
            null,
            $context
        );
    }

    /**
     * Create an exception for duplicate webhook processing
     *
     * @param string $webhookId The webhook ID that was duplicated
     * @param array $context Additional context
     * @return self
     */
    public static function duplicateWebhook(string $webhookId, array $context = []): self
    {
        $context = array_merge([
            'webhook_id' => $webhookId,
            'error_type' => 'duplicate_webhook',
        ], $context);

        return new self(
            "Webhook {$webhookId} has already been processed",
            409,
            null,
            $context
        );
    }

    /**
     * Check if this is a security-related webhook error
     *
     * @return bool True if this is a security error
     */
    public function isSecurityError(): bool
    {
        $errorType = $this->getContext()['error_type'] ?? '';
        
        $securityErrorTypes = [
            'invalid_signature',
            'missing_signature',
            'timestamp_validation',
        ];

        return in_array($errorType, $securityErrorTypes, true);
    }

    /**
     * Get security recommendations for webhook errors
     *
     * @return array Security recommendations
     */
    public function getSecurityRecommendations(): array
    {
        if (!$this->isSecurityError()) {
            return [];
        }

        $errorType = $this->getContext()['error_type'] ?? '';
        
        switch ($errorType) {
            case 'invalid_signature':
                return [
                    'Verify your webhook secret is correct',
                    'Check that the signature algorithm matches Freemius requirements',
                    'Ensure the payload is not modified before validation',
                    'Log this incident for security monitoring',
                ];
                
            case 'missing_signature':
                return [
                    'Ensure the webhook request includes the signature header',
                    'Check your webhook endpoint configuration',
                    'Verify the request is coming from Freemius',
                ];
                
            case 'timestamp_validation':
                return [
                    'Check server time synchronization (NTP)',
                    'Verify webhook timestamp tolerance settings',
                    'This could indicate a replay attack attempt',
                ];
                
            default:
                return [
                    'Review webhook security configuration',
                    'Monitor for suspicious webhook activity',
                    'Ensure proper signature validation is enabled',
                ];
        }
    }
}