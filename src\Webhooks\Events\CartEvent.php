<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\User;

/**
 * Cart webhook event
 * 
 * Triggered for cart-related events like abandonment, completion, recovery, etc.
 */
class CartEvent extends WebhookEvent
{
    private ?User $user = null;

    /**
     * Get the user associated with this cart event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the cart ID
     *
     * @return string|null The cart ID
     */
    public function getCartId(): ?string
    {
        return $this->get('cart_id');
    }

    /**
     * Get the cart total amount
     *
     * @return float|null The cart total
     */
    public function getCartTotal(): ?float
    {
        $total = $this->get('cart_total') ?? $this->get('total') ?? $this->get('amount');
        return $total ? (float) $total : null;
    }

    /**
     * Get the cart currency
     *
     * @return string|null The currency code
     */
    public function getCurrency(): ?string
    {
        return $this->get('currency');
    }

    /**
     * Check if this is a cart abandonment
     *
     * @return bool True if cart was abandoned
     */
    public function isAbandonment(): bool
    {
        return $this->eventType === 'cart.abandoned';
    }

    /**
     * Check if this is a cart completion
     *
     * @return bool True if cart was completed
     */
    public function isCompletion(): bool
    {
        return $this->eventType === 'cart.completed';
    }

    /**
     * Check if this is a cart recovery
     *
     * @return bool True if cart was recovered
     */
    public function isRecovery(): bool
    {
        return $this->eventType === 'cart.recovered';
    }

    /**
     * Check if this is a recovery email event
     *
     * @return bool True if this is a recovery email event
     */
    public function isRecoveryEmail(): bool
    {
        return in_array($this->eventType, [
            'cart.recovery.email_1_sent',
            'cart.recovery.email_2_sent',
            'cart.recovery.email_3_sent'
        ]);
    }

    /**
     * Get the recovery email number (1, 2, or 3)
     *
     * @return int|null The email number
     */
    public function getRecoveryEmailNumber(): ?int
    {
        if (preg_match('/cart\.recovery\.email_(\d+)_sent/', $this->eventType, $matches)) {
            return (int) $matches[1];
        }
        return null;
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'cart.abandoned' => 'abandoned',
            'cart.completed' => 'completed',
            'cart.created' => 'created',
            'cart.recovered' => 'recovered',
            'cart.recovery.deactivated' => 'recovery deactivated',
            'cart.recovery.email_1_sent' => 'recovery email 1 sent',
            'cart.recovery.email_2_sent' => 'recovery email 2 sent',
            'cart.recovery.email_3_sent' => 'recovery email 3 sent',
            'cart.recovery.reactivated' => 'recovery reactivated',
            'cart.recovery.subscribed' => 'recovery subscribed',
            'cart.recovery.unsubscribed' => 'recovery unsubscribed',
            'cart.updated' => 'updated',
            default => 'changed'
        };

        $parts = ["Cart {$action}"];

        if ($cartId = $this->getCartId()) {
            $parts[] = "cart:{$cartId}";
        }

        if ($total = $this->getCartTotal()) {
            $currency = $this->getCurrency() ?? 'USD';
            $parts[] = "total:{$currency} {$total}";
        }

        return implode(' ', $parts);
    }
}
