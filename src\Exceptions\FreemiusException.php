<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Exception;
use Throwable;

/**
 * Base exception class for all Freemius SDK exceptions
 * 
 * Provides a common base for all SDK-specific exceptions
 * with additional context and debugging information.
 */
class FreemiusException extends Exception
{
    protected array $context;

    /**
     * Create a new Freemius exception
     *
     * @param string $message Exception message
     * @param int $code Exception code
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get additional context information
     *
     * @return array Context information
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set additional context information
     *
     * @param array $context Context information
     * @return self
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * Add context information
     *
     * @param string $key Context key
     * @param mixed $value Context value
     * @return self
     */
    public function addContext(string $key, $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Get a string representation of the exception with context
     *
     * @return string
     */
    public function __toString(): string
    {
        $string = parent::__toString();
        
        if (!empty($this->context)) {
            $string .= "\nContext: " . json_encode($this->context, JSON_PRETTY_PRINT);
        }
        
        return $string;
    }
}