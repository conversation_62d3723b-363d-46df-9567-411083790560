<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Fixtures;

/**
 * Test fixtures based on Freemius API response formats
 */
class ApiResponseFixtures
{
    /**
     * Get a sample product response
     */
    public static function product(): array
    {
        return [
            'id' => 12345,
            'title' => 'Awesome WordPress Plugin',
            'slug' => 'awesome-wp-plugin',
            'type' => 'plugin',
            'developer_id' => 67890,
            'store_id' => 11111,
            'parent_plugin_id' => null,
            'icon' => 'https://example.com/icons/awesome-plugin.png',
            'default_plan_id' => 22222,
            'plans' => '1,2,3,4',
            'features' => '10,20,30,40',
            'money_back_period' => 30,
            'refund_policy' => 'flexible',
            'annual_renewals_discount' => 15,
            'renewals_discount_type' => 'percentage',
            'is_released' => true,
            'is_sdk_required' => true,
            'is_pricing_visible' => true,
            'is_wp_org_compliant' => true,
            'installs_count' => 5000,
            'active_installs_count' => 4500,
            'free_releases_count' => 8,
            'premium_releases_count' => 12,
            'total_purchases' => 1200,
            'total_subscriptions' => 800,
            'total_renewals' => 600,
            'total_failed_purchases' => 50,
            'earnings' => 25000.75,
            'is_static' => false,
            'environment' => 0,
            'created' => '2023-01-01 12:00:00',
            'updated' => '2023-06-15 14:30:00',
            'secret_key' => 'sk_live_abcdef123456789',
            'public_key' => 'pk_live_fedcba987654321',
        ];
    }

    /**
     * Get a sample product collection response
     */
    public static function productCollection(): array
    {
        return [
            'products' => [
                self::product(),
                [
                    'id' => 54321,
                    'title' => 'Another Great Plugin',
                    'slug' => 'another-great-plugin',
                    'type' => 'plugin',
                    'developer_id' => 67890,
                    'store_id' => 11111,
                    'parent_plugin_id' => 12345,
                    'icon' => 'https://example.com/icons/another-plugin.png',
                    'default_plan_id' => 33333,
                    'plans' => '5,6',
                    'features' => '50,60',
                    'money_back_period' => 14,
                    'refund_policy' => 'strict',
                    'annual_renewals_discount' => 10,
                    'renewals_discount_type' => 'percentage',
                    'is_released' => true,
                    'is_sdk_required' => false,
                    'is_pricing_visible' => true,
                    'is_wp_org_compliant' => false,
                    'installs_count' => 1500,
                    'active_installs_count' => 1200,
                    'free_releases_count' => 3,
                    'premium_releases_count' => 5,
                    'total_purchases' => 300,
                    'total_subscriptions' => 200,
                    'total_renewals' => 150,
                    'total_failed_purchases' => 15,
                    'earnings' => 7500.25,
                    'is_static' => false,
                    'environment' => 0,
                    'created' => '2023-03-15 09:30:00',
                    'updated' => '2023-06-20 16:45:00',
                    'secret_key' => 'sk_live_xyz789abc123',
                    'public_key' => 'pk_live_abc123xyz789',
                ],
            ],
            'paging' => [
                'cursors' => [
                    'before' => 'before_cursor_123',
                    'after' => 'after_cursor_456',
                ],
                'next' => 'https://api.freemius.com/v1/products?after=after_cursor_456',
                'previous' => 'https://api.freemius.com/v1/products?before=before_cursor_123',
            ],
        ];
    }

    /**
     * Get a sample user response
     */
    public static function user(): array
    {
        return [
            'id' => 98765,
            'email' => '<EMAIL>',
            'first' => 'John',
            'last' => 'Doe',
            'is_verified' => true,
            'customer_id' => 'cus_abc123def456',
            'gross_revenue' => 299.99,
            'created' => '2023-02-10 14:20:00',
            'updated' => '2023-06-18 10:15:00',
        ];
    }

    /**
     * Get a sample license response
     */
    public static function license(): array
    {
        return [
            'id' => 55555,
            'user_id' => 98765,
            'product_id' => 12345,
            'plan_id' => 22222,
            'quota' => 1,
            'activated' => 1,
            'blocked' => false,
            'expired' => false,
            'expiration' => '2024-06-15 14:30:00',
            'is_free_localhost' => false,
            'is_block_features' => false,
            'secret_key' => 'sk_license_secret123',
            'created' => '2023-06-15 14:30:00',
            'updated' => '2023-06-15 14:30:00',
        ];
    }

    /**
     * Get a sample installation response
     */
    public static function installation(): array
    {
        return [
            'id' => 77777,
            'user_id' => 98765,
            'product_id' => 12345,
            'license_id' => 55555,
            'url' => 'https://example-site.com',
            'title' => 'Example Site',
            'language' => 'en',
            'charset' => 'UTF-8',
            'platform_version' => '6.2.2',
            'sdk_version' => '2.5.10',
            'version' => '1.2.3',
            'is_active' => true,
            'is_uninstalled' => false,
            'is_locked' => false,
            'created' => '2023-06-15 15:00:00',
            'updated' => '2023-06-20 12:30:00',
        ];
    }

    /**
     * Get a sample subscription response
     */
    public static function subscription(): array
    {
        return [
            'id' => 88888,
            'user_id' => 98765,
            'product_id' => 12345,
            'plan_id' => 22222,
            'license_id' => 55555,
            'billing_cycle' => 'annual',
            'amount' => 99.99,
            'currency' => 'USD',
            'status' => 'active',
            'trial_ends' => null,
            'next_payment' => '2024-06-15 14:30:00',
            'created' => '2023-06-15 14:30:00',
            'updated' => '2023-06-15 14:30:00',
        ];
    }

    /**
     * Get a sample payment response
     */
    public static function payment(): array
    {
        return [
            'id' => 99999,
            'user_id' => 98765,
            'product_id' => 12345,
            'subscription_id' => 88888,
            'license_id' => 55555,
            'amount' => 99.99,
            'currency' => 'USD',
            'status' => 'completed',
            'gateway' => 'stripe',
            'external_id' => 'pi_1234567890abcdef',
            'created' => '2023-06-15 14:30:00',
            'updated' => '2023-06-15 14:30:00',
        ];
    }

    /**
     * Get a sample plan response
     */
    public static function plan(): array
    {
        return [
            'id' => 22222,
            'product_id' => 12345,
            'name' => 'Professional',
            'title' => 'Professional Plan',
            'description' => 'Perfect for professionals',
            'is_free' => false,
            'trial_period' => 14,
            'annual_discount' => 20,
            'is_block_features' => true,
            'license_type' => 0,
            'is_https_support' => true,
            'support_kb' => true,
            'support_forum' => true,
            'support_email' => true,
            'support_phone' => false,
            'support_skype' => false,
            'created' => '2023-01-01 12:00:00',
            'updated' => '2023-03-15 10:30:00',
        ];
    }

    /**
     * Get a sample error response
     */
    public static function errorResponse(int $code = 400, string $message = 'Bad Request'): array
    {
        return [
            'error' => [
                'type' => 'invalid_request_error',
                'code' => $code,
                'message' => $message,
                'param' => null,
            ],
        ];
    }

    /**
     * Get a sample validation error response
     */
    public static function validationErrorResponse(): array
    {
        return [
            'error' => [
                'type' => 'invalid_request_error',
                'code' => 400,
                'message' => 'Validation failed',
                'param' => null,
                'errors' => [
                    'email' => ['Email is required'],
                    'name' => ['Name must be at least 2 characters'],
                ],
            ],
        ];
    }

    /**
     * Get a sample rate limit error response
     */
    public static function rateLimitErrorResponse(): array
    {
        return [
            'error' => [
                'type' => 'rate_limit_error',
                'code' => 429,
                'message' => 'Too many requests',
                'param' => null,
            ],
        ];
    }

    /**
     * Get a sample authentication error response
     */
    public static function authenticationErrorResponse(): array
    {
        return [
            'error' => [
                'type' => 'authentication_error',
                'code' => 401,
                'message' => 'Invalid API credentials',
                'param' => null,
            ],
        ];
    }

    /**
     * Get a sample not found error response
     */
    public static function notFoundErrorResponse(): array
    {
        return [
            'error' => [
                'type' => 'invalid_request_error',
                'code' => 404,
                'message' => 'Resource not found',
                'param' => null,
            ],
        ];
    }

    /**
     * Get webhook payload for install activated event
     */
    public static function webhookInstallActivated(): array
    {
        return [
            'id' => 'evt_install_activated_123',
            'type' => 'install.activated',
            'created' => **********,
            'data' => [
                'object' => [
                    'id' => 77777,
                    'user_id' => 98765,
                    'product_id' => 12345,
                    'license_id' => 55555,
                    'url' => 'https://example-site.com',
                    'title' => 'Example Site',
                    'language' => 'en',
                    'charset' => 'UTF-8',
                    'platform_version' => '6.2.2',
                    'sdk_version' => '2.5.10',
                    'version' => '1.2.3',
                    'is_active' => true,
                    'is_uninstalled' => false,
                    'is_locked' => false,
                    'created' => '2023-06-15 15:00:00',
                    'updated' => '2023-06-15 15:00:00',
                ],
            ],
        ];
    }

    /**
     * Get webhook payload for subscription updated event
     */
    public static function webhookSubscriptionUpdated(): array
    {
        return [
            'id' => 'evt_subscription_updated_456',
            'type' => 'subscription.updated',
            'created' => **********,
            'data' => [
                'object' => [
                    'id' => 88888,
                    'user_id' => 98765,
                    'product_id' => 12345,
                    'plan_id' => 22222,
                    'license_id' => 55555,
                    'billing_cycle' => 'annual',
                    'amount' => 99.99,
                    'currency' => 'USD',
                    'status' => 'active',
                    'trial_ends' => null,
                    'next_payment' => '2024-06-15 14:30:00',
                    'created' => '2023-06-15 14:30:00',
                    'updated' => '2023-06-15 14:30:00',
                ],
            ],
        ];
    }

    /**
     * Get webhook payload for payment completed event
     */
    public static function webhookPaymentCompleted(): array
    {
        return [
            'id' => 'evt_payment_completed_789',
            'type' => 'payment.completed',
            'created' => **********,
            'data' => [
                'object' => [
                    'id' => 99999,
                    'user_id' => 98765,
                    'product_id' => 12345,
                    'subscription_id' => 88888,
                    'license_id' => 55555,
                    'amount' => 99.99,
                    'currency' => 'USD',
                    'status' => 'completed',
                    'gateway' => 'stripe',
                    'external_id' => 'pi_1234567890abcdef',
                    'created' => '2023-06-15 14:30:00',
                    'updated' => '2023-06-15 14:30:00',
                ],
            ],
        ];
    }

    /**
     * Get a paginated response wrapper
     */
    public static function paginatedResponse(array $data, string $dataKey = 'data'): array
    {
        return [
            $dataKey => $data,
            'paging' => [
                'cursors' => [
                    'before' => 'before_cursor_abc',
                    'after' => 'after_cursor_xyz',
                ],
                'next' => 'https://api.freemius.com/v1/endpoint?after=after_cursor_xyz',
                'previous' => 'https://api.freemius.com/v1/endpoint?before=before_cursor_abc',
            ],
        ];
    }

    /**
     * Get sample response headers
     */
    public static function responseHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'X-RateLimit-Limit' => '1000',
            'X-RateLimit-Remaining' => '999',
            'X-RateLimit-Reset' => '**********',
            'X-Request-ID' => 'req_123456789abcdef',
        ];
    }

    /**
     * Get rate limit response headers
     */
    public static function rateLimitHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'X-RateLimit-Limit' => '1000',
            'X-RateLimit-Remaining' => '0',
            'X-RateLimit-Reset' => '1623456849',
            'Retry-After' => '60',
            'X-Request-ID' => 'req_rate_limited_123',
        ];
    }
}