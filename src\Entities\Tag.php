<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Tag entity representing a Freemius product tag/version.
 * 
 * A tag represents a specific version release of a product
 * that can be distributed to customers.
 */
class Tag extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'plugin_id' => 'integer',
        'developer_id' => 'integer',
        'downloaded' => 'integer',
        'uniques' => 'integer',
        'limit' => 'integer',
        'has_free' => 'boolean',
        'has_premium' => 'boolean',
        'is_incremental' => 'boolean',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
    ];

    /**
     * Get the tag ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the plugin/product ID.
     */
    public function getPluginId(): int
    {
        return $this->getAttribute('plugin_id');
    }

    /**
     * Get the developer ID.
     */
    public function getDeveloperId(): int
    {
        return $this->getAttribute('developer_id');
    }

    /**
     * Get the product slug.
     */
    public function getSlug(): ?string
    {
        return $this->getAttribute('slug');
    }

    /**
     * Get the premium product slug.
     */
    public function getPremiumSlug(): ?string
    {
        return $this->getAttribute('premium_slug');
    }

    /**
     * Get the version.
     */
    public function getVersion(): string
    {
        return $this->getAttribute('version', '');
    }

    /**
     * Get the SDK version.
     */
    public function getSdkVersion(): ?string
    {
        return $this->getAttribute('sdk_version');
    }

    /**
     * Get the required platform version.
     */
    public function getRequiresPlatformVersion(): ?string
    {
        return $this->getAttribute('requires_platform_version');
    }

    /**
     * Get the required programming language version.
     */
    public function getRequiresProgrammingLanguageVersion(): ?string
    {
        return $this->getAttribute('requires_programming_language_version');
    }

    /**
     * Get the tested up to version.
     */
    public function getTestedUpToVersion(): ?string
    {
        return $this->getAttribute('tested_up_to_version');
    }

    /**
     * Get the download count.
     */
    public function getDownloaded(): int
    {
        return $this->getAttribute('downloaded', 0);
    }

    /**
     * Get the unique downloads count.
     */
    public function getUniques(): int
    {
        return $this->getAttribute('uniques', 0);
    }

    /**
     * Get the download limit for staged rollout.
     */
    public function getLimit(): ?int
    {
        return $this->getAttribute('limit');
    }

    /**
     * Check if the tag has a free version.
     */
    public function hasFree(): bool
    {
        return $this->getAttribute('has_free', false);
    }

    /**
     * Check if the tag has a premium version.
     */
    public function hasPremium(): bool
    {
        return $this->getAttribute('has_premium', false);
    }

    /**
     * Get the release mode.
     */
    public function getReleaseMode(): string
    {
        return $this->getAttribute('release_mode', 'pending');
    }

    /**
     * Check if the tag is released.
     */
    public function isReleased(): bool
    {
        return $this->getReleaseMode() === 'released';
    }

    /**
     * Check if the tag is in beta.
     */
    public function isBeta(): bool
    {
        return $this->getReleaseMode() === 'beta';
    }

    /**
     * Check if the tag is pending.
     */
    public function isPending(): bool
    {
        return $this->getReleaseMode() === 'pending';
    }

    /**
     * Check if the tag is incremental.
     */
    public function isIncremental(): bool
    {
        return $this->getAttribute('is_incremental', false);
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }
}