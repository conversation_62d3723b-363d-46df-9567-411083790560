<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use Freemius\SDK\Configuration;
use <PERSON>mius\SDK\Exceptions\RateLimitException;

/**
 * Rate limiter for managing API request rates
 * 
 * Implements client-side rate limiting to prevent exceeding
 * API limits and provides backoff strategies for rate limit recovery.
 */
class RateLimiter
{
    private Configuration $config;
    private array $requestHistory = [];
    private ?int $lastRateLimitReset = null;
    private ?int $rateLimitRemaining = null;
    private ?int $rateLimitLimit = null;

    /**
     * Create a new rate limiter instance
     *
     * @param Configuration $config SDK configuration
     */
    public function __construct(Configuration $config)
    {
        $this->config = $config;
    }

    /**
     * Check if a request can be made without exceeding rate limits
     *
     * @return bool True if request can be made
     * @throws RateLimitException If rate limit would be exceeded
     */
    public function canMakeRequest(): bool
    {
        $this->cleanupOldRequests();

        // Check server-side rate limit info if available
        if ($this->rateLimitRemaining !== null && $this->rateLimitRemaining <= 0) {
            $retryAfter = $this->calculateRetryAfter();
            throw RateLimitException::withRetryInfo($retryAfter, [
                'X-RateLimit-Limit' => (string) $this->rateLimitLimit,
                'X-RateLimit-Remaining' => '0',
                'X-RateLimit-Reset' => (string) $this->lastRateLimitReset,
            ]);
        }

        // Check client-side rate limiting
        $currentWindow = $this->getCurrentWindow();
        $requestsInWindow = count($this->requestHistory[$currentWindow] ?? []);

        if ($requestsInWindow >= $this->config->getRateLimitMaxRequests()) {
            $retryAfter = $this->config->getRateLimitWindowSeconds() - (time() % $this->config->getRateLimitWindowSeconds());
            throw RateLimitException::withRetryInfo($retryAfter);
        }

        return true;
    }

    /**
     * Record a successful request
     *
     * @return void
     */
    public function recordRequest(): void
    {
        $currentWindow = $this->getCurrentWindow();
        
        if (!isset($this->requestHistory[$currentWindow])) {
            $this->requestHistory[$currentWindow] = [];
        }
        
        $this->requestHistory[$currentWindow][] = time();
    }

    /**
     * Update rate limit information from response headers
     *
     * @param array $headers Response headers
     * @return void
     */
    public function updateFromHeaders(array $headers): void
    {
        if (isset($headers['X-RateLimit-Limit'])) {
            $this->rateLimitLimit = (int) $headers['X-RateLimit-Limit'];
        }

        if (isset($headers['X-RateLimit-Remaining'])) {
            $this->rateLimitRemaining = (int) $headers['X-RateLimit-Remaining'];
        }

        if (isset($headers['X-RateLimit-Reset'])) {
            $this->lastRateLimitReset = (int) $headers['X-RateLimit-Reset'];
        }
    }

    /**
     * Calculate backoff delay based on strategy
     *
     * @param int $attempt Attempt number (0-based)
     * @param int|null $retryAfter Server-suggested retry after seconds
     * @return int Delay in seconds
     */
    public function calculateBackoffDelay(int $attempt, ?int $retryAfter = null): int
    {
        // Use server-suggested delay if available
        if ($retryAfter !== null) {
            return $retryAfter;
        }

        $strategy = $this->config->getRateLimitBackoffStrategy();

        switch ($strategy) {
            case 'linear':
                return ($attempt + 1) * 2; // 2s, 4s, 6s, 8s...

            case 'exponential':
                return min(pow(2, $attempt), 300); // 1s, 2s, 4s, 8s... max 5 minutes

            case 'fixed':
                return 5; // Always 5 seconds

            default:
                return min(pow(2, $attempt), 300); // Default to exponential
        }
    }

    /**
     * Get current rate limit status
     *
     * @return array Rate limit status information
     */
    public function getStatus(): array
    {
        $this->cleanupOldRequests();
        $currentWindow = $this->getCurrentWindow();
        $requestsInWindow = count($this->requestHistory[$currentWindow] ?? []);

        return [
            'requests_made' => $requestsInWindow,
            'requests_limit' => $this->config->getRateLimitMaxRequests(),
            'requests_remaining' => max(0, $this->config->getRateLimitMaxRequests() - $requestsInWindow),
            'window_seconds' => $this->config->getRateLimitWindowSeconds(),
            'current_window' => $currentWindow,
            'server_limit' => $this->rateLimitLimit,
            'server_remaining' => $this->rateLimitRemaining,
            'server_reset' => $this->lastRateLimitReset,
        ];
    }

    /**
     * Reset rate limiter state
     *
     * @return void
     */
    public function reset(): void
    {
        $this->requestHistory = [];
        $this->lastRateLimitReset = null;
        $this->rateLimitRemaining = null;
        $this->rateLimitLimit = null;
    }

    /**
     * Get the current time window for rate limiting
     *
     * @return int Current window timestamp
     */
    private function getCurrentWindow(): int
    {
        $windowSize = $this->config->getRateLimitWindowSeconds();
        return (int) (time() / $windowSize) * $windowSize;
    }

    /**
     * Clean up old request history outside current windows
     *
     * @return void
     */
    private function cleanupOldRequests(): void
    {
        $currentWindow = $this->getCurrentWindow();
        $windowSize = $this->config->getRateLimitWindowSeconds();
        $cutoffTime = $currentWindow - ($windowSize * 2); // Keep 2 windows of history

        foreach ($this->requestHistory as $window => $requests) {
            if ($window < $cutoffTime) {
                unset($this->requestHistory[$window]);
            }
        }
    }

    /**
     * Calculate retry after time based on current state
     *
     * @return int Seconds to wait before retry
     */
    private function calculateRetryAfter(): int
    {
        if ($this->lastRateLimitReset !== null) {
            $resetTime = $this->lastRateLimitReset;
            $currentTime = time();
            
            if ($resetTime > $currentTime) {
                return $resetTime - $currentTime;
            }
        }

        // Fallback to window-based calculation
        $windowSize = $this->config->getRateLimitWindowSeconds();
        return $windowSize - (time() % $windowSize);
    }
}