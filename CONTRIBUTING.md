# Contributing

Contributions to the Freemius PHP SDK are welcome and encouraged! Whether you're fixing bugs, adding new features, or improving the documentation, your contributions help make the SDK better for everyone.

## Ways to Contribute

- **Bug Reports:** If you encounter any issues or bugs while using the SDK, please report them on the [GitHub issue tracker](https://github.com/skpassegna/freemius-php-sdk/issues). Provide a clear description of the problem, steps to reproduce it, and any relevant error messages or logs.
- **Feature Requests:** If you have ideas for new features or enhancements, please open an issue on the GitHub issue tracker. Describe the proposed feature, its benefits, and any potential implementation details.
- **Pull Requests:** If you'd like to contribute code changes, you can submit a pull request on GitHub. Fork the repository, make your changes, and submit a pull request with a clear description of your changes.

## Contribution Guidelines

- **Follow Coding Standards:** Adhere to the PSR-12 coding standards to ensure code consistency and readability.
- **Write Unit Tests:** Provide comprehensive unit tests for any new features or bug fixes to ensure code quality and prevent regressions.
- **Update Documentation:** If your changes affect the SDK's functionality or usage, update the documentation accordingly.

## Development Setup

1. **Clone the Repository:**
   ```bash
   git clone https://github.com/skpassegna/freemius-php-sdk.git
   ```
2. **Install Dependencies:**

   ```bash
   composer install
   ```

3. **Run Tests:**
   ```bash
   ./vendor/bin/phpunit
   ```

## Code of Conduct

Please be respectful and considerate of others when contributing to the project. Follow the [Contributor Covenant Code of Conduct](https://www.contributor-covenant.org/version/2/1/code_of_conduct/) to ensure a welcoming and inclusive community.

**Thank you for your contributions!**