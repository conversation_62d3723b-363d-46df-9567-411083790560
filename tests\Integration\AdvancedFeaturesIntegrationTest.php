<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Integration;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\FreemiusSDK;
use Freemius\SDK\Configuration;
use Freemius\SDK\Exceptions\RateLimitException;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Integration tests for advanced SDK features
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\FreemiusSDK::class)]
#[\PHPUnit\Framework\Attributes\Group('integration')]
class AdvancedFeaturesIntegrationTest extends TestCase
{
    private FreemiusSDK $sdk;
    private Configuration $config;

    protected function setUp(): void
    {
        // Skip integration tests if no bearer token is provided
        $bearerToken = $_ENV['FREEMIUS_TEST_BEARER_TOKEN'] ?? '';
        if (empty($bearerToken)) {
            $this->markTestSkipped('FREEMIUS_TEST_BEARER_TOKEN environment variable not set');
        }

        // Configure SDK for sandbox environment
        $this->config = new Configuration([
            'bearerToken' => $bearerToken,
            'sandbox' => true,
            'timeout' => 30,
            'retryAttempts' => 2,
            'logging' => false,
            'productScope' => 1, // Use test product ID for sandbox
        ]);

        $this->sdk = new FreemiusSDK($this->config);
    }

    public function testNestedResourceRelationships(): void
    {
        // Test nested resource access (e.g., product.users(), license.subscriptions())
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            // Test product -> users relationship
            $productUsers = $this->sdk->products()->users($productId);
            $this->assertInstanceOf(\Freemius\SDK\Resources\Users::class, $productUsers);
            
            $users = $productUsers->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
            
            // Test product -> licenses relationship
            $productLicenses = $this->sdk->products()->licenses($productId);
            $this->assertInstanceOf(\Freemius\SDK\Resources\Licenses::class, $productLicenses);
            
            $licenses = $productLicenses->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);
            
            // Test product -> installations relationship
            $productInstallations = $this->sdk->products()->installations($productId);
            $this->assertInstanceOf(\Freemius\SDK\Resources\Installations::class, $productInstallations);
            
            try {
                $installations = $productInstallations->all();
                $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $installations);
            } catch (\Freemius\SDK\Exceptions\NotFoundException $e) {
                // It's acceptable if no installations exist for this product
                $this->assertTrue(true, 'No installations found for product, which is acceptable');
            }
        } else {
            $this->markTestSkipped('No products available for nested resource testing');
        }
    }

    public function testFluentInterfaceComplexQueries(): void
    {
        // Test complex fluent interface queries
        $users = $this->sdk->users()
            ->where('email', 'like', '%@example.com')
            ->where('is_verified', true)
            ->limit(10)
            ->offset(0)
            ->orderBy('created', 'desc')
            ->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        $this->assertLessThanOrEqual(10, $users->count());
        
        // Test chaining with different resources
        $licenses = $this->sdk->licenses()
            ->where('is_active', true)
            ->where('is_expired', false)
            ->limit(5)
            ->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);
        $this->assertLessThanOrEqual(5, $licenses->count());
    }

    public function testAdvancedPaginationHandling(): void
    {
        // Test advanced pagination scenarios
        $pageSize = 3;
        $firstPage = $this->sdk->products()->all(['limit' => $pageSize, 'offset' => 0]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $firstPage);
        $this->assertLessThanOrEqual($pageSize, $firstPage->count());
        
        $pagination = $firstPage->getPagination();
        $this->assertIsArray($pagination);
        
        // Test that we can navigate through pages
        if ($firstPage->count() === $pageSize) {
            $secondPage = $this->sdk->products()->all(['limit' => $pageSize, 'offset' => $pageSize]);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $secondPage);
            
            // Ensure different pages have different content (if available)
            if ($firstPage->count() > 0 && $secondPage->count() > 0) {
                $firstPageIds = $firstPage->pluck('id');
                $secondPageIds = $secondPage->pluck('id');
                
                // Should have different IDs (no overlap)
                $intersection = array_intersect($firstPageIds, $secondPageIds);
                $this->assertEmpty($intersection, 'Pages should not have overlapping content');
            }
        }
    }

    public function testBulkOperationsAndBatching(): void
    {
        // Test bulk operations where supported
        $products = $this->sdk->products()->all(['limit' => 5]);
        
        if ($products->count() > 1) {
            $productIds = $products->pluck('id');
            
            // Test bulk retrieval
            $bulkResults = [];
            foreach ($productIds as $productId) {
                try {
                    $product = $this->sdk->products()->get($productId);
                    $bulkResults[] = $product;
                } catch (\Exception $e) {
                    // Some products might not be accessible, which is fine for testing
                    continue;
                }
            }
            
            $this->assertGreaterThan(0, count($bulkResults));
            
            // Verify all results are Product entities
            foreach ($bulkResults as $product) {
                $this->assertInstanceOf(\Freemius\SDK\Entities\Product::class, $product);
            }
        } else {
            $this->markTestSkipped('Not enough products for bulk operation testing');
        }
    }

    public function testAdvancedFilteringAndSearching(): void
    {
        // Test basic filtering capabilities with supported parameters
        try {
            // Test users with basic parameters (fields, count, offset are supported)
            $users = $this->sdk->users()->all([
                'count' => 10,
                'fields' => 'id,email,first,last,created',
            ]);

            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        } catch (\RuntimeException $e) {
            if (strpos($e->getMessage(), 'Failed to decode JSON') !== false) {
                $this->markTestSkipped('API returned malformed JSON for users filtering request');
            } else {
                throw $e;
            }
        } catch (\Exception $e) {
            // If we get any other exception, skip the test as the API might not be accessible
            $this->markTestSkipped('API not accessible for users filtering: ' . $e->getMessage());
        }

        // Test installations with supported filtering parameters
        try {
            $installations = $this->sdk->installations()->all([
                'count' => 10,
                'fields' => 'id,url,title,is_active',
            ]);

            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $installations);
        } catch (\Exception $e) {
            // If we get any exception, skip this part as the API might not be accessible
            $this->markTestSkipped('API not accessible for installations filtering: ' . $e->getMessage());
        }

        // Test products with basic filtering
        try {
            $products = $this->sdk->products()->all([
                'count' => 5,
                'fields' => 'id,title,slug',
            ]);

            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
        } catch (\Exception $e) {
            // If we get any exception, skip this part as the API might not be accessible
            $this->markTestSkipped('API not accessible for products filtering: ' . $e->getMessage());
        }
    }

    public function testConcurrentRequestHandling(): void
    {
        // Test that the SDK can handle multiple concurrent requests
        $requests = [];
        $startTime = microtime(true);
        
        // Make multiple requests concurrently (simulated)
        for ($i = 0; $i < 3; $i++) {
            try {
                $requests[] = $this->sdk->products()->all(['limit' => 2]);
            } catch (\Exception $e) {
                // Rate limiting or other errors are acceptable
                $this->assertInstanceOf(\Exception::class, $e);
            }
        }
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // Should complete within reasonable time
        $this->assertLessThan(30, $duration, 'Concurrent requests took too long');
        
        // At least some requests should succeed
        $successfulRequests = array_filter($requests, function($request) {
            return $request instanceof \Freemius\SDK\Support\Collection;
        });
        
        $this->assertGreaterThan(0, count($successfulRequests));
    }

    public function testErrorRecoveryAndRetryLogic(): void
    {
        // Test that the SDK properly handles and recovers from errors
        $maxAttempts = 3;
        $attempts = 0;
        $success = false;
        
        while ($attempts < $maxAttempts && !$success) {
            try {
                $attempts++;
                $products = $this->sdk->products()->all();
                $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
                $success = true;
            } catch (RateLimitException $e) {
                // Rate limiting is expected, wait and retry
                if ($attempts < $maxAttempts) {
                    sleep(1);
                    continue;
                } else {
                    throw $e;
                }
            } catch (\Exception $e) {
                // Other exceptions should be handled appropriately
                $this->assertInstanceOf(\Exception::class, $e);
                break;
            }
        }
        
        $this->assertTrue($success || $attempts >= $maxAttempts, 'Should either succeed or exhaust retry attempts');
    }

    public function testDataConsistencyAcrossRequests(): void
    {
        try {
            // Test that data remains consistent across multiple requests
            $firstRequest = $this->sdk->products()->all(['limit' => 5]);
        
        if ($firstRequest->count() > 0) {
            $firstProduct = $firstRequest->first();
            $productId = $firstProduct->getId();
            
            // Make the same request again
            $secondRequest = $this->sdk->products()->all(['limit' => 5]);
            
            if ($secondRequest->count() > 0) {
                $foundSameProduct = false;
                foreach ($secondRequest as $product) {
                    if ($product->getId() === $productId) {
                        $foundSameProduct = true;
                        
                        // Verify data consistency
                        $this->assertEquals($firstProduct->getTitle(), $product->getTitle());
                        $this->assertEquals($firstProduct->getSlug(), $product->getSlug());
                        $this->assertEquals($firstProduct->getType(), $product->getType());
                        break;
                    }
                }
                
                $this->assertTrue($foundSameProduct, 'Same product should be found in subsequent requests');
            }
        } else {
            $this->markTestSkipped('No products available for consistency testing');
        }
        } catch (\RuntimeException $e) {
            if (strpos($e->getMessage(), 'Failed to decode JSON') !== false) {
                $this->markTestSkipped('API returned malformed JSON for consistency testing request');
            } else {
                throw $e;
            }
        }
    }

    public function testResourceRelationshipIntegrity(): void
    {
        // Test that relationships between resources are maintained
        try {
            $products = $this->sdk->products()->all();

            // At minimum, we should be able to get products
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);

            if ($products->count() > 0) {
                $product = $products->first();
                $productId = $product->getId();

                // Verify we can get the product ID
                $this->assertNotEmpty($productId);
                $this->assertIsNumeric($productId);

                // Get users for this product
                $this->sdk->setProductScope($productId);
                $users = $this->sdk->users()->all();

                // Verify users collection is valid
                $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);

                // Get licenses for this product
                $licenses = $this->sdk->licenses()->all();

                // Verify licenses collection is valid
                $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);

                // If we have both users and licenses, verify relationships
                if ($users->count() > 0 && $licenses->count() > 0) {
                    $userIds = [];
                    foreach ($users as $user) {
                        $userIds[] = $user->getId();
                    }

                    foreach ($licenses as $license) {
                        // Verify license has a product ID
                        $this->assertNotEmpty($license->getProductId());

                        // License should belong to a user of this product (if it has a user)
                        if ($license->getUserId()) {
                            $this->assertContains($license->getUserId(), $userIds);
                        }
                    }
                }
            } else {
                $this->markTestSkipped('No products available for relationship testing');
            }
        } catch (\Exception $e) {
            $this->markTestSkipped('API not accessible for relationship testing: ' . $e->getMessage());
        }
    }

    public function testComplexWorkflowScenarios(): void
    {
        // Test complex real-world workflow scenarios
        
        // Scenario 1: Product analytics workflow
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $product = $products->first();
            $productId = $product->getId();
            
            $this->sdk->setProductScope($productId);
            
            // Get all related data for analytics
            $users = $this->sdk->users()->all();
            
            try {
                $installations = $this->sdk->installations()->all();
            } catch (\Freemius\SDK\Exceptions\NotFoundException $e) {
                // Create empty collection if no installations exist
                $installations = new \Freemius\SDK\Support\Collection();
            }
            
            $licenses = $this->sdk->licenses()->all();
            $subscriptions = $this->sdk->subscriptions()->all();
            $payments = $this->sdk->payments()->all();
            
            // Verify we can access all data types
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $installations);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $subscriptions);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $payments);
            
            // Calculate some basic metrics
            $totalUsers = $users->count();
            $totalInstallations = $installations->count();
            $totalRevenue = 0;
            
            foreach ($payments as $payment) {
                if ($payment->getStatus() === 'completed') {
                    $totalRevenue += $payment->getAmount();
                }
            }
            
            // Verify metrics make sense
            $this->assertGreaterThanOrEqual(0, $totalUsers);
            $this->assertGreaterThanOrEqual(0, $totalInstallations);
            $this->assertGreaterThanOrEqual(0, $totalRevenue);
        } else {
            $this->markTestSkipped('No products available for workflow testing');
        }
    }

    public function testConfigurationFlexibility(): void
    {
        // Test different configuration options
        $configs = [
            new Configuration([
                'bearerToken' => $_ENV['FREEMIUS_TEST_BEARER_TOKEN'],
                'sandbox' => true,
                'timeout' => 15,
                'retryAttempts' => 1,
                'productScope' => 1,
            ]),
            new Configuration([
                'bearerToken' => $_ENV['FREEMIUS_TEST_BEARER_TOKEN'],
                'sandbox' => true,
                'timeout' => 45,
                'retryAttempts' => 3,
                'productScope' => 1,
            ]),
        ];
        
        foreach ($configs as $config) {
            $sdk = new FreemiusSDK($config);
            
            // Test that SDK works with different configurations
            $products = $sdk->products()->all(['limit' => 1]);
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
            
            // Verify configuration is applied
            $this->assertTrue($config->isSandbox());
            $this->assertIsInt($config->getTimeout());
            $this->assertIsInt($config->getRetryAttempts());
        }
    }
}
