<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Trial entity representing a Freemius trial period.
 * 
 * A trial allows users to test premium features of a product
 * for a limited time before making a purchase decision.
 */
class Trial extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'product_id' => 'integer',
        'plan_id' => 'integer',
        'license_id' => 'integer',
        'payment_id' => 'integer',
        'with_payment_method' => 'boolean',
        'is_active' => 'boolean',
        'is_expired' => 'boolean',
        'is_cancelled' => 'boolean',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'trial_ends_at',
        'canceled_at',
        'converted_at',
    ];

    /**
     * Get the trial ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the product ID.
     */
    public function getProductId(): int
    {
        return $this->getAttribute('product_id');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the license ID.
     */
    public function getLicenseId(): ?int
    {
        return $this->getAttribute('license_id');
    }

    /**
     * Get the payment ID.
     */
    public function getPaymentId(): ?int
    {
        return $this->getAttribute('payment_id');
    }

    /**
     * Check if trial was created with payment method.
     */
    public function hasPaymentMethod(): bool
    {
        return $this->getAttribute('with_payment_method', false);
    }

    /**
     * Check if the trial is active.
     */
    public function isActive(): bool
    {
        return $this->getAttribute('is_active', false);
    }

    /**
     * Check if the trial is expired.
     */
    public function isExpired(): bool
    {
        return $this->getAttribute('is_expired', false);
    }

    /**
     * Check if the trial is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->getAttribute('is_cancelled', false);
    }

    /**
     * Get the number of days remaining in the trial.
     */
    public function getDaysRemaining(): int
    {
        $trialEndsAt = $this->getTrialEndsAt();
        if (!$trialEndsAt) {
            return 0;
        }

        $now = new DateTime();
        if ($trialEndsAt <= $now) {
            return 0;
        }

        return (int) $now->diff($trialEndsAt)->days;
    }

    /**
     * Check if the trial is currently valid (active and not expired).
     */
    public function isValid(): bool
    {
        return $this->isActive() && !$this->isExpired() && !$this->isCancelled();
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the trial end date.
     */
    public function getTrialEndsAt(): DateTime
    {
        return $this->getAttribute('trial_ends_at');
    }

    /**
     * Get the trial cancellation date.
     */
    public function getCanceledAt(): ?DateTime
    {
        return $this->getAttribute('canceled_at');
    }

    /**
     * Get the trial conversion date.
     */
    public function getConvertedAt(): ?DateTime
    {
        return $this->getAttribute('converted_at');
    }
}