<?php

declare(strict_types=1);

namespace Freemius\SDK;

use InvalidArgumentException;

/**
 * Configuration management for the Freemius SDK
 * 
 * Handles all configuration options including API credentials,
 * environment settings, timeouts, and logging preferences.
 */
class Configuration
{
    private string $bearerToken;
    private string $baseUrl;
    private bool $sandbox;
    private int $timeout;
    private int $retryAttempts;
    private bool $logging;
    private string $logLevel;
    private ?string $logFile;
    private ?int $productScope;
    private int $rateLimitMaxRequests;
    private int $rateLimitWindowSeconds;
    private string $rateLimitBackoffStrategy;
    private bool $cacheEnabled;
    private int $cacheTtl;
    private int $cacheMaxSize;

    /**
     * Default configuration values
     */
    private const DEFAULTS = [
        'baseUrl' => 'https://api.freemius.com/v1/',
        'sandboxUrl' => 'https://docs.freemius.com/_mock/api/',
        'sandbox' => false,
        'timeout' => 30,
        'retryAttempts' => 3,
        'logging' => false,
        'logLevel' => 'info',
        'logFile' => null,
        'productScope' => null,
        'rateLimitMaxRequests' => 100,
        'rateLimitWindowSeconds' => 60,
        'rateLimitBackoffStrategy' => 'exponential',
        'cacheEnabled' => true,
        'cacheTtl' => 300,
        'cacheMaxSize' => 1000,
    ];

    /**
     * Valid log levels
     */
    private const VALID_LOG_LEVELS = ['debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency'];

    /**
     * Valid rate limit backoff strategies
     */
    private const VALID_BACKOFF_STRATEGIES = ['linear', 'exponential', 'fixed'];

    /**
     * Create a new configuration instance
     *
     * @param array $options Configuration options
     * @throws InvalidArgumentException If required options are missing or invalid
     */
    public function __construct(array $options = [])
    {
        $this->validateRequiredOptions($options);

        $this->bearerToken = $options['bearerToken'];
        $this->sandbox = $options['sandbox'] ?? self::DEFAULTS['sandbox'];
        $this->baseUrl = $this->sandbox ? self::DEFAULTS['sandboxUrl'] : ($options['baseUrl'] ?? self::DEFAULTS['baseUrl']);
        $this->timeout = $options['timeout'] ?? self::DEFAULTS['timeout'];
        $this->retryAttempts = $options['retryAttempts'] ?? self::DEFAULTS['retryAttempts'];
        $this->logging = $options['logging'] ?? self::DEFAULTS['logging'];
        $this->logLevel = $options['logLevel'] ?? self::DEFAULTS['logLevel'];
        $this->logFile = $options['logFile'] ?? self::DEFAULTS['logFile'];
        $this->productScope = $options['productScope'] ?? self::DEFAULTS['productScope'];
        $this->rateLimitMaxRequests = $options['rateLimitMaxRequests'] ?? self::DEFAULTS['rateLimitMaxRequests'];
        $this->rateLimitWindowSeconds = $options['rateLimitWindowSeconds'] ?? self::DEFAULTS['rateLimitWindowSeconds'];
        $this->rateLimitBackoffStrategy = $options['rateLimitBackoffStrategy'] ?? self::DEFAULTS['rateLimitBackoffStrategy'];
        $this->cacheEnabled = $options['cacheEnabled'] ?? self::DEFAULTS['cacheEnabled'];
        $this->cacheTtl = $options['cacheTtl'] ?? self::DEFAULTS['cacheTtl'];
        $this->cacheMaxSize = $options['cacheMaxSize'] ?? self::DEFAULTS['cacheMaxSize'];

        $this->validateConfiguration();
    }

    /**
     * Create configuration from environment variables
     *
     * @param array $additionalOptions Additional options to override environment values
     * @return self
     * @throws InvalidArgumentException If required environment variables are missing
     */
    public static function fromEnvironment(array $additionalOptions = []): self
    {
        $envOptions = [
            'bearerToken' => $_ENV['FREEMIUS_BEARER_TOKEN'] ?? $_SERVER['FREEMIUS_BEARER_TOKEN'] ?? null,
            'sandbox' => self::parseBooleanEnv($_ENV['FREEMIUS_SANDBOX'] ?? $_SERVER['FREEMIUS_SANDBOX'] ?? null),
            'baseUrl' => $_ENV['FREEMIUS_BASE_URL'] ?? $_SERVER['FREEMIUS_BASE_URL'] ?? null,
            'timeout' => self::parseIntegerEnv($_ENV['FREEMIUS_TIMEOUT'] ?? $_SERVER['FREEMIUS_TIMEOUT'] ?? null),
            'retryAttempts' => self::parseIntegerEnv($_ENV['FREEMIUS_RETRY_ATTEMPTS'] ?? $_SERVER['FREEMIUS_RETRY_ATTEMPTS'] ?? null),
            'logging' => self::parseBooleanEnv($_ENV['FREEMIUS_LOGGING'] ?? $_SERVER['FREEMIUS_LOGGING'] ?? null),
            'logLevel' => $_ENV['FREEMIUS_LOG_LEVEL'] ?? $_SERVER['FREEMIUS_LOG_LEVEL'] ?? null,
            'logFile' => $_ENV['FREEMIUS_LOG_FILE'] ?? $_SERVER['FREEMIUS_LOG_FILE'] ?? null,
            'productScope' => self::parseIntegerEnv($_ENV['FREEMIUS_PRODUCT_SCOPE'] ?? $_SERVER['FREEMIUS_PRODUCT_SCOPE'] ?? null),
            'rateLimitMaxRequests' => self::parseIntegerEnv($_ENV['FREEMIUS_RATE_LIMIT_MAX_REQUESTS'] ?? $_SERVER['FREEMIUS_RATE_LIMIT_MAX_REQUESTS'] ?? null),
            'rateLimitWindowSeconds' => self::parseIntegerEnv($_ENV['FREEMIUS_RATE_LIMIT_WINDOW_SECONDS'] ?? $_SERVER['FREEMIUS_RATE_LIMIT_WINDOW_SECONDS'] ?? null),
            'rateLimitBackoffStrategy' => $_ENV['FREEMIUS_RATE_LIMIT_BACKOFF_STRATEGY'] ?? $_SERVER['FREEMIUS_RATE_LIMIT_BACKOFF_STRATEGY'] ?? null,
            'cacheEnabled' => self::parseBooleanEnv($_ENV['FREEMIUS_CACHE_ENABLED'] ?? $_SERVER['FREEMIUS_CACHE_ENABLED'] ?? null),
            'cacheTtl' => self::parseIntegerEnv($_ENV['FREEMIUS_CACHE_TTL'] ?? $_SERVER['FREEMIUS_CACHE_TTL'] ?? null),
            'cacheMaxSize' => self::parseIntegerEnv($_ENV['FREEMIUS_CACHE_MAX_SIZE'] ?? $_SERVER['FREEMIUS_CACHE_MAX_SIZE'] ?? null),
        ];

        // Remove null values to allow defaults to take effect
        $envOptions = array_filter($envOptions, function ($value) {
            return $value !== null;
        });

        // Merge with additional options (additional options take precedence)
        $options = array_merge($envOptions, $additionalOptions);

        return new self($options);
    }



    /**
     * Enable sandbox mode
     *
     * @param bool $sandbox Whether to use sandbox mode
     * @return self
     */
    public function useSandbox(bool $sandbox = true): self
    {
        $this->sandbox = $sandbox;
        $this->baseUrl = $sandbox ? self::DEFAULTS['sandboxUrl'] : self::DEFAULTS['baseUrl'];
        
        return $this;
    }

    /**
     * Set request timeout
     *
     * @param int $seconds Timeout in seconds
     * @return self
     * @throws InvalidArgumentException If timeout is invalid
     */
    public function setTimeout(int $seconds): self
    {
        if ($seconds <= 0) {
            throw new InvalidArgumentException('Timeout must be greater than 0');
        }
        
        $this->timeout = $seconds;
        
        return $this;
    }

    /**
     * Enable logging with optional level
     *
     * @param string $level Log level
     * @return self
     * @throws InvalidArgumentException If log level is invalid
     */
    public function enableLogging(string $level = 'info'): self
    {
        if (!in_array($level, self::VALID_LOG_LEVELS, true)) {
            throw new InvalidArgumentException(sprintf('Invalid log level "%s". Valid levels: %s', $level, implode(', ', self::VALID_LOG_LEVELS)));
        }
        
        $this->logging = true;
        $this->logLevel = $level;
        
        return $this;
    }

    /**
     * Disable logging
     *
     * @return self
     */
    public function disableLogging(): self
    {
        $this->logging = false;
        
        return $this;
    }

    /**
     * Set product scope
     *
     * @param int|null $productId Product ID to scope requests to
     * @return self
     * @throws InvalidArgumentException If product ID is invalid
     */
    public function setProductScope(?int $productId): self
    {
        if ($productId !== null && $productId <= 0) {
            throw new InvalidArgumentException('Product scope must be a positive integer or null');
        }
        
        $this->productScope = $productId;
        
        return $this;
    }

    /**
     * Clear product scope
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->productScope = null;

        return $this;
    }

    /**
     * Set log file path
     *
     * @param string|null $logFile Path to log file, or null to disable file logging
     * @return self
     */
    public function setLogFile(?string $logFile): self
    {
        $this->logFile = $logFile;

        return $this;
    }

    /**
     * Set rate limiting configuration
     *
     * @param int $maxRequests Maximum requests per time window
     * @param int $windowSeconds Time window in seconds
     * @param string $backoffStrategy Backoff strategy ('linear', 'exponential', 'fixed')
     * @return self
     * @throws InvalidArgumentException If parameters are invalid
     */
    public function setRateLimit(int $maxRequests, int $windowSeconds, string $backoffStrategy = 'exponential'): self
    {
        if ($maxRequests <= 0) {
            throw new InvalidArgumentException('Max requests must be greater than 0');
        }

        if ($windowSeconds <= 0) {
            throw new InvalidArgumentException('Window seconds must be greater than 0');
        }

        if (!in_array($backoffStrategy, self::VALID_BACKOFF_STRATEGIES, true)) {
            throw new InvalidArgumentException(sprintf('Invalid backoff strategy "%s". Valid strategies: %s', $backoffStrategy, implode(', ', self::VALID_BACKOFF_STRATEGIES)));
        }

        $this->rateLimitMaxRequests = $maxRequests;
        $this->rateLimitWindowSeconds = $windowSeconds;
        $this->rateLimitBackoffStrategy = $backoffStrategy;

        return $this;
    }

    /**
     * Set retry attempts
     *
     * @param int $attempts Number of retry attempts
     * @return self
     * @throws InvalidArgumentException If attempts is invalid
     */
    public function setRetryAttempts(int $attempts): self
    {
        if ($attempts < 0) {
            throw new InvalidArgumentException('Retry attempts must be 0 or greater');
        }

        $this->retryAttempts = $attempts;

        return $this;
    }

    /**
     * Set cache configuration
     *
     * @param bool $enabled Whether caching is enabled
     * @param int|null $ttl Cache TTL in seconds
     * @param int|null $maxSize Maximum cache size
     * @return self
     * @throws InvalidArgumentException If parameters are invalid
     */
    public function setCacheConfig(bool $enabled, ?int $ttl = null, ?int $maxSize = null): self
    {
        $this->cacheEnabled = $enabled;

        if ($ttl !== null) {
            if ($ttl <= 0) {
                throw new InvalidArgumentException('Cache TTL must be greater than 0');
            }
            $this->cacheTtl = $ttl;
        }

        if ($maxSize !== null) {
            if ($maxSize <= 0) {
                throw new InvalidArgumentException('Cache max size must be greater than 0');
            }
            $this->cacheMaxSize = $maxSize;
        }

        return $this;
    }

    /**
     * Enable caching
     *
     * @return self
     */
    public function enableCache(): self
    {
        $this->cacheEnabled = true;
        return $this;
    }

    /**
     * Disable caching
     *
     * @return self
     */
    public function disableCache(): self
    {
        $this->cacheEnabled = false;
        return $this;
    }

    /**
     * Check if product scope is set
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->productScope !== null;
    }

    // Getters
    public function getBearerToken(): string
    {
        return $this->bearerToken;
    }

    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function isSandbox(): bool
    {
        return $this->sandbox;
    }

    public function getTimeout(): int
    {
        return $this->timeout;
    }

    public function getRetryAttempts(): int
    {
        return $this->retryAttempts;
    }

    public function isLoggingEnabled(): bool
    {
        return $this->logging;
    }

    public function getLogLevel(): string
    {
        return $this->logLevel;
    }

    public function getProductScope(): ?int
    {
        return $this->productScope;
    }

    public function getLogFile(): ?string
    {
        return $this->logFile;
    }

    public function getRateLimitMaxRequests(): int
    {
        return $this->rateLimitMaxRequests;
    }

    public function getRateLimitWindowSeconds(): int
    {
        return $this->rateLimitWindowSeconds;
    }

    public function getRateLimitBackoffStrategy(): string
    {
        return $this->rateLimitBackoffStrategy;
    }

    public function isCacheEnabled(): bool
    {
        return $this->cacheEnabled;
    }

    public function getCacheTtl(): int
    {
        return $this->cacheTtl;
    }

    public function getCacheMaxSize(): int
    {
        return $this->cacheMaxSize;
    }

    /**
     * Validate required configuration options
     *
     * @param array $options
     * @throws InvalidArgumentException
     */
    private function validateRequiredOptions(array $options): void
    {
        if (empty($options['bearerToken'])) {
            throw new InvalidArgumentException(
                'Bearer token is required. Set it via options array, environment variable FREEMIUS_BEARER_TOKEN, or use Configuration::fromEnvironment()'
            );
        }

        if (!is_string($options['bearerToken'])) {
            throw new InvalidArgumentException('Bearer token must be a string');
        }

        if (strlen(trim($options['bearerToken'])) === 0) {
            throw new InvalidArgumentException('Bearer token cannot be empty');
        }
    }

    /**
     * Validate all configuration options with detailed error messages
     *
     * @param array $options Configuration options to validate
     * @return array Validation errors (empty if valid)
     */
    public static function validateOptions(array $options): array
    {
        $errors = [];

        // Validate bearer token
        if (empty($options['bearerToken'])) {
            $errors['bearerToken'] = 'Bearer token is required';
        } elseif (!is_string($options['bearerToken'])) {
            $errors['bearerToken'] = 'Bearer token must be a string';
        } elseif (strlen(trim($options['bearerToken'])) === 0) {
            $errors['bearerToken'] = 'Bearer token cannot be empty';
        }

        // Validate sandbox
        if (isset($options['sandbox']) && !is_bool($options['sandbox'])) {
            $errors['sandbox'] = 'Sandbox must be a boolean value';
        }

        // Validate base URL
        if (isset($options['baseUrl'])) {
            if (!is_string($options['baseUrl'])) {
                $errors['baseUrl'] = 'Base URL must be a string';
            } elseif (!filter_var($options['baseUrl'], FILTER_VALIDATE_URL)) {
                $errors['baseUrl'] = 'Base URL must be a valid URL';
            } elseif (!str_starts_with($options['baseUrl'], 'https://')) {
                $errors['baseUrl'] = 'Base URL must use HTTPS protocol';
            }
        }

        // Validate timeout
        if (isset($options['timeout'])) {
            if (!is_int($options['timeout'])) {
                $errors['timeout'] = 'Timeout must be an integer';
            } elseif ($options['timeout'] <= 0) {
                $errors['timeout'] = 'Timeout must be greater than 0 seconds';
            } elseif ($options['timeout'] > 300) {
                $errors['timeout'] = 'Timeout should not exceed 300 seconds (5 minutes)';
            }
        }

        // Validate retry attempts
        if (isset($options['retryAttempts'])) {
            if (!is_int($options['retryAttempts'])) {
                $errors['retryAttempts'] = 'Retry attempts must be an integer';
            } elseif ($options['retryAttempts'] < 0) {
                $errors['retryAttempts'] = 'Retry attempts must be 0 or greater';
            } elseif ($options['retryAttempts'] > 10) {
                $errors['retryAttempts'] = 'Retry attempts should not exceed 10';
            }
        }

        // Validate logging
        if (isset($options['logging']) && !is_bool($options['logging'])) {
            $errors['logging'] = 'Logging must be a boolean value';
        }

        // Validate log level
        if (isset($options['logLevel'])) {
            if (!is_string($options['logLevel'])) {
                $errors['logLevel'] = 'Log level must be a string';
            } elseif (!in_array($options['logLevel'], self::VALID_LOG_LEVELS, true)) {
                $errors['logLevel'] = sprintf(
                    'Invalid log level "%s". Valid levels: %s',
                    $options['logLevel'],
                    implode(', ', self::VALID_LOG_LEVELS)
                );
            }
        }

        // Validate log file
        if (isset($options['logFile']) && $options['logFile'] !== null) {
            if (!is_string($options['logFile'])) {
                $errors['logFile'] = 'Log file must be a string or null';
            } else {
                $logDir = dirname($options['logFile']);
                if (!is_dir($logDir) && !mkdir($logDir, 0755, true)) {
                    $errors['logFile'] = sprintf('Cannot create log directory: %s', $logDir);
                } elseif (is_dir($logDir) && !is_writable($logDir)) {
                    $errors['logFile'] = sprintf('Log directory is not writable: %s', $logDir);
                }
            }
        }

        // Validate product scope
        if (isset($options['productScope']) && $options['productScope'] !== null) {
            if (!is_int($options['productScope'])) {
                $errors['productScope'] = 'Product scope must be an integer or null';
            } elseif ($options['productScope'] <= 0) {
                $errors['productScope'] = 'Product scope must be a positive integer';
            }
        }

        // Validate rate limiting options
        if (isset($options['rateLimitMaxRequests'])) {
            if (!is_int($options['rateLimitMaxRequests'])) {
                $errors['rateLimitMaxRequests'] = 'Rate limit max requests must be an integer';
            } elseif ($options['rateLimitMaxRequests'] <= 0) {
                $errors['rateLimitMaxRequests'] = 'Rate limit max requests must be greater than 0';
            } elseif ($options['rateLimitMaxRequests'] > 10000) {
                $errors['rateLimitMaxRequests'] = 'Rate limit max requests should not exceed 10,000';
            }
        }

        if (isset($options['rateLimitWindowSeconds'])) {
            if (!is_int($options['rateLimitWindowSeconds'])) {
                $errors['rateLimitWindowSeconds'] = 'Rate limit window seconds must be an integer';
            } elseif ($options['rateLimitWindowSeconds'] <= 0) {
                $errors['rateLimitWindowSeconds'] = 'Rate limit window seconds must be greater than 0';
            } elseif ($options['rateLimitWindowSeconds'] > 3600) {
                $errors['rateLimitWindowSeconds'] = 'Rate limit window seconds should not exceed 3600 (1 hour)';
            }
        }

        if (isset($options['rateLimitBackoffStrategy'])) {
            if (!is_string($options['rateLimitBackoffStrategy'])) {
                $errors['rateLimitBackoffStrategy'] = 'Rate limit backoff strategy must be a string';
            } elseif (!in_array($options['rateLimitBackoffStrategy'], self::VALID_BACKOFF_STRATEGIES, true)) {
                $errors['rateLimitBackoffStrategy'] = sprintf(
                    'Invalid backoff strategy "%s". Valid strategies: %s',
                    $options['rateLimitBackoffStrategy'],
                    implode(', ', self::VALID_BACKOFF_STRATEGIES)
                );
            }
        }

        return $errors;
    }

    /**
     * Validate configuration values
     *
     * @throws InvalidArgumentException
     */
    private function validateConfiguration(): void
    {
        if ($this->timeout <= 0) {
            throw new InvalidArgumentException('Timeout must be greater than 0');
        }

        if ($this->retryAttempts < 0) {
            throw new InvalidArgumentException('Retry attempts must be 0 or greater');
        }

        if (!in_array($this->logLevel, self::VALID_LOG_LEVELS, true)) {
            throw new InvalidArgumentException(sprintf('Invalid log level "%s"', $this->logLevel));
        }

        if ($this->productScope !== null && $this->productScope <= 0) {
            throw new InvalidArgumentException('Product scope must be a positive integer');
        }

        if ($this->rateLimitMaxRequests <= 0) {
            throw new InvalidArgumentException('Rate limit max requests must be greater than 0');
        }

        if ($this->rateLimitWindowSeconds <= 0) {
            throw new InvalidArgumentException('Rate limit window seconds must be greater than 0');
        }

        if (!in_array($this->rateLimitBackoffStrategy, self::VALID_BACKOFF_STRATEGIES, true)) {
            throw new InvalidArgumentException(sprintf('Invalid rate limit backoff strategy "%s"', $this->rateLimitBackoffStrategy));
        }

        if ($this->logFile !== null && !is_string($this->logFile)) {
            throw new InvalidArgumentException('Log file must be a string or null');
        }

        if ($this->cacheTtl <= 0) {
            throw new InvalidArgumentException('Cache TTL must be greater than 0');
        }

        if ($this->cacheMaxSize <= 0) {
            throw new InvalidArgumentException('Cache max size must be greater than 0');
        }
    }

    /**
     * Parse boolean environment variable
     *
     * @param string|null $value Environment variable value
     * @return bool|null
     */
    private static function parseBooleanEnv(?string $value): ?bool
    {
        if ($value === null) {
            return null;
        }

        $value = strtolower(trim($value));

        if (in_array($value, ['true', '1', 'yes', 'on'], true)) {
            return true;
        }

        if (in_array($value, ['false', '0', 'no', 'off'], true)) {
            return false;
        }

        return null;
    }

    /**
     * Parse integer environment variable
     *
     * @param string|null $value Environment variable value
     * @return int|null
     */
    private static function parseIntegerEnv(?string $value): ?int
    {
        if ($value === null || trim($value) === '') {
            return null;
        }

        $parsed = filter_var(trim($value), FILTER_VALIDATE_INT);
        return $parsed !== false ? $parsed : null;
    }

    /**
     * Get configuration help and suggestions
     *
     * @return array Configuration help information
     */
    public static function getConfigurationHelp(): array
    {
        return [
            'bearerToken' => [
                'description' => 'Your Freemius API bearer token',
                'required' => true,
                'type' => 'string',
                'example' => 'sk_live_...',
                'env_var' => 'FREEMIUS_BEARER_TOKEN',
                'help' => 'Get your bearer token from the Freemius Developer Dashboard'
            ],
            'sandbox' => [
                'description' => 'Use sandbox/testing environment',
                'required' => false,
                'type' => 'boolean',
                'default' => false,
                'env_var' => 'FREEMIUS_SANDBOX',
                'help' => 'Set to true for testing, false for production'
            ],
            'baseUrl' => [
                'description' => 'Custom API base URL',
                'required' => false,
                'type' => 'string',
                'default' => 'https://api.freemius.com/v1/',
                'env_var' => 'FREEMIUS_BASE_URL',
                'help' => 'Override the default API endpoint URL'
            ],
            'timeout' => [
                'description' => 'Request timeout in seconds',
                'required' => false,
                'type' => 'integer',
                'default' => 30,
                'range' => '1-300',
                'env_var' => 'FREEMIUS_TIMEOUT',
                'help' => 'How long to wait for API responses'
            ],
            'retryAttempts' => [
                'description' => 'Number of retry attempts for failed requests',
                'required' => false,
                'type' => 'integer',
                'default' => 3,
                'range' => '0-10',
                'env_var' => 'FREEMIUS_RETRY_ATTEMPTS',
                'help' => 'Automatic retries for network errors and server errors'
            ],
            'logging' => [
                'description' => 'Enable request/response logging',
                'required' => false,
                'type' => 'boolean',
                'default' => false,
                'env_var' => 'FREEMIUS_LOGGING',
                'help' => 'Log all API requests and responses for debugging'
            ],
            'logLevel' => [
                'description' => 'Logging level',
                'required' => false,
                'type' => 'string',
                'default' => 'info',
                'options' => self::VALID_LOG_LEVELS,
                'env_var' => 'FREEMIUS_LOG_LEVEL',
                'help' => 'Control verbosity of logs'
            ],
            'logFile' => [
                'description' => 'Path to log file',
                'required' => false,
                'type' => 'string',
                'default' => null,
                'example' => '/var/log/freemius-sdk.log',
                'env_var' => 'FREEMIUS_LOG_FILE',
                'help' => 'Specify custom log file location'
            ],
            'productScope' => [
                'description' => 'Default product ID for scoped requests',
                'required' => false,
                'type' => 'integer',
                'default' => null,
                'env_var' => 'FREEMIUS_PRODUCT_SCOPE',
                'help' => 'Automatically scope all requests to a specific product'
            ],
            'rateLimitMaxRequests' => [
                'description' => 'Maximum requests per time window',
                'required' => false,
                'type' => 'integer',
                'default' => 100,
                'range' => '1-10000',
                'env_var' => 'FREEMIUS_RATE_LIMIT_MAX_REQUESTS',
                'help' => 'Rate limiting configuration'
            ],
            'rateLimitWindowSeconds' => [
                'description' => 'Rate limit time window in seconds',
                'required' => false,
                'type' => 'integer',
                'default' => 60,
                'range' => '1-3600',
                'env_var' => 'FREEMIUS_RATE_LIMIT_WINDOW_SECONDS',
                'help' => 'Time window for rate limiting'
            ],
            'rateLimitBackoffStrategy' => [
                'description' => 'Backoff strategy for rate limit retries',
                'required' => false,
                'type' => 'string',
                'default' => 'exponential',
                'options' => self::VALID_BACKOFF_STRATEGIES,
                'env_var' => 'FREEMIUS_RATE_LIMIT_BACKOFF_STRATEGY',
                'help' => 'How to handle rate limit delays'
            ]
        ];
    }

    /**
     * Get environment variable examples
     *
     * @return array Environment variable examples
     */
    public static function getEnvironmentVariableExamples(): array
    {
        return [
            'FREEMIUS_BEARER_TOKEN=sk_live_your_token_here',
            'FREEMIUS_SANDBOX=false',
            'FREEMIUS_TIMEOUT=30',
            'FREEMIUS_RETRY_ATTEMPTS=3',
            'FREEMIUS_LOGGING=true',
            'FREEMIUS_LOG_LEVEL=info',
            'FREEMIUS_LOG_FILE=/var/log/freemius-sdk.log',
            'FREEMIUS_PRODUCT_SCOPE=12345',
            'FREEMIUS_RATE_LIMIT_MAX_REQUESTS=100',
            'FREEMIUS_RATE_LIMIT_WINDOW_SECONDS=60',
            'FREEMIUS_RATE_LIMIT_BACKOFF_STRATEGY=exponential'
        ];
    }
}