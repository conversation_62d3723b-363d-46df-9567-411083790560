<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\User;

/**
 * Review webhook event
 * 
 * Triggered for review-related events like creation, updates, deletion, etc.
 */
class ReviewEvent extends WebhookEvent
{
    private ?User $user = null;

    /**
     * Get the user associated with this review
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the review ID
     *
     * @return int|null The review ID
     */
    public function getReviewId(): ?int
    {
        $reviewId = $this->get('review_id');
        return $reviewId ? (int) $reviewId : null;
    }

    /**
     * Get the review rating
     *
     * @return int|null The review rating (1-5)
     */
    public function getRating(): ?int
    {
        $rating = $this->getNestedData('review.rating') ?? $this->get('rating');
        return $rating ? (int) $rating : null;
    }

    /**
     * Get the review text
     *
     * @return string|null The review text
     */
    public function getReviewText(): ?string
    {
        return $this->getNestedData('review.text') ?? $this->get('review_text');
    }

    /**
     * Get the review title
     *
     * @return string|null The review title
     */
    public function getReviewTitle(): ?string
    {
        return $this->getNestedData('review.title') ?? $this->get('review_title');
    }

    /**
     * Check if this is a review creation
     *
     * @return bool True if review was created
     */
    public function isCreation(): bool
    {
        return $this->eventType === 'review.created';
    }

    /**
     * Check if this is a review update
     *
     * @return bool True if review was updated
     */
    public function isUpdate(): bool
    {
        return $this->eventType === 'review.updated';
    }

    /**
     * Check if this is a review deletion
     *
     * @return bool True if review was deleted
     */
    public function isDeletion(): bool
    {
        return $this->eventType === 'review.deleted';
    }

    /**
     * Check if this is a review request
     *
     * @return bool True if review was requested
     */
    public function isRequest(): bool
    {
        return $this->eventType === 'review.requested';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'review.created' => 'created',
            'review.deleted' => 'deleted',
            'review.requested' => 'requested',
            'review.updated' => 'updated',
            default => 'changed'
        };

        $parts = ["Review {$action}"];

        if ($reviewId = $this->getReviewId()) {
            $parts[] = "review:{$reviewId}";
        }

        if ($rating = $this->getRating()) {
            $parts[] = "rating:{$rating}/5";
        }

        return implode(' ', $parts);
    }
}
