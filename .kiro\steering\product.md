# Product Overview

## Freemius PHP SDK - Unofficial but Refactored and Modernized

This is a modernized, unofficial PHP SDK for the Freemius platform that provides seamless integration with Freemius REST API - ONLY the REST API.

### Key Features
- Full method chaining support
- Bearer token authentication
- Webhook support for real-time events
- Complete coverage of all Freemius API operations
- Production-grade, commercial-ready implementation

### Target Audience
- WordPress plugin/theme developers
- SaaS developers using Freemius for licensing
- Developers needing programmatic access to Freemius services

### API Endpoints
- Production: `https://api.freemius.com/v1/`
- Sandbox/Mock: `https://docs.freemius.com/_mock/api/`

### License
GPL-2.0+ - Open source but with commercial-grade quality standards