<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Integration;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\FreemiusSDK;
use Freemius\SDK\Configuration;
use Freemius\SDK\Authentication\BearerAuth;
use Freemius\SDK\Exceptions\AuthenticationException;
use Freemius\SDK\Exceptions\NotFoundException;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Integration tests for FreemiusSDK using sandbox API
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\FreemiusSDK::class)]
#[\PHPUnit\Framework\Attributes\Group('integration')]
class FreemiusSDKIntegrationTest extends TestCase
{
    private FreemiusSDK $sdk;
    private Configuration $config;

    protected function setUp(): void
    {
        // Skip integration tests if no bearer token is provided
        $bearerToken = $_ENV['FREEMIUS_TEST_BEARER_TOKEN'] ?? '';
        if (empty($bearerToken)) {
            $this->markTestSkipped('FREEMIUS_TEST_BEARER_TOKEN environment variable not set');
        }

        // Configure SDK for sandbox environment
        $this->config = new Configuration([
            'bearerToken' => $bearerToken,
            'sandbox' => true,
            'timeout' => 30,
            'retryAttempts' => 2,
            'logging' => false,
            'productScope' => 1, // Use test product ID for sandbox
        ]);

        $this->sdk = new FreemiusSDK($this->config);
    }

    public function testSdkInitialization(): void
    {
        $this->assertInstanceOf(FreemiusSDK::class, $this->sdk);
    }

    public function testAuthenticationWithValidToken(): void
    {
        // This test will pass if setUp() doesn't throw an exception
        $this->assertTrue(true);
    }

    public function testAuthenticationWithInvalidToken(): void
    {
        $invalidConfig = new Configuration([
            'bearerToken' => 'invalid_token_12345',
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $invalidSdk = new FreemiusSDK($invalidConfig);

        $this->expectException(AuthenticationException::class);
        
        // Try to make a request that should fail with invalid token
        $invalidSdk->products()->all();
    }

    public function testProductsListEndpoint(): void
    {
        $products = $this->sdk->products()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
        // Sandbox may have 0 or more products
        $this->assertGreaterThanOrEqual(0, $products->count());
    }

    public function testProductsGetEndpoint(): void
    {
        // First get a list of products to find a valid ID
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            $product = $this->sdk->products()->get($productId);
            
            $this->assertInstanceOf(\Freemius\SDK\Entities\Product::class, $product);
            $this->assertEquals($productId, $product->getId());
        } else {
            $this->markTestSkipped('No products available in sandbox for testing');
        }
    }

    public function testProductNotFound(): void
    {
        $this->expectException(NotFoundException::class);
        
        // Try to get a product that doesn't exist
        $this->sdk->products()->get(999999);
    }

    public function testUsersListEndpoint(): void
    {
        $users = $this->sdk->users()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        $this->assertGreaterThanOrEqual(0, $users->count());
    }

    public function testLicensesListEndpoint(): void
    {
        $licenses = $this->sdk->licenses()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);
        $this->assertGreaterThanOrEqual(0, $licenses->count());
    }

    public function testInstallationsListEndpoint(): void
    {
        $installations = $this->sdk->installations()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $installations);
        $this->assertGreaterThanOrEqual(0, $installations->count());
    }

    public function testSubscriptionsListEndpoint(): void
    {
        $subscriptions = $this->sdk->subscriptions()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $subscriptions);
        $this->assertGreaterThanOrEqual(0, $subscriptions->count());
    }

    public function testPaymentsListEndpoint(): void
    {
        $payments = $this->sdk->payments()->all();
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $payments);
        $this->assertGreaterThanOrEqual(0, $payments->count());
    }

    public function testPaginationHandling(): void
    {
        $products = $this->sdk->products()->all(['limit' => 2]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
        
        // Test pagination metadata
        $pagination = $products->getPagination();
        $this->assertIsArray($pagination);
        
        if ($products->count() > 0) {
            // Test that we can access pagination methods
            $this->assertIsInt($products->count());
            $this->assertIsBool($products->isEmpty());
            $this->assertIsBool($products->isNotEmpty());
        }
    }

    public function testFilteringAndSearchParameters(): void
    {
        // Test with search parameters
        $products = $this->sdk->products()->all([
            'limit' => 10,
            'offset' => 0,
        ]);
        
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
        $this->assertLessThanOrEqual(10, $products->count());
    }

    public function testProductScopeManagement(): void
    {
        // Get a product to use for scoping
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            // Set product scope
            $scopedSdk = $this->sdk->setProductScope($productId);
            
            $this->assertSame($this->sdk, $scopedSdk); // Should return same instance
            
            // Test that scoped requests work
            $users = $scopedSdk->users()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        } else {
            $this->markTestSkipped('No products available for scope testing');
        }
    }

    public function testErrorHandlingWithInvalidParameters(): void
    {
        $this->expectException(ValidationException::class);
        
        // Try to create something with invalid data
        $this->sdk->products()->create([
            'invalid_field' => 'invalid_value',
        ]);
    }

    public function testRateLimitingHandling(): void
    {
        // Make multiple requests quickly to test rate limiting
        $requestCount = 5;
        $responses = [];
        
        for ($i = 0; $i < $requestCount; $i++) {
            try {
                $response = $this->sdk->products()->all(['limit' => 1]);
                $responses[] = $response;
            } catch (\Exception $e) {
                // Rate limiting or other errors are acceptable in this test
                $this->assertInstanceOf(\Exception::class, $e);
            }
        }
        
        // At least some requests should succeed
        $this->assertGreaterThan(0, count($responses));
    }

    public function testCompleteWorkflow(): void
    {
        // Test a complete workflow: get products, then get users for a product
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $firstProduct = $products->first();
            $productId = $firstProduct->getId();
            
            // Get product details
            $product = $this->sdk->products()->get($productId);
            $this->assertInstanceOf(\Freemius\SDK\Entities\Product::class, $product);
            
            // Set scope and get users
            $this->sdk->setProductScope($productId);
            $users = $this->sdk->users()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
            
            // Get licenses for the product
            $licenses = $this->sdk->licenses()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $licenses);
            
            // Get installations for the product
            $installations = $this->sdk->installations()->all();
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $installations);
        } else {
            $this->markTestSkipped('No products available for workflow testing');
        }
    }

    public function testHttpMethodsAndEndpoints(): void
    {
        // Test different HTTP methods are working
        
        // GET requests
        $products = $this->sdk->products()->all();
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
        
        $users = $this->sdk->users()->all();
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        
        // Test that the SDK can handle different response formats
        if ($products->count() > 0) {
            $product = $this->sdk->products()->get($products->first()->getId());
            $this->assertInstanceOf(\Freemius\SDK\Entities\Product::class, $product);
        }
    }

    public function testConfigurationOptions(): void
    {
        // Test that configuration options are working
        $this->assertTrue($this->config->isSandbox());
        $this->assertEquals(30, $this->config->getTimeout());
        $this->assertEquals(2, $this->config->getRetryAttempts());
        $this->assertFalse($this->config->isLoggingEnabled());
    }

    public function testEntityDataIntegrity(): void
    {
        $products = $this->sdk->products()->all();
        
        if ($products->count() > 0) {
            $product = $products->first();
            
            // Test that entity has expected methods and data
            $this->assertIsInt($product->getId());
            $this->assertIsString($product->getTitle());
            $this->assertIsString($product->getSlug());
            $this->assertIsString($product->getType());
            $this->assertIsBool($product->isReleased());
            $this->assertInstanceOf(\DateTime::class, $product->getCreatedAt());
            
            // Test that entity can be converted to array and JSON
            $array = $product->toArray();
            $this->assertIsArray($array);
            $this->assertArrayHasKey('id', $array);
            
            $json = $product->toJson();
            $this->assertJson($json);
        } else {
            $this->markTestSkipped('No products available for entity testing');
        }
    }
}
