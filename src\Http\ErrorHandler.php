<?php

declare(strict_types=1);

namespace <PERSON><PERSON>us\SDK\Http;

use <PERSON>mius\SDK\Exceptions\AuthenticationException;
use Freemius\SDK\Exceptions\AuthorizationException;
use Freemius\SDK\Exceptions\FreemiusException;
use Freemius\SDK\Exceptions\NetworkException;
use Freemius\SDK\Exceptions\NotFoundException;
use Freemius\SDK\Exceptions\RateLimitException;
use Freemius\SDK\Exceptions\ServerException;
use Freemius\SDK\Exceptions\ValidationException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\RequestInterface;

/**
 * Error handler for mapping API responses to exceptions
 * 
 * Analyzes HTTP responses and request exceptions to create
 * appropriate SDK exceptions with relevant context.
 */
class ErrorHandler
{
    /**
     * Handle a Guzzle request exception and convert to SDK exception
     *
     * @param RequestException $exception The Guzzle exception
     * @return FreemiusException The converted SDK exception
     */
    public static function handleRequestException(RequestException $exception): FreemiusException
    {
        $request = $exception->getRequest();
        $response = $exception->getResponse();
        
        $context = [
            'method' => $request->getMethod(),
            'uri' => (string) $request->getUri(),
            'guzzle_message' => $exception->getMessage(),
        ];

        // If we have a response, analyze it
        if ($response !== null) {
            return self::handleResponse($response, $context, $exception);
        }

        // Handle network/connection errors without response
        return self::handleNetworkException($exception, $context);
    }

    /**
     * Handle network exceptions (no response received)
     *
     * @param RequestException $exception The Guzzle exception
     * @param array $context Additional context
     * @return NetworkException The network exception
     */
    private static function handleNetworkException(
        RequestException $exception,
        array $context = []
    ): NetworkException {
        $message = $exception->getMessage();
        $uri = $context['uri'] ?? '';
        $host = parse_url($uri, PHP_URL_HOST) ?? '';

        // Handle specific connection exceptions
        if ($exception instanceof ConnectException) {
            // Check for specific connection error types
            if (strpos($message, 'timed out') !== false) {
                return NetworkException::connectionTimeout(0, $host, $context);
            }
            
            if (strpos($message, 'Connection refused') !== false) {
                return NetworkException::connectionRefused($host, 0, $context);
            }
            
            if (strpos($message, 'Could not resolve host') !== false) {
                return NetworkException::dnsResolutionFailed($host, $context);
            }
            
            if (strpos($message, 'SSL') !== false || strpos($message, 'certificate') !== false) {
                return NetworkException::sslCertificateError($host, $message, $context);
            }
        }

        // Generic network error
        return NetworkException::connectivityIssue($message, $context);
    }

    /**
     * Handle an HTTP response and create appropriate exception
     *
     * @param ResponseInterface $response The HTTP response
     * @param array $context Additional context
     * @param RequestException|null $originalException Original Guzzle exception
     * @return FreemiusException The appropriate SDK exception
     */
    public static function handleResponse(
        ResponseInterface $response,
        array $context = [],
        ?RequestException $originalException = null
    ): FreemiusException {
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $response->getBody()->rewind(); // Reset stream position
        
        // Try to parse JSON error response
        $errorData = self::parseErrorResponse($body);
        
        $context = array_merge($context, [
            'status_code' => $statusCode,
            'response_body' => $body,
            'error_data' => $errorData,
        ]);

        // Determine exception type based on status code
        switch ($statusCode) {
            case 400:
                return self::createValidationException($errorData, $context, $originalException);
                
            case 401:
                return self::createAuthenticationException($errorData, $context, $originalException);
                
            case 403:
                return self::createAuthorizationException($errorData, $context, $originalException);
                
            case 404:
                return self::createNotFoundException($errorData, $context, $originalException);
                
            case 422:
                return self::createValidationException($errorData, $context, $originalException);
                
            case 429:
                return self::createRateLimitException($response, $errorData, $context, $originalException);
                
            case 500:
            case 502:
            case 503:
            case 504:
                return ServerException::fromStatusCode(
                    $statusCode,
                    $errorData['message'] ?? '',
                    $context
                );
                
            default:
                // Handle other 5xx errors as server exceptions
                if ($statusCode >= 500) {
                    return ServerException::fromStatusCode(
                        $statusCode,
                        $errorData['message'] ?? '',
                        $context
                    );
                }
                
                // Generic client error
                return new FreemiusException(
                    $errorData['message'] ?? "HTTP error {$statusCode}",
                    $statusCode,
                    $originalException,
                    $context
                );
        }
    }

    /**
     * Create an authentication exception based on error data
     *
     * @param array $errorData Parsed error response data
     * @param array $context Additional context
     * @param RequestException|null $originalException Original exception
     * @return AuthenticationException
     */
    private static function createAuthenticationException(
        array $errorData,
        array $context,
        ?RequestException $originalException = null
    ): AuthenticationException {
        $message = $errorData['message'] ?? 'Authentication failed';
        $errorCode = $errorData['code'] ?? '';
        
        // Detect specific authentication error types
        if (self::isTokenExpiredError($message, $errorCode)) {
            return AuthenticationException::expiredToken($context);
        }
        
        if (self::isInvalidTokenError($message, $errorCode)) {
            return AuthenticationException::invalidBearerToken('', $context);
        }
        
        if (self::isMissingAuthError($message, $errorCode)) {
            return AuthenticationException::missingAuthentication($context);
        }
        
        // Generic authentication error
        return new AuthenticationException($message, 401, $originalException, $context);
    }

    /**
     * Create an authorization exception based on error data
     *
     * @param array $errorData Parsed error response data
     * @param array $context Additional context
     * @param RequestException|null $originalException Original exception
     * @return AuthorizationException
     */
    private static function createAuthorizationException(
        array $errorData,
        array $context,
        ?RequestException $originalException = null
    ): AuthorizationException {
        $message = $errorData['message'] ?? 'Access denied';
        $resource = $errorData['resource'] ?? '';
        $action = $errorData['action'] ?? '';

        if ($resource || $action) {
            return AuthorizationException::insufficientPermissions($resource, $action, $context);
        }

        return new AuthorizationException($message, 403, $originalException, $context);
    }

    /**
     * Create a not found exception based on error data
     *
     * @param array $errorData Parsed error response data
     * @param array $context Additional context
     * @param RequestException|null $originalException Original exception
     * @return NotFoundException
     */
    private static function createNotFoundException(
        array $errorData,
        array $context,
        ?RequestException $originalException = null
    ): NotFoundException {
        $message = $errorData['message'] ?? 'Resource not found';
        $resourceType = $errorData['resource_type'] ?? '';
        $resourceId = $errorData['resource_id'] ?? '';

        if ($resourceType && $resourceId) {
            return NotFoundException::resourceNotFound($resourceType, $resourceId, $context);
        }

        return new NotFoundException($message, 404, $originalException, $context);
    }

    /**
     * Create a validation exception based on error data
     *
     * @param array $errorData Parsed error response data
     * @param array $context Additional context
     * @param RequestException|null $originalException Original exception
     * @return ValidationException
     */
    private static function createValidationException(
        array $errorData,
        array $context,
        ?RequestException $originalException = null
    ): ValidationException {
        $message = $errorData['message'] ?? 'Validation failed';
        $errors = $errorData['errors'] ?? $errorData['validation_errors'] ?? [];

        return new ValidationException($message, $errors, 400, $originalException);
    }

    /**
     * Create a rate limit exception based on response and error data
     *
     * @param ResponseInterface $response HTTP response
     * @param array $errorData Parsed error response data
     * @param array $context Additional context
     * @param RequestException|null $originalException Original exception
     * @return RateLimitException
     */
    private static function createRateLimitException(
        ResponseInterface $response,
        array $errorData,
        array $context,
        ?RequestException $originalException = null
    ): RateLimitException {
        // Extract rate limit headers
        $retryAfter = $response->getHeaderLine('Retry-After');
        $rateLimitHeaders = [
            'X-RateLimit-Limit' => $response->getHeaderLine('X-RateLimit-Limit'),
            'X-RateLimit-Remaining' => $response->getHeaderLine('X-RateLimit-Remaining'),
            'X-RateLimit-Reset' => $response->getHeaderLine('X-RateLimit-Reset'),
        ];

        // Filter out empty headers
        $rateLimitHeaders = array_filter($rateLimitHeaders);

        $retryAfterInt = $retryAfter ? (int) $retryAfter : null;

        return RateLimitException::withRetryInfo($retryAfterInt, $rateLimitHeaders, $context);
    }

    /**
     * Parse error response body to extract error information
     *
     * @param string $body Response body
     * @return array Parsed error data
     */
    private static function parseErrorResponse(string $body): array
    {
        if (empty($body)) {
            return [];
        }

        $decoded = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['message' => $body];
        }

        // Handle different error response formats
        if (isset($decoded['error'])) {
            // Format: {"error": {"message": "...", "code": "..."}}
            if (is_array($decoded['error'])) {
                return $decoded['error'];
            }
            
            // Format: {"error": "error message"}
            return ['message' => $decoded['error']];
        }

        // Format: {"message": "...", "code": "..."}
        if (isset($decoded['message'])) {
            return $decoded;
        }

        // Return the entire decoded response if no standard format
        return $decoded;
    }

    /**
     * Check if error indicates an expired token
     *
     * @param string $message Error message
     * @param string $code Error code
     * @return bool
     */
    private static function isTokenExpiredError(string $message, string $code): bool
    {
        $expiredPatterns = [
            'expired',
            'token expired',
            'access token expired',
            'bearer token expired',
        ];

        $lowerMessage = strtolower($message);
        $lowerCode = strtolower($code);
        
        foreach ($expiredPatterns as $pattern) {
            if (strpos($lowerMessage, $pattern) !== false || strpos($lowerCode, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if error indicates an invalid token
     *
     * @param string $message Error message
     * @param string $code Error code
     * @return bool
     */
    private static function isInvalidTokenError(string $message, string $code): bool
    {
        $invalidPatterns = [
            'invalid token',
            'invalid bearer',
            'invalid access token',
            'malformed token',
            'bad token',
        ];

        $lowerMessage = strtolower($message);
        $lowerCode = strtolower($code);
        
        foreach ($invalidPatterns as $pattern) {
            if (strpos($lowerMessage, $pattern) !== false || strpos($lowerCode, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if error indicates missing authentication
     *
     * @param string $message Error message
     * @param string $code Error code
     * @return bool
     */
    private static function isMissingAuthError(string $message, string $code): bool
    {
        $missingPatterns = [
            'missing authorization',
            'no authorization',
            'authorization required',
            'authentication required',
            'missing token',
            'no token provided',
        ];

        $lowerMessage = strtolower($message);
        $lowerCode = strtolower($code);
        
        foreach ($missingPatterns as $pattern) {
            if (strpos($lowerMessage, $pattern) !== false || strpos($lowerCode, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}