<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\User;

/**
 * Email tracking webhook event
 * 
 * Triggered for email tracking events like opens and clicks.
 */
class EmailEvent extends WebhookEvent
{
    private ?User $user = null;

    /**
     * Get the user associated with this email event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the email ID
     *
     * @return string|null The email ID
     */
    public function getEmailId(): ?string
    {
        return $this->get('email_id');
    }

    /**
     * Get the email type
     *
     * @return string|null The email type
     */
    public function getEmailType(): ?string
    {
        return $this->get('email_type');
    }

    /**
     * Get the clicked link URL (for click events)
     *
     * @return string|null The clicked URL
     */
    public function getClickedUrl(): ?string
    {
        return $this->get('clicked_url') ?? $this->get('url');
    }

    /**
     * Get the user agent
     *
     * @return string|null The user agent
     */
    public function getUserAgent(): ?string
    {
        return $this->get('user_agent');
    }

    /**
     * Get the IP address
     *
     * @return string|null The IP address
     */
    public function getIpAddress(): ?string
    {
        return $this->get('ip_address') ?? $this->get('ip');
    }

    /**
     * Check if this is an email open event
     *
     * @return bool True if email was opened
     */
    public function isOpen(): bool
    {
        return $this->eventType === 'email.opened';
    }

    /**
     * Check if this is an email click event
     *
     * @return bool True if email link was clicked
     */
    public function isClick(): bool
    {
        return $this->eventType === 'email.clicked';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'email.clicked' => 'clicked',
            'email.opened' => 'opened',
            default => 'tracked'
        };

        $parts = ["Email {$action}"];

        if ($emailType = $this->getEmailType()) {
            $parts[] = "type:{$emailType}";
        }

        if ($this->isClick() && ($url = $this->getClickedUrl())) {
            $parts[] = "url:{$url}";
        }

        return implode(' ', $parts);
    }
}
