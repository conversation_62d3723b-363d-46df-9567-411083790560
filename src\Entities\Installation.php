<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Installation entity representing a Freemius product installation.
 * 
 * Represents an installation of a product on a site. For WordPress products 
 * (themes, plugins) it can or cannot have a license.
 */
class Installation extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'site_id' => 'integer',
        'plugin_id' => 'integer',
        'user_id' => 'integer',
        'plan_id' => 'integer',
        'license_id' => 'integer',
        'trial_plan_id' => 'integer',
        'subscription_id' => 'integer',
        'gross' => 'float',
        'is_active' => 'boolean',
        'is_disconnected' => 'boolean',
        'is_premium' => 'boolean',
        'is_uninstalled' => 'boolean',
        'is_locked' => 'boolean',
        'source' => 'integer',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'trial_ends',
        'upgraded',
        'last_seen_at',
    ];

    /**
     * Get the installation ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the site ID.
     */
    public function getSiteId(): int
    {
        return $this->getAttribute('site_id');
    }

    /**
     * Get the product ID.
     */
    public function getPluginId(): int
    {
        return $this->getAttribute('plugin_id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the site URL.
     */
    public function getUrl(): ?string
    {
        return $this->getAttribute('url');
    }

    /**
     * Get the site title.
     */
    public function getTitle(): ?string
    {
        return $this->getAttribute('title');
    }

    /**
     * Get the product version.
     */
    public function getVersion(): string
    {
        return $this->getAttribute('version', '');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): ?int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Check if the installation is using the free plan.
     */
    public function isFreePlan(): bool
    {
        return $this->getPlanId() === null;
    }

    /**
     * Get the license ID.
     */
    public function getLicenseId(): ?int
    {
        return $this->getAttribute('license_id');
    }

    /**
     * Check if the installation has a license.
     */
    public function hasLicense(): bool
    {
        return $this->getLicenseId() !== null;
    }

    /**
     * Get the trial plan ID.
     */
    public function getTrialPlanId(): ?int
    {
        return $this->getAttribute('trial_plan_id');
    }

    /**
     * Get the trial end date.
     */
    public function getTrialEnds(): ?DateTime
    {
        return $this->getAttribute('trial_ends');
    }

    /**
     * Check if the installation is in trial.
     */
    public function isInTrial(): bool
    {
        $trialEnds = $this->getTrialEnds();
        return $trialEnds !== null && $trialEnds->getTimestamp() > time();
    }

    /**
     * Check if the trial has expired.
     */
    public function isTrialExpired(): bool
    {
        $trialEnds = $this->getTrialEnds();
        return $trialEnds !== null && $trialEnds->getTimestamp() <= time();
    }

    /**
     * Get the subscription ID.
     */
    public function getSubscriptionId(): ?int
    {
        return $this->getAttribute('subscription_id');
    }

    /**
     * Check if the installation has a subscription.
     */
    public function hasSubscription(): bool
    {
        return $this->getSubscriptionId() !== null;
    }

    /**
     * Get the gross amount spent.
     */
    public function getGross(): float
    {
        return $this->getAttribute('gross', 0.0);
    }

    /**
     * Get the country code.
     */
    public function getCountryCode(): ?string
    {
        return $this->getAttribute('country_code');
    }

    /**
     * Get the language.
     */
    public function getLanguage(): ?string
    {
        return $this->getAttribute('language');
    }

    /**
     * Get the platform version (e.g., WordPress version).
     */
    public function getPlatformVersion(): ?string
    {
        return $this->getAttribute('platform_version');
    }

    /**
     * Get the SDK version.
     */
    public function getSdkVersion(): ?string
    {
        return $this->getAttribute('sdk_version');
    }

    /**
     * Get the programming language version (e.g., PHP version).
     */
    public function getProgrammingLanguageVersion(): ?string
    {
        return $this->getAttribute('programming_language_version');
    }

    /**
     * Check if the installation is active.
     */
    public function isActive(): bool
    {
        return $this->getAttribute('is_active', false);
    }

    /**
     * Check if the installation is disconnected.
     */
    public function isDisconnected(): bool
    {
        return $this->getAttribute('is_disconnected', false);
    }

    /**
     * Check if the installation is using premium code.
     */
    public function isPremium(): bool
    {
        return $this->getAttribute('is_premium', false);
    }

    /**
     * Check if the product is uninstalled.
     */
    public function isUninstalled(): bool
    {
        return $this->getAttribute('is_uninstalled', false);
    }

    /**
     * Check if the installation is locked.
     */
    public function isLocked(): bool
    {
        return $this->getAttribute('is_locked', false);
    }

    /**
     * Get the migration source.
     */
    public function getSource(): int
    {
        return $this->getAttribute('source', 0);
    }

    /**
     * Check if this installation was migrated from another platform.
     */
    public function isMigrated(): bool
    {
        return $this->getSource() !== 0;
    }

    /**
     * Get the upgrade date.
     */
    public function getUpgraded(): ?DateTime
    {
        return $this->getAttribute('upgraded');
    }

    /**
     * Get the last seen date.
     */
    public function getLastSeenAt(): ?DateTime
    {
        return $this->getAttribute('last_seen_at');
    }

    /**
     * Get the last served update version.
     */
    public function getLastServedUpdateVersion(): ?string
    {
        return $this->getAttribute('last_served_update_version');
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the secret key.
     */
    public function getSecretKey(): ?string
    {
        return $this->getAttribute('secret_key');
    }

    /**
     * Get the public key.
     */
    public function getPublicKey(): ?string
    {
        return $this->getAttribute('public_key');
    }
}