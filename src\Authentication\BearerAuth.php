<?php

declare(strict_types=1);

namespace Freemius\SDK\Authentication;

use Psr\Http\Message\RequestInterface;
use InvalidArgumentException;

/**
 * Bearer token authentication for Freemius API
 * 
 * Implements Bearer token authentication by automatically
 * injecting the Authorization header into HTTP requests.
 */
class BearerAuth implements AuthenticationInterface
{
    private string $bearerToken;

    /**
     * Create a new Bearer authentication instance
     *
     * @param string $bearerToken The Bearer token for authentication
     * @throws InvalidArgumentException If the token is empty
     */
    public function __construct(string $bearerToken)
    {
        if (empty($bearerToken)) {
            throw new InvalidArgumentException('Bearer token cannot be empty');
        }

        $this->bearerToken = $bearerToken;
    }

    /**
     * {@inheritdoc}
     */
    public function authenticate(RequestInterface $request): RequestInterface
    {
        return $request->withHeader('Authorization', 'Bearer ' . $this->bearerToken);
    }

    /**
     * Get the Bearer token (for testing purposes)
     * 
     * Note: This method should be used carefully to avoid token exposure
     *
     * @return string The Bearer token
     */
    public function getToken(): string
    {
        return $this->bearerToken;
    }

    /**
     * Update the Bearer token
     *
     * @param string $bearerToken The new Bearer token
     * @return self
     * @throws InvalidArgumentException If the token is empty
     */
    public function setToken(string $bearerToken): self
    {
        if (empty($bearerToken)) {
            throw new InvalidArgumentException('Bearer token cannot be empty');
        }

        $this->bearerToken = $bearerToken;
        return $this;
    }
}