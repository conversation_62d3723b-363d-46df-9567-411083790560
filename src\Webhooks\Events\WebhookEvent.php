<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use DateTime;
use <PERSON>mius\SDK\Entities\AbstractEntity;
use <PERSON>mius\SDK\Support\DateTimeHelper;

/**
 * Base class for all webhook events
 * 
 * Provides common functionality for parsing webhook event data
 * and accessing event metadata.
 */
abstract class WebhookEvent
{
    protected array $data;
    protected string $eventType;
    protected ?DateTime $timestamp = null;

    /**
     * Create a new webhook event
     *
     * @param array $data The webhook event data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
        $this->eventType = $this->extractEventType($data);
        $this->timestamp = $this->extractTimestamp($data);
    }

    /**
     * Get the event type
     *
     * @return string The event type (e.g., 'install.activated')
     */
    public function getEventType(): string
    {
        return $this->eventType;
    }

    /**
     * Get the event timestamp
     *
     * @return DateTime|null The event timestamp
     */
    public function getTimestamp(): ?DateTime
    {
        return $this->timestamp;
    }

    /**
     * Get the raw event data
     *
     * @return array The raw webhook data
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * Get a specific data field
     *
     * @param string $key The data key
     * @param mixed $default Default value if key doesn't exist
     * @return mixed The data value
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Check if a data field exists
     *
     * @param string $key The data key
     * @return bool True if key exists
     */
    public function has(string $key): bool
    {
        return isset($this->data[$key]);
    }

    /**
     * Get the webhook ID if available
     *
     * @return string|null The webhook ID
     */
    public function getWebhookId(): ?string
    {
        return $this->get('webhook_id') ?? $this->get('id');
    }

    /**
     * Get the product ID associated with this event
     *
     * @return int|null The product ID
     */
    public function getProductId(): ?int
    {
        $productId = $this->get('product_id') ?? $this->get('plugin_id');
        return $productId ? (int) $productId : null;
    }

    /**
     * Get the user ID associated with this event
     *
     * @return int|null The user ID
     */
    public function getUserId(): ?int
    {
        $userId = $this->get('user_id');
        return $userId ? (int) $userId : null;
    }

    /**
     * Convert event to array
     *
     * @return array The event as array
     */
    public function toArray(): array
    {
        return [
            'event_type' => $this->eventType,
            'timestamp' => $this->timestamp?->format('c'),
            'webhook_id' => $this->getWebhookId(),
            'product_id' => $this->getProductId(),
            'user_id' => $this->getUserId(),
            'data' => $this->data,
        ];
    }

    /**
     * Convert event to JSON
     *
     * @return string The event as JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    /**
     * Extract event type from webhook data
     *
     * @param array $data The webhook data
     * @return string The event type
     */
    protected function extractEventType(array $data): string
    {
        return $data['type'] ?? $data['event'] ?? $data['event_type'] ?? 'unknown';
    }

    /**
     * Extract timestamp from webhook data
     *
     * @param array $data The webhook data
     * @return DateTime|null The timestamp
     */
    protected function extractTimestamp(array $data): ?DateTime
    {
        $timestamp = $data['timestamp'] ?? $data['created'] ?? $data['occurred_at'] ?? null;

        if ($timestamp === null) {
            return null;
        }

        // Handle Unix timestamp
        if (is_numeric($timestamp)) {
            return DateTimeHelper::fromTimestamp((int) $timestamp);
        }

        // Handle ISO 8601 string
        if (is_string($timestamp)) {
            return DateTimeHelper::fromString($timestamp);
        }

        return null;
    }

    /**
     * Create entity from webhook data
     *
     * @param array $entityData The entity data
     * @param string $entityClass The entity class name
     * @return AbstractEntity|null The created entity
     */
    protected function createEntity(array $entityData, string $entityClass): ?AbstractEntity
    {
        if (empty($entityData)) {
            return null;
        }

        if (!is_subclass_of($entityClass, AbstractEntity::class)) {
            throw new \InvalidArgumentException(
                "Entity class {$entityClass} must extend " . AbstractEntity::class
            );
        }

        return new $entityClass($entityData);
    }

    /**
     * Get nested data from webhook payload
     *
     * @param string $path Dot-separated path (e.g., 'user.email')
     * @param mixed $default Default value if path doesn't exist
     * @return mixed The nested value
     */
    protected function getNestedData(string $path, $default = null)
    {
        $keys = explode('.', $path);
        $current = $this->data;

        foreach ($keys as $key) {
            if (!is_array($current) || !isset($current[$key])) {
                return $default;
            }
            $current = $current[$key];
        }

        return $current;
    }

    /**
     * Check if this is a test webhook
     *
     * @return bool True if this is a test webhook
     */
    public function isTest(): bool
    {
        return (bool) ($this->get('test') ?? $this->get('is_test') ?? false);
    }

    /**
     * Get the environment this webhook came from
     *
     * @return string The environment ('production', 'sandbox', 'test')
     */
    public function getEnvironment(): string
    {
        if ($this->isTest()) {
            return 'test';
        }

        return $this->get('environment') ?? 'production';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $productId = $this->getProductId();
        $userId = $this->getUserId();
        $webhookId = $this->getWebhookId();

        $parts = [$this->eventType];

        if ($productId) {
            $parts[] = "product:{$productId}";
        }

        if ($userId) {
            $parts[] = "user:{$userId}";
        }

        if ($webhookId) {
            $parts[] = "webhook:{$webhookId}";
        }

        return implode(' ', $parts);
    }

    /**
     * Magic method to access data as properties
     *
     * @param string $name The property name
     * @return mixed The property value
     */
    public function __get(string $name)
    {
        return $this->get($name);
    }

    /**
     * Magic method to check if property exists
     *
     * @param string $name The property name
     * @return bool True if property exists
     */
    public function __isset(string $name): bool
    {
        return $this->has($name);
    }

    /**
     * String representation of the event
     *
     * @return string The event summary
     */
    public function __toString(): string
    {
        return $this->getSummary();
    }
}