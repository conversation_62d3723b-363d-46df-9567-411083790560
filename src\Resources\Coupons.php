<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Coupon;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Coupons resource for managing Freemius discount coupons.
 * 
 * Handles CRUD operations and specialized methods for coupon management.
 */
class Coupons extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'coupons';
    }
    
    protected function getEntityClass(): string
    {
        return Coupon::class;
    }
    
    /**
     * Get active coupons only.
     */
    public function active(): self
    {
        return $this->where('is_active', true);
    }
    
    /**
     * Get inactive coupons only.
     */
    public function inactive(): self
    {
        return $this->where('is_active', false);
    }
    
    /**
     * Get expired coupons only.
     */
    public function expired(): self
    {
        return $this->where('expires_at', '<', date('Y-m-d H:i:s'));
    }
    
    /**
     * Get valid (active and not expired) coupons only.
     */
    public function valid(): self
    {
        return $this->active()
                   ->where('expires_at', '>', date('Y-m-d H:i:s'));
    }
    
    /**
     * Get coupons by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get coupons by discount type.
     */
    public function byDiscountType(string $type): self
    {
        return $this->where('discount_type', $type);
    }
    
    /**
     * Get percentage-based coupons.
     */
    public function percentage(): self
    {
        return $this->byDiscountType('percentage');
    }
    
    /**
     * Get fixed amount coupons.
     */
    public function fixedAmount(): self
    {
        return $this->byDiscountType('fixed');
    }
    
    /**
     * Get single-use coupons.
     */
    public function singleUse(): self
    {
        return $this->where('is_single_use', true);
    }
    
    /**
     * Get recurring coupons.
     */
    public function recurring(): self
    {
        return $this->where('is_recurring', true);
    }
    
    /**
     * Get coupons by currency.
     */
    public function byCurrency(string $currency): self
    {
        return $this->where('currency', $currency);
    }
    
    /**
     * Get coupons with usage limit.
     */
    public function withUsageLimit(): self
    {
        return $this->where('usage_limit', '>', 0);
    }
    
    /**
     * Get unlimited usage coupons.
     */
    public function unlimitedUsage(): self
    {
        return $this->where('usage_limit', null);
    }
    
    /**
     * Get coupons created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get coupons created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get coupons expiring before a specific date.
     */
    public function expiringBefore(string $date): self
    {
        return $this->where('expires_at', '<', $date);
    }
    
    /**
     * Get coupons expiring after a specific date.
     */
    public function expiringAfter(string $date): self
    {
        return $this->where('expires_at', '>', $date);
    }
    
    /**
     * Search coupons by code.
     */
    public function searchByCode(string $code): self
    {
        return $this->where('code', 'like', '%' . $code . '%');
    }
    
    /**
     * Find coupon by exact code.
     */
    public function findByCode(string $code): ?Coupon
    {
        $collection = $this->where('code', $code)->all();
        return $collection->first();
    }
    
    /**
     * Validate coupon for a specific cart/purchase.
     */
    public function validate(string $couponCode, array $validationData = []): array
    {
        $endpoint = $this->buildEndpoint() . '/validate';
        $data = array_merge(['coupon_code' => $couponCode], $validationData);
        
        $response = $this->httpClient->post($endpoint, $data);
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Apply coupon and get discount calculation.
     */
    public function apply(string $couponCode, array $applicationData): array
    {
        $endpoint = $this->buildEndpoint() . '/apply';
        $data = array_merge(['coupon_code' => $couponCode], $applicationData);
        
        $response = $this->httpClient->post($endpoint, $data);
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Get coupon usage statistics.
     */
    public function getUsageStats(int $couponId): array
    {
        $endpoint = $this->buildEndpoint($couponId) . '/stats';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Get coupon usage history.
     */
    public function getUsageHistory(int $couponId): array
    {
        $endpoint = $this->buildEndpoint($couponId) . '/usage';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Activate a coupon.
     */
    public function activate(int $couponId)
    {
        $endpoint = $this->buildEndpoint($couponId) . '/activate';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Coupon($data);
    }
    
    /**
     * Deactivate a coupon.
     */
    public function deactivate(int $couponId)
    {
        $endpoint = $this->buildEndpoint($couponId) . '/deactivate';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Coupon($data);
    }
    
    /**
     * Generate a random coupon code.
     */
    public function generateCode(int $length = 8): string
    {
        $endpoint = $this->buildEndpoint() . '/generate-code';
        $response = $this->httpClient->post($endpoint, ['length' => $length]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return $data['code'] ?? '';
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['code', 'discount_type'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a coupon");
            }
        }
        
        // Validate discount type
        $validTypes = ['percentage', 'dollar'];
        if (!in_array($data['discount_type'], $validTypes)) {
            throw new ValidationException("Invalid discount type. Must be one of: " . implode(', ', $validTypes));
        }
        
        // Validate discount value
        if (!isset($data['discount']) || !is_numeric($data['discount'])) {
            throw new ValidationException("Discount is required and must be numeric");
        }
        if ($data['discount'] < 0) {
            throw new ValidationException("Discount must be positive");
        }
        if ($data['discount_type'] === 'percentage' && $data['discount'] > 100) {
            throw new ValidationException("Discount percentage must not exceed 100");
        }
        
        // Validate numeric fields
        $numericFields = ['product_id', 'plan_id', 'usage_limit', 'minimum_amount'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate discount type if provided
        if (isset($data['discount_type'])) {
            $validTypes = ['percentage', 'dollar'];
            if (!in_array($data['discount_type'], $validTypes)) {
                throw new ValidationException("Invalid discount type. Must be one of: " . implode(', ', $validTypes));
            }
        }
        
        // Validate discount value if provided
        if (isset($data['discount'])) {
            if (!is_numeric($data['discount'])) {
                throw new ValidationException("Discount must be numeric");
            }
            if ($data['discount'] < 0) {
                throw new ValidationException("Discount must be positive");
            }
            if (isset($data['discount_type']) && $data['discount_type'] === 'percentage' && $data['discount'] > 100) {
                throw new ValidationException("Discount percentage must not exceed 100");
            }
        }
        
        // Validate numeric fields if provided
        $numericFields = ['product_id', 'plan_id', 'usage_limit', 'minimum_amount'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
}