<?php

declare(strict_types=1);

namespace Freemius\SDK\Authentication;

use Psr\Http\Message\RequestInterface;

/**
 * Authentication interface for Freemius API requests
 * 
 * Defines the contract for authentication implementations
 * that can modify requests to include authentication credentials.
 */
interface AuthenticationInterface
{
    /**
     * Authenticate a request by adding authentication headers or parameters
     *
     * @param RequestInterface $request The request to authenticate
     * @return RequestInterface The authenticated request
     */
    public function authenticate(RequestInterface $request): RequestInterface;
}