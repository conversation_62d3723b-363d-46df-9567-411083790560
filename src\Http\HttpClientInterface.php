<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use Psr\Http\Message\ResponseInterface;

/**
 * HTTP client interface for Freemius API requests
 * 
 * Defines the contract for HTTP client implementations
 * with support for all standard HTTP methods.
 */
interface HttpClientInterface
{
    /**
     * Send a GET request
     *
     * @param string $uri Request URI
     * @param array $params Query parameters
     * @param array $headers Additional headers
     * @return ResponseInterface
     */
    public function get(string $uri, array $params = [], array $headers = []): ResponseInterface;

    /**
     * Send a POST request
     *
     * @param string $uri Request URI
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @return ResponseInterface
     */
    public function post(string $uri, array $data = [], array $headers = []): ResponseInterface;

    /**
     * Send a PUT request
     *
     * @param string $uri Request URI
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @return ResponseInterface
     */
    public function put(string $uri, array $data = [], array $headers = []): ResponseInterface;

    /**
     * Send a DELETE request
     *
     * @param string $uri Request URI
     * @param array $headers Additional headers
     * @return ResponseInterface
     */
    public function delete(string $uri, array $headers = []): ResponseInterface;

    /**
     * Send a PATCH request
     *
     * @param string $uri Request URI
     * @param array $data Request body data
     * @param array $headers Additional headers
     * @return ResponseInterface
     */
    public function patch(string $uri, array $data = [], array $headers = []): ResponseInterface;
}