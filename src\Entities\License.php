<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * License entity representing a Freemius license.
 * 
 * A license represents authorization to use available features of the product.
 */
class License extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'plugin_id' => 'integer',
        'user_id' => 'integer',
        'plan_id' => 'integer',
        'pricing_id' => 'integer',
        'quota' => 'integer',
        'activated' => 'integer',
        'activated_local' => 'integer',
        'is_free_localhost' => 'boolean',
        'is_block_features' => 'boolean',
        'is_cancelled' => 'boolean',
        'is_whitelabeled' => 'boolean',
        'environment' => 'integer',
        'source' => 'integer',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'expiration',
    ];

    /**
     * Get the license ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the product ID.
     */
    public function getPluginId(): int
    {
        return $this->getAttribute('plugin_id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): ?int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the pricing ID.
     */
    public function getPricingId(): ?int
    {
        return $this->getAttribute('pricing_id');
    }

    /**
     * Get the license key (secret key).
     */
    public function getSecretKey(): string
    {
        return $this->getAttribute('secret_key', '');
    }

    /**
     * Get the license key (alias for getSecretKey).
     */
    public function getLicenseKey(): string
    {
        return $this->getSecretKey();
    }

    /**
     * Get the maximum number of activations allowed.
     */
    public function getQuota(): ?int
    {
        return $this->getAttribute('quota');
    }

    /**
     * Check if the license supports unlimited activations.
     */
    public function hasUnlimitedActivations(): bool
    {
        return $this->getQuota() === null;
    }

    /**
     * Get the number of production activations.
     */
    public function getActivated(): int
    {
        return $this->getAttribute('activated', 0);
    }

    /**
     * Get the number of local/staging activations.
     */
    public function getActivatedLocal(): int
    {
        return $this->getAttribute('activated_local', 0);
    }

    /**
     * Get the total number of activations.
     */
    public function getTotalActivations(): int
    {
        return $this->getActivated() + $this->getActivatedLocal();
    }

    /**
     * Get the number of remaining activations.
     */
    public function getRemainingActivations(): ?int
    {
        $quota = $this->getQuota();
        if ($quota === null) {
            return null; // Unlimited
        }
        
        return max(0, $quota - $this->getActivated());
    }

    /**
     * Check if the license can be activated.
     */
    public function canActivate(): bool
    {
        if ($this->isCancelled()) {
            return false;
        }
        
        if ($this->isExpired()) {
            return false;
        }
        
        $remaining = $this->getRemainingActivations();
        return $remaining === null || $remaining > 0;
    }

    /**
     * Get the expiration date.
     */
    public function getExpiration(): ?DateTime
    {
        return $this->getAttribute('expiration');
    }

    /**
     * Check if the license is a lifetime license.
     */
    public function isLifetime(): bool
    {
        return $this->getExpiration() === null;
    }

    /**
     * Check if the license is expired.
     */
    public function isExpired(): bool
    {
        $expiration = $this->getExpiration();
        if ($expiration === null) {
            return false; // Lifetime license
        }
        
        return $expiration->getTimestamp() < time();
    }

    /**
     * Get the number of days until expiration.
     */
    public function getDaysUntilExpiration(): ?int
    {
        $expiration = $this->getExpiration();
        if ($expiration === null) {
            return null; // Lifetime license
        }
        
        $now = new DateTime();
        $diff = $now->diff($expiration);
        
        return $diff->invert ? -$diff->days : $diff->days;
    }

    /**
     * Check if localhost activations are free.
     */
    public function isFreeLocalhost(): bool
    {
        return $this->getAttribute('is_free_localhost', true);
    }

    /**
     * Check if features should be blocked after expiration.
     */
    public function isBlockFeatures(): bool
    {
        return $this->getAttribute('is_block_features', true);
    }

    /**
     * Check if the license is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->getAttribute('is_cancelled', false);
    }

    /**
     * Check if the license is whitelabeled.
     */
    public function isWhitelabeled(): bool
    {
        return $this->getAttribute('is_whitelabeled', false);
    }

    /**
     * Get the environment (0 = production, 1 = sandbox).
     */
    public function getEnvironment(): int
    {
        return $this->getAttribute('environment', 0);
    }

    /**
     * Check if this is a sandbox license.
     */
    public function isSandbox(): bool
    {
        return $this->getEnvironment() === 1;
    }

    /**
     * Get the migration source.
     */
    public function getSource(): int
    {
        return $this->getAttribute('source', 0);
    }

    /**
     * Check if this license was migrated from another platform.
     */
    public function isMigrated(): bool
    {
        return $this->getSource() !== 0;
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }
}