<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

use Freemius\SDK\Resources\AbstractResource;
use Closure;
use Generator;

/**
 * Data processor for memory-efficient handling of large datasets.
 * 
 * Provides utilities for processing large amounts of data from the Freemius API
 * without consuming excessive memory.
 */
class DataProcessor
{
    private AbstractResource $resource;
    private int $chunkSize;
    private int $maxMemoryUsage;
    private bool $enableGarbageCollection;
    
    public function __construct(
        AbstractResource $resource,
        int $chunkSize = 100,
        int $maxMemoryUsage = 128 * 1024 * 1024, // 128MB
        bool $enableGarbageCollection = true
    ) {
        $this->resource = $resource;
        $this->chunkSize = $chunkSize;
        $this->maxMemoryUsage = $maxMemoryUsage;
        $this->enableGarbageCollection = $enableGarbageCollection;
    }
    
    /**
     * Process all data in chunks with memory management.
     */
    public function processAll(Closure $processor): array
    {
        $results = [];
        $offset = 0;
        $processedCount = 0;
        
        while (true) {
            // Check memory usage
            if ($this->isMemoryLimitReached()) {
                if ($this->enableGarbageCollection) {
                    $this->forceGarbageCollection();
                } else {
                    throw new \RuntimeException('Memory limit reached during data processing');
                }
            }
            
            // Fetch next chunk
            $collection = $this->resource->paginate($this->chunkSize, $offset);
            
            if ($collection->isEmpty()) {
                break;
            }
            
            // Process chunk
            $chunkResults = $processor($collection, $processedCount);
            if ($chunkResults !== null) {
                $results[] = $chunkResults;
            }
            
            $processedCount += $collection->count();
            $offset += $this->chunkSize;
            
            // Safety break to prevent infinite loops
            if ($processedCount > 100000) {
                break;
            }
            
            // Clear variables to help with memory management
            unset($collection, $chunkResults);
        }
        
        return $results;
    }
    
    /**
     * Stream data lazily as a generator.
     */
    public function stream(): Generator
    {
        $offset = 0;
        
        while (true) {
            // Check memory usage
            if ($this->isMemoryLimitReached()) {
                if ($this->enableGarbageCollection) {
                    $this->forceGarbageCollection();
                } else {
                    throw new \RuntimeException('Memory limit reached during data streaming');
                }
            }
            
            // Fetch next chunk
            $collection = $this->resource->paginate($this->chunkSize, $offset);
            
            if ($collection->isEmpty()) {
                break;
            }
            
            // Yield each item
            foreach ($collection->all() as $item) {
                yield $item;
            }
            
            $offset += $this->chunkSize;
            
            // Clear collection to help with memory management
            unset($collection);
        }
    }
    
    /**
     * Process data in batches with custom batch size.
     */
    public function batch(int $batchSize, Closure $processor): void
    {
        $batch = [];
        $batchCount = 0;
        
        foreach ($this->stream() as $item) {
            $batch[] = $item;
            
            if (count($batch) >= $batchSize) {
                $processor($batch, $batchCount);
                $batch = [];
                $batchCount++;
                
                // Force garbage collection periodically
                if ($this->enableGarbageCollection && $batchCount % 10 === 0) {
                    $this->forceGarbageCollection();
                }
            }
        }
        
        // Process remaining items
        if (!empty($batch)) {
            $processor($batch, $batchCount);
        }
    }
    
    /**
     * Aggregate data across all pages.
     */
    public function aggregate(Closure $aggregator, $initialValue = null)
    {
        $accumulator = $initialValue;
        
        foreach ($this->stream() as $item) {
            $accumulator = $aggregator($accumulator, $item);
        }
        
        return $accumulator;
    }
    
    /**
     * Count total items across all pages.
     */
    public function count(): int
    {
        return $this->aggregate(function ($count, $item) {
            return $count + 1;
        }, 0);
    }
    
    /**
     * Find first item matching a condition.
     */
    public function find(Closure $condition)
    {
        foreach ($this->stream() as $item) {
            if ($condition($item)) {
                return $item;
            }
        }
        
        return null;
    }
    
    /**
     * Filter items across all pages.
     */
    public function filter(Closure $filter): Generator
    {
        foreach ($this->stream() as $item) {
            if ($filter($item)) {
                yield $item;
            }
        }
    }
    
    /**
     * Map items across all pages.
     */
    public function map(Closure $mapper): Generator
    {
        foreach ($this->stream() as $item) {
            yield $mapper($item);
        }
    }
    
    /**
     * Reduce items across all pages.
     */
    public function reduce(Closure $reducer, $initialValue = null)
    {
        return $this->aggregate($reducer, $initialValue);
    }
    
    /**
     * Export data to CSV format.
     */
    public function toCsv(string $filename, array $headers = [], Closure $formatter = null): void
    {
        $file = fopen($filename, 'w');
        
        if ($file === false) {
            throw new \RuntimeException("Cannot open file for writing: {$filename}");
        }
        
        // Write headers
        if (!empty($headers)) {
            fputcsv($file, $headers);
        }
        
        // Write data
        foreach ($this->stream() as $item) {
            $row = $formatter ? $formatter($item) : (array) $item;
            fputcsv($file, $row);
        }
        
        fclose($file);
    }
    
    /**
     * Export data to JSON Lines format.
     */
    public function toJsonLines(string $filename, Closure $formatter = null): void
    {
        $file = fopen($filename, 'w');
        
        if ($file === false) {
            throw new \RuntimeException("Cannot open file for writing: {$filename}");
        }
        
        foreach ($this->stream() as $item) {
            $data = $formatter ? $formatter($item) : $item;
            fwrite($file, json_encode($data) . "\n");
        }
        
        fclose($file);
    }
    
    /**
     * Check if memory limit is reached.
     */
    private function isMemoryLimitReached(): bool
    {
        return memory_get_usage(true) > $this->maxMemoryUsage;
    }
    
    /**
     * Force garbage collection.
     */
    private function forceGarbageCollection(): void
    {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }
    
    /**
     * Get current memory usage information.
     */
    public function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => $this->maxMemoryUsage,
            'percentage' => (memory_get_usage(true) / $this->maxMemoryUsage) * 100,
        ];
    }
    
    /**
     * Set chunk size for processing.
     */
    public function setChunkSize(int $size): self
    {
        $this->chunkSize = max(1, $size);
        return $this;
    }
    
    /**
     * Set memory limit for processing.
     */
    public function setMemoryLimit(int $bytes): self
    {
        $this->maxMemoryUsage = max(1024 * 1024, $bytes); // Minimum 1MB
        return $this;
    }
    
    /**
     * Enable or disable garbage collection.
     */
    public function setGarbageCollection(bool $enabled): self
    {
        $this->enableGarbageCollection = $enabled;
        return $this;
    }
}
