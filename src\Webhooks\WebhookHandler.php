<?php

declare(strict_types=1);

namespace <PERSON>mius\SDK\Webhooks;

use Freemius\SDK\Exceptions\WebhookException;
use Freemius\SDK\Webhooks\Events\WebhookEvent;
use Freemius\SDK\Webhooks\Events\InstallActivated;
use Freemius\SDK\Webhooks\Events\SubscriptionUpdated;
use Freemius\SDK\Webhooks\Events\PaymentCompleted;
use Freemius\SDK\Webhooks\Events\LicenseActivated;
use Freemius\SDK\Webhooks\Events\UserRegistered;
use Freemius\SDK\Webhooks\Events\TrialStarted;
use Freemius\SDK\Webhooks\Events\TrialEnded;
use Freemius\SDK\Webhooks\Events\UnknownEvent;
use Freemius\SDK\Webhooks\Events\AffiliateEvent;
use Freemius\SDK\Webhooks\Events\CartEvent;
use Freemius\SDK\Webhooks\Events\ReviewEvent;
use Freemius\SDK\Webhooks\Events\CouponEvent;
use <PERSON><PERSON>us\SDK\Webhooks\Events\EmailEvent;

/**
 * Webhook handler for processing incoming Freemius webhooks
 * 
 * Handles webhook signature validation, event parsing, and creation of
 * structured event objects from webhook payloads.
 */
class WebhookHandler
{
    private WebhookValidator $validator;
    private array $eventMap;

    /**
     * Create a new webhook handler
     *
     * @param string $webhookSecret The webhook secret from Freemius
     * @param int $timestampTolerance Timestamp tolerance in seconds
     */
    public function __construct(string $webhookSecret, int $timestampTolerance = 300)
    {
        $this->validator = new WebhookValidator($webhookSecret, $timestampTolerance);
        $this->initializeEventMap();
    }

    /**
     * Handle incoming webhook request
     *
     * @param string $payload The raw webhook payload
     * @param array $headers HTTP headers from the webhook request
     * @return WebhookEvent The parsed webhook event
     * @throws WebhookException If validation or parsing fails
     */
    public function handle(string $payload, array $headers): WebhookEvent
    {
        // Extract signature and timestamp from headers
        $signature = WebhookValidator::extractSignature($headers);
        $timestamp = WebhookValidator::extractTimestamp($headers);

        // Validate the webhook
        $this->validator->validate($payload, $signature ?? '', $timestamp);

        // Parse the payload
        $data = $this->parsePayload($payload);

        // Create and return the appropriate event object
        return $this->createEvent($data);
    }

    /**
     * Handle webhook without signature validation (for testing)
     *
     * @param string $payload The raw webhook payload
     * @return WebhookEvent The parsed webhook event
     * @throws WebhookException If parsing fails
     */
    public function handleUnsafe(string $payload): WebhookEvent
    {
        $data = $this->parsePayload($payload);
        return $this->createEvent($data);
    }

    /**
     * Parse webhook payload JSON
     *
     * @param string $payload The raw webhook payload
     * @return array The parsed payload data
     * @throws WebhookException If payload is malformed
     */
    private function parsePayload(string $payload): array
    {
        if (empty($payload)) {
            throw WebhookException::malformedPayload($payload, 'Empty payload');
        }

        $data = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw WebhookException::malformedPayload(
                $payload,
                'JSON decode error: ' . json_last_error_msg()
            );
        }

        if (!is_array($data)) {
            throw WebhookException::malformedPayload($payload, 'Payload is not a JSON object');
        }

        return $data;
    }

    /**
     * Create appropriate event object from parsed data
     *
     * @param array $data The parsed webhook data
     * @return WebhookEvent The created event object
     * @throws WebhookException If event type is missing or unsupported
     */
    private function createEvent(array $data): WebhookEvent
    {
        $eventType = $data['type'] ?? $data['event'] ?? $data['event_type'] ?? null;

        if (empty($eventType)) {
            throw WebhookException::malformedPayload(
                json_encode($data),
                'Missing event type in webhook payload'
            );
        }

        $eventClass = $this->eventMap[$eventType] ?? null;

        if ($eventClass === null) {
            // Return UnknownEvent for unsupported event types instead of throwing
            return new UnknownEvent($data);
        }

        return new $eventClass($data);
    }

    /**
     * Initialize the event type to class mapping
     * Based on official Freemius webhook documentation
     */
    private function initializeEventMap(): void
    {
        $this->eventMap = [
            // Affiliate Program events
            'affiliate.approved' => AffiliateEvent::class,
            'affiliate.blocked' => AffiliateEvent::class,
            'affiliate.created' => AffiliateEvent::class,
            'affiliate.deleted' => AffiliateEvent::class,
            'affiliate.payout.pending' => AffiliateEvent::class,
            'affiliate.paypal.updated' => AffiliateEvent::class,
            'affiliate.rejected' => AffiliateEvent::class,
            'affiliate.suspended' => AffiliateEvent::class,
            'affiliate.unapproved' => AffiliateEvent::class,
            'affiliate.updated' => AffiliateEvent::class,

            // Card events
            'card.created' => UnknownEvent::class,
            'card.updated' => UnknownEvent::class,

            // Cart events
            'cart.abandoned' => CartEvent::class,
            'cart.completed' => CartEvent::class,
            'cart.created' => CartEvent::class,
            'cart.recovered' => CartEvent::class,
            'cart.recovery.deactivated' => CartEvent::class,
            'cart.recovery.email_1_sent' => CartEvent::class,
            'cart.recovery.email_2_sent' => CartEvent::class,
            'cart.recovery.email_3_sent' => CartEvent::class,
            'cart.recovery.reactivated' => CartEvent::class,
            'cart.recovery.subscribed' => CartEvent::class,
            'cart.recovery.unsubscribed' => CartEvent::class,
            'cart.updated' => CartEvent::class,

            // Coupons & Discounts events
            'coupon.created' => CouponEvent::class,
            'coupon.deleted' => CouponEvent::class,
            'coupon.updated' => CouponEvent::class,

            // Buyer Email Tracking events
            'email.clicked' => EmailEvent::class,
            'email.opened' => EmailEvent::class,

            // Installation (Downloadable Software) events
            'install.activated' => InstallActivated::class,
            'install.deactivated' => InstallActivated::class,
            'install.deleted' => InstallActivated::class,
            'install.connected' => InstallActivated::class,
            'install.disconnected' => InstallActivated::class,
            'install.installed' => InstallActivated::class,
            'install.language.updated' => InstallActivated::class,
            'install.plan.changed' => InstallActivated::class,
            'install.plan.downgraded' => InstallActivated::class,
            'install.platform.version.updated' => InstallActivated::class,
            'install.premium.activated' => InstallActivated::class,
            'install.premium.deactivated' => InstallActivated::class,
            'install.programming_language.version.updated' => InstallActivated::class,
            'install.sdk.version.updated' => InstallActivated::class,
            'install.title.updated' => InstallActivated::class,
            'install.trial.cancelled' => TrialEnded::class,
            'install.trial.expired' => TrialEnded::class,
            'install.trial_expiring_notice.sent' => TrialStarted::class,
            'install.trial.extended' => TrialStarted::class,
            'install.trial.plan.updated' => TrialStarted::class,
            'install.trial.started' => TrialStarted::class,
            'install.uninstalled' => InstallActivated::class,
            'install.updated' => InstallActivated::class,
            'install.url.updated' => InstallActivated::class,
            'install.version.downgrade' => InstallActivated::class,
            'install.version.upgraded' => InstallActivated::class,

            // Licensing events
            'license.activated' => LicenseActivated::class,
            'license.cancelled' => LicenseActivated::class,
            'license.created' => LicenseActivated::class,
            'license.deactivated' => LicenseActivated::class,
            'license.deleted' => LicenseActivated::class,
            'license.expired' => LicenseActivated::class,
            'license.expired_notice.sent' => LicenseActivated::class,
            'license.extended' => LicenseActivated::class,
            'license.ownership.changed' => LicenseActivated::class,
            'license.quota.changed' => LicenseActivated::class,
            'license.renewal_reminder.sent' => LicenseActivated::class,
            'license.shortened' => LicenseActivated::class,
            'license.trial_expiring_notice.sent' => LicenseActivated::class,
            'license.updated' => LicenseActivated::class,

            // Payment events
            'payment.created' => PaymentCompleted::class,
            'payment.refund' => PaymentCompleted::class,
            'payment.dispute.created' => PaymentCompleted::class,
            'payment.dispute.closed' => PaymentCompleted::class,
            'payment.dispute.lost' => PaymentCompleted::class,
            'payment.dispute.won' => PaymentCompleted::class,

            // Product Team events
            'member.created' => UnknownEvent::class,
            'member.deleted' => UnknownEvent::class,
            'member.updated' => UnknownEvent::class,

            // Plans, Pricing and Features events
            'plan.created' => UnknownEvent::class,
            'plan.deleted' => UnknownEvent::class,
            'plan.lifetime.purchase' => UnknownEvent::class,
            'plan.updated' => UnknownEvent::class,
            'pricing.created' => UnknownEvent::class,
            'pricing.deleted' => UnknownEvent::class,
            'pricing.updated' => UnknownEvent::class,

            // Reviews events
            'review.created' => ReviewEvent::class,
            'review.deleted' => ReviewEvent::class,
            'review.requested' => ReviewEvent::class,
            'review.updated' => ReviewEvent::class,

            // Subscription events
            'subscription.cancelled' => SubscriptionUpdated::class,
            'subscription.created' => SubscriptionUpdated::class,
            'subscription.renewal_reminder.sent' => SubscriptionUpdated::class,
            'subscription.renewal_reminder.opened' => SubscriptionUpdated::class,
            'subscription.renewal.failed' => SubscriptionUpdated::class,
            'subscription.renewal.failed.last' => SubscriptionUpdated::class,
            'subscription.renewal.failed_email.sent' => SubscriptionUpdated::class,
            'subscription.renewal.retry' => SubscriptionUpdated::class,
            'subscription.renewals.discounted' => SubscriptionUpdated::class,

            // Store events
            'store.created' => UnknownEvent::class,
            'store.dashboard_url.updated' => UnknownEvent::class,
            'store.plugin.added' => UnknownEvent::class,
            'store.plugin.removed' => UnknownEvent::class,
            'store.url.updated' => UnknownEvent::class,

            // User events
            'user.beta_program.opted_in' => UserRegistered::class,
            'user.beta_program.opted_out' => UserRegistered::class,
            'user.billing.updated' => UserRegistered::class,
            'user.billing.tax_id.updated' => UserRegistered::class,
            'user.card.created' => UserRegistered::class,
            'user.created' => UserRegistered::class,
            'user.email.changed' => UserRegistered::class,
            'user.email.verified' => UserRegistered::class,
            'user.email_status.bounced' => UserRegistered::class,
            'user.email_status.delivered' => UserRegistered::class,
            'user.email_status.dropped' => UserRegistered::class,
            'user.marketing.opted_in' => UserRegistered::class,
            'user.marketing.opted_out' => UserRegistered::class,
            'user.marketing.reset' => UserRegistered::class,
            'user.name.changed' => UserRegistered::class,
            'user.support.contacted' => UserRegistered::class,
            'user.trial.started' => TrialStarted::class,

            // Webhook events
            'webhook.created' => UnknownEvent::class,
            'webhook.deleted' => UnknownEvent::class,
            'webhook.updated' => UnknownEvent::class,

            // WordPress specific events
            'addon.free.downloaded' => UnknownEvent::class,
            'addon.premium.downloaded' => UnknownEvent::class,
            'install.extensions.opt_in' => InstallActivated::class,
            'install.extensions.opt_out' => InstallActivated::class,
            'install.ownership.candidate.confirmed' => InstallActivated::class,
            'install.ownership.completed' => InstallActivated::class,
            'install.ownership.initiated' => InstallActivated::class,
            'install.ownership.owner.confirmed' => InstallActivated::class,
            'install.site.opt_in' => InstallActivated::class,
            'install.site.opt_out' => InstallActivated::class,
            'install.user.opt_in' => InstallActivated::class,
            'install.user.opt_out' => InstallActivated::class,
            'license.site.blacklisted' => LicenseActivated::class,
            'license.blacklisted_site.deleted' => LicenseActivated::class,
            'license.site.whitelisted' => LicenseActivated::class,
            'license.whitelisted_site.deleted' => LicenseActivated::class,
            'plugin.free.downloaded' => UnknownEvent::class,
            'plugin.premium.downloaded' => UnknownEvent::class,
            'plugin.version.deleted' => UnknownEvent::class,
            'plugin.version.deployed' => UnknownEvent::class,
            'plugin.version.released' => UnknownEvent::class,
            'plugin.version.beta.released' => UnknownEvent::class,
            'plugin.version.release.suspended' => UnknownEvent::class,
            'plugin.version.updated' => UnknownEvent::class,
            'pricing.visit' => UnknownEvent::class,
        ];
    }

    /**
     * Get supported event types
     *
     * @return array List of supported event types
     */
    public function getSupportedEventTypes(): array
    {
        return array_keys($this->eventMap);
    }

    /**
     * Check if an event type is supported
     *
     * @param string $eventType The event type to check
     * @return bool True if supported
     */
    public function isEventTypeSupported(string $eventType): bool
    {
        return isset($this->eventMap[$eventType]);
    }

    /**
     * Add custom event type mapping
     *
     * @param string $eventType The event type
     * @param string $eventClass The event class (must extend WebhookEvent)
     * @return self
     */
    public function addEventType(string $eventType, string $eventClass): self
    {
        if (!is_subclass_of($eventClass, WebhookEvent::class)) {
            throw new \InvalidArgumentException(
                "Event class {$eventClass} must extend " . WebhookEvent::class
            );
        }

        $this->eventMap[$eventType] = $eventClass;
        return $this;
    }

    /**
     * Remove event type mapping
     *
     * @param string $eventType The event type to remove
     * @return self
     */
    public function removeEventType(string $eventType): self
    {
        unset($this->eventMap[$eventType]);
        return $this;
    }

    /**
     * Get the webhook validator instance
     *
     * @return WebhookValidator
     */
    public function getValidator(): WebhookValidator
    {
        return $this->validator;
    }

    /**
     * Validate webhook signature only
     *
     * @param string $payload The raw webhook payload
     * @param string $signature The signature from webhook headers
     * @return bool True if signature is valid
     * @throws WebhookException If validation fails
     */
    public function validateSignature(string $payload, string $signature): bool
    {
        return $this->validator->validateSignature($payload, $signature);
    }

    /**
     * Validate webhook timestamp only
     *
     * @param int $timestamp The timestamp from webhook headers
     * @return bool True if timestamp is valid
     * @throws WebhookException If validation fails
     */
    public function validateTimestamp(int $timestamp): bool
    {
        return $this->validator->validateTimestamp($timestamp);
    }

    /**
     * Create webhook handler from configuration
     *
     * @param array $config Configuration array with 'webhook_secret' and optional 'timestamp_tolerance'
     * @return self
     * @throws WebhookException If webhook secret is missing
     */
    public static function fromConfig(array $config): self
    {
        $webhookSecret = $config['webhook_secret'] ?? '';
        if (empty($webhookSecret)) {
            throw WebhookException::missingWebhookSecret();
        }

        $timestampTolerance = $config['timestamp_tolerance'] ?? 300;

        return new self($webhookSecret, $timestampTolerance);
    }
}