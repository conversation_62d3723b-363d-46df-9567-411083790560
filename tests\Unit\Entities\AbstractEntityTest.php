<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Entities;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Entities\AbstractEntity;
use DateTime;
use InvalidArgumentException;

/**
 * Test implementation of AbstractEntity for testing purposes
 */
class TestEntity extends AbstractEntity
{
    protected array $casts = [
        'id' => 'integer',
        'price' => 'float',
        'active' => 'boolean',
        'metadata' => 'array',
        'config' => 'object',
    ];

    protected array $dates = [
        'created_at',
        'updated_at',
    ];
}

/**
 * Unit tests for AbstractEntity
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Entities\AbstractEntity::class)]
class AbstractEntityTest extends TestCase
{
    private TestEntity $entity;

    protected function setUp(): void
    {
        $this->entity = new TestEntity();
    }

    public function testConstructorWithData(): void
    {
        $data = [
            'id' => '123',
            'name' => 'Test Product',
            'active' => 'true',
            'created_at' => '2023-01-01 12:00:00',
        ];

        $entity = new TestEntity($data);

        $this->assertEquals(123, $entity->getAttribute('id'));
        $this->assertEquals('Test Product', $entity->getAttribute('name'));
        $this->assertTrue($entity->getAttribute('active'));
        $this->assertInstanceOf(DateTime::class, $entity->getAttribute('created_at'));
    }

    public function testFillMethod(): void
    {
        $data = [
            'id' => '456',
            'title' => 'Another Product',
        ];

        $result = $this->entity->fill($data);

        $this->assertSame($this->entity, $result);
        $this->assertEquals(456, $this->entity->getAttribute('id'));
        $this->assertEquals('Another Product', $this->entity->getAttribute('title'));
    }

    public function testGetAttributeWithDefault(): void
    {
        $this->assertEquals('default', $this->entity->getAttribute('nonexistent', 'default'));
        $this->assertNull($this->entity->getAttribute('nonexistent'));
    }

    public function testSetAttribute(): void
    {
        $result = $this->entity->setAttribute('test_key', 'test_value');

        $this->assertSame($this->entity, $result);
        $this->assertEquals('test_value', $this->entity->getAttribute('test_key'));
    }

    public function testHasAttribute(): void
    {
        $this->assertFalse($this->entity->hasAttribute('test_key'));
        
        $this->entity->setAttribute('test_key', 'test_value');
        
        $this->assertTrue($this->entity->hasAttribute('test_key'));
    }

    public function testGetAttributes(): void
    {
        $data = [
            'id' => '789',
            'name' => 'Test',
            'active' => 'false',
        ];

        $this->entity->fill($data);
        $attributes = $this->entity->getAttributes();

        $this->assertEquals(789, $attributes['id']);
        $this->assertEquals('Test', $attributes['name']);
        $this->assertFalse($attributes['active']);
    }

    public function testToArray(): void
    {
        $data = [
            'id' => '100',
            'title' => 'Product Title',
        ];

        $this->entity->fill($data);
        $array = $this->entity->toArray();

        $this->assertEquals(100, $array['id']);
        $this->assertEquals('Product Title', $array['title']);
    }

    public function testToJson(): void
    {
        $data = [
            'id' => '200',
            'name' => 'JSON Test',
        ];

        $this->entity->fill($data);
        $json = $this->entity->toJson();

        $decoded = json_decode($json, true);
        $this->assertEquals(200, $decoded['id']);
        $this->assertEquals('JSON Test', $decoded['name']);
    }

    public function testToJsonWithInvalidData(): void
    {
        // Create an entity with data that can't be JSON encoded
        $this->entity->setAttribute('invalid', "\xB1\x31");
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Failed to encode entity to JSON');
        
        $this->entity->toJson();
    }

    public function testIsDirty(): void
    {
        $data = ['id' => '300', 'name' => 'Original'];
        $entity = new TestEntity($data);

        // Should not be dirty initially
        $this->assertFalse($entity->isDirty());
        $this->assertFalse($entity->isDirty('id'));
        $this->assertFalse($entity->isDirty('name'));

        // Modify an attribute
        $entity->setAttribute('name', 'Modified');

        $this->assertTrue($entity->isDirty());
        $this->assertFalse($entity->isDirty('id'));
        $this->assertTrue($entity->isDirty('name'));

        // Add new attribute
        $entity->setAttribute('new_field', 'new_value');

        $this->assertTrue($entity->isDirty());
        $this->assertTrue($entity->isDirty('new_field'));
    }

    public function testGetOriginal(): void
    {
        $data = ['id' => '400', 'name' => 'Original Name'];
        $entity = new TestEntity($data);

        $this->assertEquals($data, $entity->getOriginal());
        $this->assertEquals('400', $entity->getOriginal('id'));
        $this->assertEquals('Original Name', $entity->getOriginal('name'));
        $this->assertEquals('default', $entity->getOriginal('nonexistent', 'default'));

        // Modify and check original remains unchanged
        $entity->setAttribute('name', 'New Name');
        $this->assertEquals('Original Name', $entity->getOriginal('name'));
    }

    public function testCastInteger(): void
    {
        $this->entity->setAttribute('id', '500');
        $this->assertSame(500, $this->entity->getAttribute('id'));

        $this->entity->setAttribute('id', 600.7);
        $this->assertSame(600, $this->entity->getAttribute('id'));
    }

    public function testCastFloat(): void
    {
        $this->entity->setAttribute('price', '19.99');
        $this->assertSame(19.99, $this->entity->getAttribute('price'));

        $this->entity->setAttribute('price', 25);
        $this->assertSame(25.0, $this->entity->getAttribute('price'));
    }

    public function testCastBoolean(): void
    {
        // Test various truthy values
        $truthyValues = ['true', '1', 'yes', 'on', 'TRUE', 'Yes', 'ON', true, 1];
        foreach ($truthyValues as $value) {
            $this->entity->setAttribute('active', $value);
            $this->assertTrue($this->entity->getAttribute('active'), "Failed for value: " . var_export($value, true));
        }

        // Test various falsy values
        $falsyValues = ['false', '0', 'no', 'off', '', 'FALSE', 'No', 'OFF', false, 0];
        foreach ($falsyValues as $value) {
            $this->entity->setAttribute('active', $value);
            $this->assertFalse($this->entity->getAttribute('active'), "Failed for value: " . var_export($value, true));
        }
    }

    public function testCastArray(): void
    {
        // Test with array
        $array = ['key' => 'value'];
        $this->entity->setAttribute('metadata', $array);
        $this->assertEquals($array, $this->entity->getAttribute('metadata'));

        // Test with JSON string
        $json = '{"key":"value","number":123}';
        $this->entity->setAttribute('metadata', $json);
        $expected = ['key' => 'value', 'number' => 123];
        $this->assertEquals($expected, $this->entity->getAttribute('metadata'));
    }

    public function testCastObject(): void
    {
        // Test with object
        $object = (object) ['key' => 'value'];
        $this->entity->setAttribute('config', $object);
        $this->assertEquals($object, $this->entity->getAttribute('config'));

        // Test with JSON string
        $json = '{"key":"value","number":123}';
        $this->entity->setAttribute('config', $json);
        $result = $this->entity->getAttribute('config');
        $this->assertIsObject($result);
        $this->assertEquals('value', $result->key);
        $this->assertEquals(123, $result->number);
    }

    public function testCastDateTime(): void
    {
        $dateString = '2023-06-15 14:30:00';
        $this->entity->setAttribute('created_at', $dateString);
        
        $result = $this->entity->getAttribute('created_at');
        $this->assertInstanceOf(DateTime::class, $result);
    }

    public function testCastWithNullValue(): void
    {
        $this->entity->setAttribute('id', null);
        $this->assertNull($this->entity->getAttribute('id'));

        $this->entity->setAttribute('active', null);
        $this->assertNull($this->entity->getAttribute('active'));
    }

    public function testMagicGetSet(): void
    {
        $this->entity->test_property = 'magic_value';
        $this->assertEquals('magic_value', $this->entity->test_property);
    }

    public function testMagicIsset(): void
    {
        $this->assertFalse(isset($this->entity->test_property));
        
        $this->entity->test_property = 'value';
        $this->assertTrue(isset($this->entity->test_property));
    }

    public function testMagicUnset(): void
    {
        $this->entity->test_property = 'value';
        $this->assertTrue(isset($this->entity->test_property));
        
        unset($this->entity->test_property);
        $this->assertFalse(isset($this->entity->test_property));
    }
}