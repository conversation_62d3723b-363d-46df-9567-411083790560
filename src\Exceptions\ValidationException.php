<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

/**
 * Exception thrown when data validation fails.
 * 
 * This exception is thrown when entity data doesn't meet the required
 * validation criteria or when API responses contain invalid data.
 */
class ValidationException extends FreemiusException
{
    /**
     * Validation errors.
     */
    private array $errors;

    /**
     * Create a new validation exception.
     */
    public function __construct(string $message, array $errors = [], int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
    }

    /**
     * Get validation errors.
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Check if there are validation errors.
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Get errors for a specific field.
     */
    public function getFieldErrors(string $field): array
    {
        return $this->errors[$field] ?? [];
    }

    /**
     * Check if a specific field has errors.
     */
    public function hasFieldErrors(string $field): bool
    {
        return isset($this->errors[$field]) && !empty($this->errors[$field]);
    }
}