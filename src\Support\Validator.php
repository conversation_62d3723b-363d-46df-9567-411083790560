<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

use DateTime;
use InvalidArgumentException;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Validator class for Freemius data schema validation.
 * 
 * Provides validation methods for common Freemius data types and formats,
 * ensuring data integrity and proper type casting throughout the SDK.
 */
class Validator
{
    /**
     * Validation rules for different data types.
     */
    private const VALIDATION_RULES = [
        'required' => 'validateRequired',
        'string' => 'validateString',
        'integer' => 'validateInteger',
        'float' => 'validateFloat',
        'boolean' => 'validateBoolean',
        'array' => 'validateArray',
        'email' => 'validateEmail',
        'url' => 'validateUrl',
        'datetime' => 'validateDateTime',
        'currency' => 'validateCurrency',
        'freemius_id' => 'validateFreemiusId',
        'slug' => 'validateSlug',
        'version' => 'validateVersion',
    ];

    /**
     * Validation errors collected during validation.
     */
    private array $errors = [];

    /**
     * Validate data against a set of rules.
     */
    public function validate(array $data, array $rules): array
    {
        $this->errors = [];
        $validatedData = [];

        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $fieldRules = is_string($fieldRules) ? explode('|', $fieldRules) : $fieldRules;

            try {
                $validatedValue = $this->validateField($field, $value, $fieldRules);
                if ($validatedValue !== null || in_array('required', $fieldRules)) {
                    $validatedData[$field] = $validatedValue;
                }
            } catch (InvalidArgumentException $e) {
                $this->errors[$field] = $e->getMessage();
            }
        }

        if (!empty($this->errors)) {
            throw new ValidationException('Validation failed', $this->errors);
        }

        return $validatedData;
    }

    /**
     * Validate a single field against its rules.
     */
    private function validateField(string $field, mixed $value, array $rules): mixed
    {
        $isRequired = in_array('required', $rules);
        
        // Handle null values
        if ($value === null) {
            if ($isRequired) {
                throw new InvalidArgumentException("Field '{$field}' is required");
            }
            return null;
        }

        // Apply validation rules
        foreach ($rules as $rule) {
            if ($rule === 'required') {
                continue; // Already handled above
            }

            $value = $this->applyValidationRule($field, $value, $rule);
        }

        return $value;
    }

    /**
     * Apply a single validation rule to a value.
     */
    private function applyValidationRule(string $field, mixed $value, string $rule): mixed
    {
        // Handle parameterized rules (e.g., "min:5", "max:100")
        $ruleParts = explode(':', $rule, 2);
        $ruleName = $ruleParts[0];
        $ruleParam = $ruleParts[1] ?? null;

        if (!isset(self::VALIDATION_RULES[$ruleName])) {
            throw new InvalidArgumentException("Unknown validation rule: {$ruleName}");
        }

        $method = self::VALIDATION_RULES[$ruleName];
        
        if (!method_exists($this, $method)) {
            throw new InvalidArgumentException("Validation method not found: {$method}");
        }

        return $this->$method($field, $value, $ruleParam);
    }

    /**
     * Validate required field.
     */
    private function validateRequired(string $field, mixed $value, ?string $param = null): mixed
    {
        if ($value === null || $value === '' || (is_array($value) && empty($value))) {
            throw new InvalidArgumentException("Field '{$field}' is required");
        }
        return $value;
    }

    /**
     * Validate and cast to string.
     */
    private function validateString(string $field, mixed $value, ?string $param = null): string
    {
        if (!is_scalar($value) && !is_null($value)) {
            throw new InvalidArgumentException("Field '{$field}' must be a string");
        }
        return (string) $value;
    }

    /**
     * Validate and cast to integer.
     */
    private function validateInteger(string $field, mixed $value, ?string $param = null): int
    {
        if (!is_numeric($value)) {
            throw new InvalidArgumentException("Field '{$field}' must be an integer");
        }
        return (int) $value;
    }

    /**
     * Validate and cast to float.
     */
    private function validateFloat(string $field, mixed $value, ?string $param = null): float
    {
        if (!is_numeric($value)) {
            throw new InvalidArgumentException("Field '{$field}' must be a number");
        }
        return (float) $value;
    }

    /**
     * Validate and cast to boolean.
     */
    private function validateBoolean(string $field, mixed $value, ?string $param = null): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            $lower = strtolower(trim($value));
            if (in_array($lower, ['true', '1', 'yes', 'on'])) {
                return true;
            }
            if (in_array($lower, ['false', '0', 'no', 'off', ''])) {
                return false;
            }
        }
        
        if (is_numeric($value)) {
            return (bool) $value;
        }

        throw new InvalidArgumentException("Field '{$field}' must be a boolean value");
    }

    /**
     * Validate array.
     */
    private function validateArray(string $field, mixed $value, ?string $param = null): array
    {
        if (!is_array($value)) {
            throw new InvalidArgumentException("Field '{$field}' must be an array");
        }
        return $value;
    }

    /**
     * Validate email address.
     */
    private function validateEmail(string $field, mixed $value, ?string $param = null): string
    {
        $value = $this->validateString($field, $value);
        
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException("Field '{$field}' must be a valid email address");
        }
        
        return $value;
    }

    /**
     * Validate URL.
     */
    private function validateUrl(string $field, mixed $value, ?string $param = null): string
    {
        $value = $this->validateString($field, $value);
        
        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException("Field '{$field}' must be a valid URL");
        }
        
        return $value;
    }

    /**
     * Validate and convert to DateTime.
     */
    private function validateDateTime(string $field, mixed $value, ?string $param = null): DateTime
    {
        if ($value instanceof DateTime) {
            return $value;
        }

        if (is_numeric($value)) {
            return new DateTime('@' . $value);
        }

        if (is_string($value)) {
            try {
                return new DateTime($value);
            } catch (\Exception $e) {
                throw new InvalidArgumentException("Field '{$field}' must be a valid date/time format");
            }
        }

        throw new InvalidArgumentException("Field '{$field}' must be a valid date/time");
    }

    /**
     * Validate currency amount (maintains precision for financial calculations).
     */
    private function validateCurrency(string $field, mixed $value, ?string $param = null): string
    {
        if (!is_numeric($value)) {
            throw new InvalidArgumentException("Field '{$field}' must be a valid currency amount");
        }

        // Convert to string to maintain precision
        $amount = number_format((float) $value, 2, '.', '');
        
        if ((float) $amount < 0) {
            throw new InvalidArgumentException("Field '{$field}' must be a positive currency amount");
        }

        return $amount;
    }

    /**
     * Validate Freemius ID (positive integer).
     */
    private function validateFreemiusId(string $field, mixed $value, ?string $param = null): int
    {
        $id = $this->validateInteger($field, $value);
        
        if ($id <= 0) {
            throw new InvalidArgumentException("Field '{$field}' must be a positive integer");
        }
        
        return $id;
    }

    /**
     * Validate slug format (lowercase, alphanumeric with hyphens).
     */
    private function validateSlug(string $field, mixed $value, ?string $param = null): string
    {
        $value = $this->validateString($field, $value);
        
        if (!preg_match('/^[a-z0-9-]+$/', $value)) {
            throw new InvalidArgumentException("Field '{$field}' must be a valid slug (lowercase letters, numbers, and hyphens only)");
        }
        
        return $value;
    }

    /**
     * Validate version string (semantic versioning).
     */
    private function validateVersion(string $field, mixed $value, ?string $param = null): string
    {
        $value = $this->validateString($field, $value);
        
        if (!preg_match('/^\d+\.\d+(\.\d+)?(-[a-zA-Z0-9-]+)?$/', $value)) {
            throw new InvalidArgumentException("Field '{$field}' must be a valid version string");
        }
        
        return $value;
    }

    /**
     * Get validation errors.
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Check if validation has errors.
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Clear validation errors.
     */
    public function clearErrors(): void
    {
        $this->errors = [];
    }
}