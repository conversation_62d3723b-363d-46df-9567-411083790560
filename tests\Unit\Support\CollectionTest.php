<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Support;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Support\Collection;
use Freemius\SDK\Entities\Product;

/**
 * Unit tests for Collection
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Support\Collection::class)]
class CollectionTest extends TestCase
{
    private Collection $collection;
    private array $sampleData;
    private array $samplePagination;

    protected function setUp(): void
    {
        $this->sampleData = [
            ['id' => 1, 'name' => 'Item 1', 'active' => true],
            ['id' => 2, 'name' => 'Item 2', 'active' => false],
            ['id' => 3, 'name' => 'Item 3', 'active' => true],
        ];
        
        $this->samplePagination = [
            'current_page' => 1,
            'total_pages' => 3,
            'total' => 10,
            'per_page' => 3,
            'offset' => 0,
            'count' => 3,
        ];
        
        $this->collection = new Collection($this->sampleData, $this->samplePagination);
    }

    public function testConstructorWithArray(): void
    {
        $collection = new Collection($this->sampleData);
        $this->assertCount(3, $collection);
    }

    public function testConstructorWithEmptyArray(): void
    {
        $collection = new Collection([]);
        $this->assertCount(0, $collection);
    }

    public function testConstructorWithPagination(): void
    {
        $collection = new Collection($this->sampleData, $this->samplePagination);
        $this->assertCount(3, $collection);
        $this->assertEquals($this->samplePagination, $collection->getPagination());
    }

    public function testAll(): void
    {
        $this->assertEquals($this->sampleData, $this->collection->all());
    }

    public function testCount(): void
    {
        $this->assertEquals(3, $this->collection->count());
        $this->assertEquals(3, count($this->collection));
    }

    public function testIsEmpty(): void
    {
        $this->assertFalse($this->collection->isEmpty());
        
        $emptyCollection = new Collection([]);
        $this->assertTrue($emptyCollection->isEmpty());
    }

    public function testIsNotEmpty(): void
    {
        $this->assertTrue($this->collection->isNotEmpty());
        
        $emptyCollection = new Collection([]);
        $this->assertFalse($emptyCollection->isNotEmpty());
    }

    public function testFirst(): void
    {
        $first = $this->collection->first();
        $this->assertEquals(['id' => 1, 'name' => 'Item 1', 'active' => true], $first);
        
        $emptyCollection = new Collection([]);
        $this->assertNull($emptyCollection->first());
    }

    public function testLast(): void
    {
        $last = $this->collection->last();
        $this->assertEquals(['id' => 3, 'name' => 'Item 3', 'active' => true], $last);
        
        $emptyCollection = new Collection([]);
        $this->assertNull($emptyCollection->last());
    }

    public function testGet(): void
    {
        $this->assertEquals(['id' => 2, 'name' => 'Item 2', 'active' => false], $this->collection->get(1));
        $this->assertNull($this->collection->get(10));
    }

    public function testFilter(): void
    {
        $filtered = $this->collection->filter(function ($item) {
            return $item['active'] === true;
        });
        
        $this->assertInstanceOf(Collection::class, $filtered);
        $this->assertCount(2, $filtered);
        
        $filteredArray = $filtered->all();
        $this->assertEquals('Item 1', $filteredArray[0]['name']);
        // After filtering, the array is re-indexed, so the second item is at index 1
        $this->assertEquals('Item 3', $filteredArray[1]['name']);
    }

    public function testMap(): void
    {
        $mapped = $this->collection->map(function ($item) {
            return $item['name'];
        });
        
        $this->assertInstanceOf(Collection::class, $mapped);
        $this->assertEquals(['Item 1', 'Item 2', 'Item 3'], $mapped->all());
    }

    public function testReduce(): void
    {
        $sum = $this->collection->reduce(function ($carry, $item) {
            return $carry + $item['id'];
        }, 0);
        
        $this->assertEquals(6, $sum);
    }

    public function testSlice(): void
    {
        $sliced = $this->collection->slice(1, 1);
        
        $this->assertInstanceOf(Collection::class, $sliced);
        $this->assertCount(1, $sliced);
        $this->assertEquals(['id' => 2, 'name' => 'Item 2', 'active' => false], $sliced->first());
    }

    public function testTake(): void
    {
        $taken = $this->collection->take(2);
        
        $this->assertInstanceOf(Collection::class, $taken);
        $this->assertCount(2, $taken);
        
        $items = $taken->all();
        $this->assertEquals(1, $items[0]['id']);
        $this->assertEquals(2, $items[1]['id']);
    }

    public function testSkip(): void
    {
        $skipped = $this->collection->skip(1);
        
        $this->assertInstanceOf(Collection::class, $skipped);
        $this->assertCount(2, $skipped);
        
        $items = $skipped->all();
        $this->assertEquals(2, $items[0]['id']);
        $this->assertEquals(3, $items[1]['id']);
    }

    public function testToArray(): void
    {
        // Create collection with entity objects that have toArray method
        $product = new Product(['id' => 1, 'title' => 'Test Product']);
        $entityCollection = new Collection([$product]);
        
        $array = $entityCollection->toArray();
        
        $this->assertIsArray($array);
        $this->assertCount(1, $array);
        $this->assertIsArray($array[0]); // Should be converted to array
    }

    public function testToJson(): void
    {
        // Create collection with entity objects to avoid method_exists error
        $product = new Product(['id' => 1, 'title' => 'Test Product']);
        $entityCollection = new Collection([$product], $this->samplePagination);
        
        $json = $entityCollection->toJson();
        
        $this->assertJson($json);
        $decoded = json_decode($json, true);
        $this->assertArrayHasKey('data', $decoded);
        $this->assertArrayHasKey('pagination', $decoded);
    }

    public function testGetPagination(): void
    {
        $this->assertEquals($this->samplePagination, $this->collection->getPagination());
    }

    public function testGetCurrentPage(): void
    {
        $this->assertEquals(1, $this->collection->getCurrentPage());
        
        $noPaginationCollection = new Collection([]);
        $this->assertNull($noPaginationCollection->getCurrentPage());
    }

    public function testGetTotalPages(): void
    {
        $this->assertEquals(3, $this->collection->getTotalPages());
    }

    public function testGetTotal(): void
    {
        $this->assertEquals(10, $this->collection->getTotal());
    }

    public function testGetPerPage(): void
    {
        $this->assertEquals(3, $this->collection->getPerPage());
    }

    public function testHasNextPage(): void
    {
        $this->assertTrue($this->collection->hasNextPage());
        
        $lastPageCollection = new Collection([], ['current_page' => 3, 'total_pages' => 3]);
        $this->assertFalse($lastPageCollection->hasNextPage());
    }

    public function testHasPreviousPage(): void
    {
        $this->assertFalse($this->collection->hasPreviousPage());
        
        $secondPageCollection = new Collection([], ['current_page' => 2, 'total_pages' => 3]);
        $this->assertTrue($secondPageCollection->hasPreviousPage());
    }

    public function testGetCurrentOffset(): void
    {
        $this->assertEquals(0, $this->collection->getCurrentOffset());
    }

    public function testGetCount(): void
    {
        $this->assertEquals(3, $this->collection->getCount());
    }

    public function testHasMoreItems(): void
    {
        $this->assertTrue($this->collection->hasMoreItems());
        
        $completeCollection = new Collection([], ['total' => 3, 'offset' => 0, 'count' => 3]);
        $this->assertFalse($completeCollection->hasMoreItems());
    }

    public function testGetPaginationSummary(): void
    {
        $summary = $this->collection->getPaginationSummary();
        $this->assertIsString($summary);
        $this->assertStringContainsString('1-3', $summary);
        $this->assertStringContainsString('10', $summary);
    }

    public function testIsCompletelyEmpty(): void
    {
        $this->assertFalse($this->collection->isCompletelyEmpty());
        
        $emptyCollection = new Collection([]);
        $this->assertTrue($emptyCollection->isCompletelyEmpty());
    }

    public function testGetNextPageOffset(): void
    {
        $nextOffset = $this->collection->getNextPageOffset();
        $this->assertEquals(3, $nextOffset);
    }

    public function testGetPreviousPageOffset(): void
    {
        $prevOffset = $this->collection->getPreviousPageOffset();
        $this->assertEquals(0, $prevOffset);
        
        $secondPageCollection = new Collection([], ['offset' => 3, 'count' => 3]);
        $this->assertEquals(0, $secondPageCollection->getPreviousPageOffset());
    }

    public function testArrayAccess(): void
    {
        // Test offsetExists
        $this->assertTrue(isset($this->collection[0]));
        $this->assertFalse(isset($this->collection[10]));
        
        // Test offsetGet
        $this->assertEquals(['id' => 1, 'name' => 'Item 1', 'active' => true], $this->collection[0]);
        
        // Test offsetSet
        $this->collection[3] = ['id' => 4, 'name' => 'Item 4', 'active' => false];
        $this->assertEquals(['id' => 4, 'name' => 'Item 4', 'active' => false], $this->collection[3]);
        
        // Test offsetUnset
        unset($this->collection[1]);
        $this->assertFalse(isset($this->collection[1]));
        $this->assertCount(3, $this->collection); // Still 3 because we added one and removed one
    }

    public function testIterator(): void
    {
        $items = [];
        foreach ($this->collection as $key => $item) {
            $items[$key] = $item;
        }
        
        $this->assertEquals($this->sampleData, $items);
    }

    public function testJsonSerialize(): void
    {
        // Create collection with entity objects to avoid method_exists error
        $product = new Product(['id' => 1, 'title' => 'Test Product']);
        $entityCollection = new Collection([$product], $this->samplePagination);
        
        $json = json_encode($entityCollection);
        
        $this->assertJson($json);
        $decoded = json_decode($json, true);
        $this->assertArrayHasKey('data', $decoded);
        $this->assertArrayHasKey('pagination', $decoded);
        $this->assertEquals($this->samplePagination, $decoded['pagination']);
    }

    public function testWithEntityObjects(): void
    {
        $productData = [
            'id' => 12345,
            'title' => 'Test Product',
            'slug' => 'test-product',
        ];
        $product = new Product($productData);
        
        $collection = new Collection([$product]);
        
        $this->assertCount(1, $collection);
        $this->assertInstanceOf(Product::class, $collection->first());
        $this->assertEquals(12345, $collection->first()->getId());
    }

    public function testChainedOperations(): void
    {
        $result = $this->collection
            ->filter(function ($item) {
                return $item['active'] === true;
            })
            ->map(function ($item) {
                return strtoupper($item['name']);
            })
            ->take(1);
        
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
        $this->assertEquals('ITEM 1', $result->first());
    }
}