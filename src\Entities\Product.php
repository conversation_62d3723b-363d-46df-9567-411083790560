<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Product entity representing a Freemius product.
 * 
 * A product is a software that is being sold on Freemius. It can be a 
 * WordPress plugin, theme, SaaS, or any other software.
 */
class Product extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'parent_plugin_id' => 'integer',
        'developer_id' => 'integer',
        'store_id' => 'integer',
        'default_plan_id' => 'integer',
        'money_back_period' => 'integer',
        'annual_renewals_discount' => 'integer',
        'is_released' => 'boolean',
        'is_sdk_required' => 'boolean',
        'is_pricing_visible' => 'boolean',
        'is_wp_org_compliant' => 'boolean',
        'installs_count' => 'integer',
        'active_installs_count' => 'integer',
        'free_releases_count' => 'integer',
        'premium_releases_count' => 'integer',
        'total_purchases' => 'integer',
        'total_subscriptions' => 'integer',
        'total_renewals' => 'integer',
        'total_failed_purchases' => 'integer',
        'earnings' => 'float',
        'is_static' => 'boolean',
        'environment' => 'integer',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
    ];

    /**
     * Get the product ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the product title.
     */
    public function getTitle(): string
    {
        return $this->getAttribute('title', '');
    }

    /**
     * Get the product slug.
     */
    public function getSlug(): string
    {
        return $this->getAttribute('slug', '');
    }

    /**
     * Get the product type.
     */
    public function getType(): string
    {
        return $this->getAttribute('type', 'plugin');
    }

    /**
     * Get the developer ID.
     */
    public function getDeveloperId(): int
    {
        return $this->getAttribute('developer_id');
    }

    /**
     * Get the store ID.
     */
    public function getStoreId(): int
    {
        return $this->getAttribute('store_id');
    }

    /**
     * Get the parent plugin ID (for add-ons).
     */
    public function getParentPluginId(): ?int
    {
        return $this->getAttribute('parent_plugin_id');
    }

    /**
     * Check if this product is an add-on.
     */
    public function isAddon(): bool
    {
        return $this->getParentPluginId() !== null;
    }

    /**
     * Get the product icon URL.
     */
    public function getIcon(): ?string
    {
        return $this->getAttribute('icon');
    }

    /**
     * Get the default plan ID.
     */
    public function getDefaultPlanId(): ?int
    {
        return $this->getAttribute('default_plan_id');
    }

    /**
     * Get the plans as an array of IDs.
     */
    public function getPlans(): array
    {
        $plans = $this->getAttribute('plans', '');
        return $plans ? array_map('intval', explode(',', $plans)) : [];
    }

    /**
     * Get the features as an array of IDs.
     */
    public function getFeatures(): array
    {
        $features = $this->getAttribute('features', '');
        return $features ? array_map('intval', explode(',', $features)) : [];
    }

    /**
     * Get the money-back guarantee period in days.
     */
    public function getMoneyBackPeriod(): int
    {
        return $this->getAttribute('money_back_period', 0);
    }

    /**
     * Get the refund policy.
     */
    public function getRefundPolicy(): string
    {
        return $this->getAttribute('refund_policy', 'flexible');
    }

    /**
     * Get the annual renewals discount percentage.
     */
    public function getAnnualRenewalsDiscount(): ?int
    {
        return $this->getAttribute('annual_renewals_discount');
    }

    /**
     * Get the renewals discount type.
     */
    public function getRenewalsDiscountType(): string
    {
        return $this->getAttribute('renewals_discount_type', 'percentage');
    }

    /**
     * Check if the product is released.
     */
    public function isReleased(): bool
    {
        return $this->getAttribute('is_released', true);
    }

    /**
     * Check if SDK is required.
     */
    public function isSdkRequired(): bool
    {
        return $this->getAttribute('is_sdk_required', true);
    }

    /**
     * Check if pricing is visible.
     */
    public function isPricingVisible(): bool
    {
        return $this->getAttribute('is_pricing_visible', true);
    }

    /**
     * Check if the product is WordPress.org compliant.
     */
    public function isWpOrgCompliant(): bool
    {
        return $this->getAttribute('is_wp_org_compliant', true);
    }

    /**
     * Get the total installs count.
     */
    public function getInstallsCount(): int
    {
        return $this->getAttribute('installs_count', 0);
    }

    /**
     * Get the active installs count.
     */
    public function getActiveInstallsCount(): int
    {
        return $this->getAttribute('active_installs_count', 0);
    }

    /**
     * Get the free releases count.
     */
    public function getFreeReleasesCount(): int
    {
        return $this->getAttribute('free_releases_count', 0);
    }

    /**
     * Get the premium releases count.
     */
    public function getPremiumReleasesCount(): int
    {
        return $this->getAttribute('premium_releases_count', 0);
    }

    /**
     * Get the total purchases count.
     */
    public function getTotalPurchases(): int
    {
        return $this->getAttribute('total_purchases', 0);
    }

    /**
     * Get the total subscriptions count.
     */
    public function getTotalSubscriptions(): int
    {
        return $this->getAttribute('total_subscriptions', 0);
    }

    /**
     * Get the total renewals count.
     */
    public function getTotalRenewals(): int
    {
        return $this->getAttribute('total_renewals', 0);
    }

    /**
     * Get the total failed purchases count.
     */
    public function getTotalFailedPurchases(): int
    {
        return $this->getAttribute('total_failed_purchases', 0);
    }

    /**
     * Get the total earnings.
     */
    public function getEarnings(): float
    {
        return $this->getAttribute('earnings', 0.0);
    }

    /**
     * Check if the product is static (widget or template).
     */
    public function isStatic(): bool
    {
        return $this->getAttribute('is_static', false);
    }

    /**
     * Get the environment (0 = production, 1 = sandbox).
     */
    public function getEnvironment(): int
    {
        return $this->getAttribute('environment', 0);
    }

    /**
     * Check if this is a sandbox product.
     */
    public function isSandbox(): bool
    {
        return $this->getEnvironment() === 1;
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the secret key.
     */
    public function getSecretKey(): ?string
    {
        return $this->getAttribute('secret_key');
    }

    /**
     * Get the public key.
     */
    public function getPublicKey(): ?string
    {
        return $this->getAttribute('public_key');
    }
}