# Technology Stack & Build System

## Core Requirements
- **PHP Version**: 8.0+ (strict requirement)
- **Architecture**: Pure PHP, no frameworks
- **Quality Standard**: Production-grade, commercial-ready code

## Dependencies

### Required Extensions
- `ext-curl` - HTTP client functionality
- `ext-json` - JSON processing

### Core Libraries (Allowed Only)
- `guzzlehttp/guzzle` ^7.9 - HTTP client
- `monolog/monolog` ^3.9 - Logging

### Development Dependencies
- `phpunit/phpunit` ^11.5 - Testing framework
- `symfony/var-dumper` ^7.1 - Debugging utilities
- `friendsofphp/php-cs-fixer` ^3.64 - Code style fixing

## Build & Development Commands

### Installation
```bash
composer install
```

### Testing
```bash
# Run PHPUnit tests
vendor/bin/phpunit
```

### Code Quality
```bash
# Fix code style
vendor/bin/php-cs-fixer fix

# Check code style without fixing
vendor/bin/php-cs-fixer fix --dry-run --diff
```

### Autoloading
- PSR-4 autoloading: `Freemius\SDK\` → `src/`
- Use `composer dump-autoload` after adding new classes

## Testing Strategy
- Manual verification only (no automated CI/CD)
- Focus on integration testing with Freemius API
- Use sandbox environment for safe testing