<?php

declare(strict_types=1);

/**
 * Integration Test Runner
 * 
 * This script demonstrates how to run integration tests with proper environment setup.
 * It can be used as a reference for CI/CD pipelines or local testing.
 */

// Check if we're running from the project root
if (!file_exists('vendor/autoload.php')) {
    echo "Error: Please run this script from the project root directory.\n";
    exit(1);
}

// Load environment variables from .env file if it exists
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            [$key, $value] = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');
            
            if (!empty($key) && !isset($_ENV[$key])) {
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
}

// Check for required environment variables
$requiredVars = [
    'FREEMIUS_TEST_BEARER_TOKEN' => 'Bearer token for Freemius sandbox API',
];

$optionalVars = [
    'FREEMIUS_TEST_WEBHOOK_SECRET' => 'Webhook secret for webhook testing',
];

$missingVars = [];
foreach ($requiredVars as $var => $description) {
    if (empty($_ENV[$var])) {
        $missingVars[] = "$var ($description)";
    }
}

if (!empty($missingVars)) {
    echo "Error: Missing required environment variables:\n";
    foreach ($missingVars as $var) {
        echo "  - $var\n";
    }
    echo "\nPlease set these environment variables before running integration tests.\n";
    echo "See tests/Integration/README.md for setup instructions.\n";
    exit(1);
}

// Display configuration
echo "Integration Test Configuration:\n";
echo "==============================\n";
foreach ($requiredVars as $var => $description) {
    $value = $_ENV[$var];
    $maskedValue = substr($value, 0, 8) . str_repeat('*', max(0, strlen($value) - 8));
    echo "$var: $maskedValue\n";
}

foreach ($optionalVars as $var => $description) {
    if (!empty($_ENV[$var])) {
        $value = $_ENV[$var];
        $maskedValue = substr($value, 0, 8) . str_repeat('*', max(0, strlen($value) - 8));
        echo "$var: $maskedValue\n";
    } else {
        echo "$var: (not set)\n";
    }
}

echo "\n";

// Parse command line arguments
$args = array_slice($argv, 1);
$phpunitArgs = [];
$showHelp = false;

foreach ($args as $arg) {
    if ($arg === '--help' || $arg === '-h') {
        $showHelp = true;
        break;
    }
    $phpunitArgs[] = $arg;
}

if ($showHelp) {
    echo "Integration Test Runner\n";
    echo "======================\n\n";
    echo "Usage: php tests/Integration/run-integration-tests.php [phpunit-options]\n\n";
    echo "Examples:\n";
    echo "  php tests/Integration/run-integration-tests.php\n";
    echo "  php tests/Integration/run-integration-tests.php --filter testProductsListEndpoint\n";
    echo "  php tests/Integration/run-integration-tests.php tests/Integration/FreemiusSDKIntegrationTest.php\n";
    echo "  php tests/Integration/run-integration-tests.php tests/Integration/ResourcesIntegrationTest.php\n";
    echo "  php tests/Integration/run-integration-tests.php tests/Integration/AdvancedFeaturesIntegrationTest.php\n";
    echo "  php tests/Integration/run-integration-tests.php tests/Integration/ErrorScenariosIntegrationTest.php\n";
    echo "  php tests/Integration/run-integration-tests.php --group integration\n\n";
    echo "Environment Variables:\n";
    foreach (array_merge($requiredVars, $optionalVars) as $var => $description) {
        echo "  $var: $description\n";
    }
    echo "\nFor more information, see tests/Integration/README.md\n";
    exit(0);
}

// Build PHPUnit command
$phpunitCmd = [
    'vendor/bin/phpunit',
    '--configuration', 'phpunit.xml',
];

// Add default arguments if none provided
if (empty($phpunitArgs)) {
    $phpunitArgs = ['tests/Integration/'];
}

$phpunitCmd = array_merge($phpunitCmd, $phpunitArgs);

// Display command being executed
echo "Running: " . implode(' ', $phpunitCmd) . "\n";
echo str_repeat('=', 50) . "\n\n";

// Execute PHPUnit
$descriptorSpec = [
    0 => ['pipe', 'r'],  // stdin
    1 => ['pipe', 'w'],  // stdout
    2 => ['pipe', 'w'],  // stderr
];

$process = proc_open(implode(' ', $phpunitCmd), $descriptorSpec, $pipes);

if (is_resource($process)) {
    // Close stdin
    fclose($pipes[0]);
    
    // Read stdout and stderr
    $stdout = stream_get_contents($pipes[1]);
    $stderr = stream_get_contents($pipes[2]);
    
    fclose($pipes[1]);
    fclose($pipes[2]);
    
    // Get exit code
    $exitCode = proc_close($process);
    
    // Output results
    if (!empty($stdout)) {
        echo $stdout;
    }
    
    if (!empty($stderr)) {
        echo $stderr;
    }
    
    // Exit with same code as PHPUnit
    exit($exitCode);
} else {
    echo "Error: Failed to execute PHPUnit command.\n";
    exit(1);
}