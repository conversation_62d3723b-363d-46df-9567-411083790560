<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

use DateTime;
use DateTimeInterface;
use DateTimeZone;
use InvalidArgumentException;

/**
 * DateTime helper for handling Freemius timestamp formats.
 * 
 * Provides utilities for parsing and formatting DateTime objects
 * according to Freemius API specifications and common timestamp formats.
 */
class DateTimeHelper
{
    /**
     * Common Freemius timestamp formats.
     */
    private const FREEMIUS_FORMATS = [
        'Y-m-d H:i:s',           // 2023-12-01 10:30:00
        'Y-m-d\TH:i:s\Z',        // 2023-12-01T10:30:00Z
        'Y-m-d\TH:i:sP',         // 2023-12-01T10:30:00+00:00
        'Y-m-d\TH:i:s.u\Z',      // 2023-12-01T10:30:00.123456Z
        'Y-m-d\TH:i:s.uP',       // 2023-12-01T10:30:00.123456+00:00
        'Y-m-d',                 // 2023-12-01
    ];

    /**
     * Default timezone for Freemius API (UTC).
     */
    private static DateTimeZone $defaultTimezone;

    /**
     * Initialize default timezone.
     */
    private static function initializeDefaultTimezone(): void
    {
        if (!isset(self::$defaultTimezone)) {
            self::$defaultTimezone = new DateTimeZone('UTC');
        }
    }

    /**
     * Parse a Freemius timestamp into a DateTime object.
     */
    public static function parse(mixed $value): ?DateTime
    {
        if ($value === null) {
            return null;
        }

        if ($value instanceof DateTimeInterface) {
            return DateTime::createFromInterface($value);
        }

        // Handle Unix timestamps
        if (is_numeric($value)) {
            return new DateTime('@' . $value);
        }

        if (!is_string($value)) {
            throw new InvalidArgumentException('DateTime value must be a string, number, or DateTime instance');
        }

        // Try parsing with common Freemius formats
        foreach (self::FREEMIUS_FORMATS as $format) {
            $dateTime = DateTime::createFromFormat($format, $value);
            if ($dateTime !== false) {
                // Ensure UTC timezone for consistency
                if ($dateTime->getTimezone()->getName() !== 'UTC') {
                    $dateTime->setTimezone(self::getDefaultTimezone());
                }
                return $dateTime;
            }
        }

        // Fallback to PHP's built-in parsing
        try {
            $dateTime = new DateTime($value);
            // Convert to UTC for consistency
            $dateTime->setTimezone(self::getDefaultTimezone());
            return $dateTime;
        } catch (\Exception $e) {
            throw new InvalidArgumentException("Unable to parse datetime: {$value}");
        }
    }

    /**
     * Format a DateTime object for Freemius API requests.
     */
    public static function format(DateTimeInterface $dateTime, string $format = 'Y-m-d\TH:i:s\Z'): string
    {
        // Convert to UTC for API consistency
        if ($dateTime->getTimezone()->getName() !== 'UTC') {
            $utcDateTime = DateTime::createFromInterface($dateTime);
            $utcDateTime->setTimezone(self::getDefaultTimezone());
            return $utcDateTime->format($format);
        }

        return $dateTime->format($format);
    }

    /**
     * Convert a DateTime to Unix timestamp.
     */
    public static function toTimestamp(DateTimeInterface $dateTime): int
    {
        return $dateTime->getTimestamp();
    }

    /**
     * Create a DateTime from Unix timestamp.
     */
    public static function fromTimestamp(int $timestamp): DateTime
    {
        return new DateTime('@' . $timestamp);
    }

    /**
     * Get the current DateTime in UTC.
     */
    public static function now(): DateTime
    {
        return new DateTime('now', self::getDefaultTimezone());
    }

    /**
     * Check if a string is a valid Freemius timestamp format.
     */
    public static function isValidFormat(string $value): bool
    {
        foreach (self::FREEMIUS_FORMATS as $format) {
            if (DateTime::createFromFormat($format, $value) !== false) {
                return true;
            }
        }

        // Try PHP's built-in parsing as fallback
        try {
            new DateTime($value);
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Convert a DateTime to ISO 8601 format (Freemius standard).
     */
    public static function toIso8601(DateTimeInterface $dateTime): string
    {
        return self::format($dateTime, 'Y-m-d\TH:i:s\Z');
    }

    /**
     * Parse an ISO 8601 timestamp.
     */
    public static function fromIso8601(string $value): DateTime
    {
        $dateTime = DateTime::createFromFormat('Y-m-d\TH:i:s\Z', $value);
        
        if ($dateTime === false) {
            // Try with timezone offset
            $dateTime = DateTime::createFromFormat('Y-m-d\TH:i:sP', $value);
        }

        if ($dateTime === false) {
            throw new InvalidArgumentException("Invalid ISO 8601 format: {$value}");
        }

        return $dateTime;
    }

    /**
     * Get the default timezone (UTC).
     */
    public static function getDefaultTimezone(): DateTimeZone
    {
        self::initializeDefaultTimezone();
        return self::$defaultTimezone;
    }

    /**
     * Set the default timezone.
     */
    public static function setDefaultTimezone(DateTimeZone $timezone): void
    {
        self::$defaultTimezone = $timezone;
    }

    /**
     * Calculate the difference between two DateTime objects in seconds.
     */
    public static function diffInSeconds(DateTimeInterface $from, DateTimeInterface $to): int
    {
        return $to->getTimestamp() - $from->getTimestamp();
    }

    /**
     * Check if a DateTime is in the past.
     */
    public static function isPast(DateTimeInterface $dateTime): bool
    {
        return $dateTime->getTimestamp() < self::now()->getTimestamp();
    }

    /**
     * Check if a DateTime is in the future.
     */
    public static function isFuture(DateTimeInterface $dateTime): bool
    {
        return $dateTime->getTimestamp() > self::now()->getTimestamp();
    }

    /**
     * Add seconds to a DateTime.
     */
    public static function addSeconds(DateTimeInterface $dateTime, int $seconds): DateTime
    {
        $newDateTime = DateTime::createFromInterface($dateTime);
        $newDateTime->modify("+{$seconds} seconds");
        return $newDateTime;
    }

    /**
     * Subtract seconds from a DateTime.
     */
    public static function subSeconds(DateTimeInterface $dateTime, int $seconds): DateTime
    {
        $newDateTime = DateTime::createFromInterface($dateTime);
        $newDateTime->modify("-{$seconds} seconds");
        return $newDateTime;
    }
}