<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Http\HttpClientInterface;
use Freemius\SDK\Support\Collection;
use Freemius\SDK\Support\PaginatedCollection;
use Freemius\SDK\Support\QueryBuilder;
use Freemius\SDK\Support\DataProcessor;
use Freemius\SDK\Exceptions\ValidationException;
use Closure;

/**
 * Abstract base class for all Freemius API resources.
 * 
 * Provides common functionality including method chaining, query building,
 * and CRUD operations for Freemius API endpoints.
 */
abstract class AbstractResource
{
    protected HttpClientInterface $httpClient;
    protected QueryBuilder $queryBuilder;
    protected ?int $productScope = null;
    
    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
        $this->queryBuilder = new QueryBuilder();
    }
    
    /**
     * Get the base endpoint path for this resource.
     */
    abstract protected function getEndpoint(): string;
    
    /**
     * Get the entity class name for this resource.
     */
    abstract protected function getEntityClass(): string;
    
    /**
     * Set the product scope for API requests.
     *
     * @param int|null $productId Product ID to scope requests to
     * @return self
     * @throws ValidationException If product ID is invalid
     */
    public function setProductScope(?int $productId): self
    {
        if ($productId !== null && $productId <= 0) {
            throw new ValidationException('Product scope must be a positive integer or null');
        }
        
        $this->productScope = $productId;
        return $this;
    }

    /**
     * Get the current product scope.
     *
     * @return int|null
     */
    public function getProductScope(): ?int
    {
        return $this->productScope;
    }

    /**
     * Check if product scope is set.
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->productScope !== null;
    }

    /**
     * Clear the product scope.
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->productScope = null;
        return $this;
    }
    
    /**
     * Add a where condition to the query.
     */
    public function where(string $field, $operator, $value = null): self
    {
        // If only two arguments provided, assume equals operator
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->queryBuilder->where($field, $operator, $value);
        return $this;
    }
    
    /**
     * Set the limit for the query.
     */
    public function limit(int $count): self
    {
        $this->queryBuilder->limit($count);
        return $this;
    }
    
    /**
     * Set the offset for the query.
     */
    public function offset(int $offset): self
    {
        $this->queryBuilder->offset($offset);
        return $this;
    }
    
    /**
     * Order results by a field.
     */
    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->queryBuilder->orderBy($field, $direction);
        return $this;
    }

    /**
     * Add a simple where equals condition.
     */
    public function whereEquals(string $field, $value): self
    {
        $this->queryBuilder->whereEquals($field, $value);
        return $this;
    }

    /**
     * Filter by status.
     */
    public function whereStatus(string $status): self
    {
        $this->queryBuilder->whereStatus($status);
        return $this;
    }

    /**
     * Filter by plan ID.
     */
    public function wherePlan(int $planId): self
    {
        $this->queryBuilder->wherePlan($planId);
        return $this;
    }

    /**
     * Filter by user ID.
     */
    public function whereUser(int $userId): self
    {
        $this->queryBuilder->whereUser($userId);
        return $this;
    }

    /**
     * Set the count (number of records to return).
     */
    public function count(int $count): self
    {
        $this->queryBuilder->count($count);
        return $this;
    }
    
    /**
     * Get a single entity by ID.
     *
     * @param int $id Entity ID
     * @return mixed Entity instance
     */
    public function get(int $id)
    {
        $endpoint = $this->buildScopedEndpoint($id);
        $response = $this->httpClient->get($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($data);
    }
    
    /**
     * Get all entities matching the current query.
     *
     * @return Collection
     */
    public function all(): Collection
    {
        $params = $this->queryBuilder->toArray();
        $endpoint = $this->buildScopedEndpoint(null, $params);
        
        $response = $this->httpClient->get($endpoint);
        $responseBody = $response->getBody()->getContents();
        $data = json_decode($responseBody, true);
        
        // Handle JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \RuntimeException('Failed to decode JSON response: ' . json_last_error_msg() . '. Response: ' . substr($responseBody, 0, 200));
        }
        
        // Ensure we have an array
        if (!is_array($data)) {
            throw new \RuntimeException('Expected array response, got: ' . gettype($data) . '. Response: ' . substr($responseBody, 0, 200));
        }
        
        return $this->createCollection($data);
    }
    
    /**
     * Get the first entity matching the current query.
     */
    public function first()
    {
        $this->count(1);
        $collection = $this->all();

        return $collection->first();
    }

    /**
     * Get paginated results with automatic pagination support.
     */
    public function paginate(int $count = 25, int $offset = 0): PaginatedCollection
    {
        $this->count($count)->offset($offset);

        $params = $this->queryBuilder->toArray();
        $endpoint = $this->buildScopedEndpoint(null, $params);

        $response = $this->httpClient->get($endpoint);
        $data = json_decode($response->getBody()->getContents(), true);

        $items = $this->createEntitiesFromData($data);
        $pagination = $this->extractPaginationMetadata($data, $count, $offset);

        return new PaginatedCollection(
            $items,
            $pagination,
            $this->httpClient,
            $this,
            $this->queryBuilder->clone(),
            $this->buildScopedEndpoint(),
            $this->getEntityClass(),
            false,
            100 // max pages
        );
    }

    /**
     * Process results in chunks for memory efficiency.
     */
    public function chunk(int $size, Closure $callback): void
    {
        $offset = 0;
        $processedCount = 0;

        do {
            $collection = $this->paginate($size, $offset);

            if ($collection->isEmpty()) {
                break;
            }

            $callback($collection, $processedCount);
            $processedCount += $collection->count();
            $offset += $size;

            // Prevent infinite loops
            if ($processedCount > 10000) {
                break;
            }

        } while ($collection->hasMoreItems());
    }

    /**
     * Get all results lazily as a generator.
     */
    public function lazy(int $chunkSize = 100): \Generator
    {
        $offset = 0;

        do {
            $collection = $this->paginate($chunkSize, $offset);

            if ($collection->isEmpty()) {
                break;
            }

            foreach ($collection->all() as $item) {
                yield $item;
            }

            $offset += $chunkSize;

        } while ($collection->hasMoreItems());
    }

    /**
     * Search for entities using the search parameter.
     */
    public function search(string $query): self
    {
        $this->queryBuilder->search($query);
        return $this;
    }

    /**
     * Filter by email address.
     */
    public function filterByEmail(string $email): self
    {
        $this->queryBuilder->email($email);
        return $this;
    }

    /**
     * Create a data processor for memory-efficient processing.
     */
    public function processor(int $chunkSize = 100, int $maxMemoryUsage = 128 * 1024 * 1024): DataProcessor
    {
        return new DataProcessor($this, $chunkSize, $maxMemoryUsage);
    }

    /**
     * Process all data in chunks with automatic memory management.
     */
    public function processInChunks(Closure $processor, int $chunkSize = 100): array
    {
        return $this->processor($chunkSize)->processAll($processor);
    }

    /**
     * Stream all data lazily for memory-efficient processing.
     */
    public function streamAll(int $chunkSize = 100): \Generator
    {
        return $this->processor($chunkSize)->stream();
    }

    /**
     * Export data to CSV file.
     */
    public function exportToCsv(string $filename, array $headers = [], Closure $formatter = null, int $chunkSize = 100): void
    {
        $this->processor($chunkSize)->toCsv($filename, $headers, $formatter);
    }

    /**
     * Export data to JSON Lines file.
     */
    public function exportToJsonLines(string $filename, Closure $formatter = null, int $chunkSize = 100): void
    {
        $this->processor($chunkSize)->toJsonLines($filename, $formatter);
    }
    
    /**
     * Create a new entity.
     *
     * @param array $data Entity data
     * @return mixed Entity instance
     */
    public function create(array $data)
    {
        $this->validateCreateData($data);
        
        // Automatically inject product_id if scope is set and not already present
        if ($this->productScope !== null && !isset($data['product_id'])) {
            $data['product_id'] = $this->productScope;
        }
        
        $endpoint = $this->buildEndpoint();
        $response = $this->httpClient->post($endpoint, $data);
        
        $responseData = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($responseData);
    }
    
    /**
     * Update an existing entity.
     *
     * @param int $id Entity ID
     * @param array $data Updated entity data
     * @return mixed Entity instance
     */
    public function update(int $id, array $data)
    {
        $this->validateUpdateData($data);
        
        // Automatically inject product_id if scope is set and not already present
        if ($this->productScope !== null && !isset($data['product_id'])) {
            $data['product_id'] = $this->productScope;
        }
        
        $endpoint = $this->buildEndpoint($id);
        $response = $this->httpClient->put($endpoint, $data);
        
        $responseData = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($responseData);
    }
    
    /**
     * Delete an entity.
     *
     * @param int $id Entity ID
     * @return bool True if deletion was successful
     */
    public function delete(int $id): bool
    {
        $endpoint = $this->buildScopedEndpoint($id);
        $response = $this->httpClient->delete($endpoint);
        
        return $response->getStatusCode() === 204;
    }
    
    /**
     * Build the full endpoint URL with automatic product scope injection.
     *
     * @param int|null $id Optional entity ID
     * @return string
     * @throws ValidationException If product scope is required but not set
     */
    protected function buildEndpoint(?int $id = null): string
    {
        $endpoint = $this->getEndpoint();
        
        // Add product scope if set
        if ($this->productScope !== null) {
            $endpoint = "products/{$this->productScope}/" . ltrim($endpoint, '/');
        }
        
        // Add ID if provided
        if ($id !== null) {
            $endpoint = rtrim($endpoint, '/') . '/' . $id;
        }
        
        // Add .json extension for Freemius API
        $endpoint = rtrim($endpoint, '/') . '.json';
        
        return $endpoint;
    }

    /**
     * Build endpoint with automatic product ID injection for scoped requests.
     *
     * @param int|null $id Optional entity ID
     * @param array $params Additional parameters to inject
     * @return string
     */
    protected function buildScopedEndpoint(?int $id = null, array $params = []): string
    {
        $endpoint = $this->buildEndpoint($id);
        
        // Automatically inject product_id parameter if scope is set and not already present
        if ($this->productScope !== null && !isset($params['product_id'])) {
            $params['product_id'] = $this->productScope;
        }
        
        // Append query parameters if any
        if (!empty($params)) {
            $queryString = http_build_query($params);
            $endpoint .= (strpos($endpoint, '?') !== false ? '&' : '?') . $queryString;
        }
        
        return $endpoint;
    }

    /**
     * Validate that product scope is set when required.
     *
     * @param bool $required Whether product scope is required
     * @throws ValidationException If product scope is required but not set
     */
    protected function validateProductScope(bool $required = false): void
    {
        if ($required && $this->productScope === null) {
            throw new ValidationException(
                'Product scope is required for this operation. Use setProductScope() to set a product ID.'
            );
        }
    }
    
    /**
     * Create a collection from API response data.
     */
    protected function createCollection(array $data): Collection
    {
        $items = $this->createEntitiesFromData($data);
        $pagination = $this->extractPaginationMetadata($data);

        return new Collection($items, $pagination);
    }

    /**
     * Create entities from API response data.
     */
    protected function createEntitiesFromData(array $data): array
    {
        $entityClass = $this->getEntityClass();
        $items = [];

        // Handle different response formats
        if (isset($data['data'])) {
            // Standard collection response with data wrapper
            $items = $data['data'];
        } elseif ($this->isSingleEntityResponse($data)) {
            // Single entity response (e.g., when accessing /products/{id}.json)
            $items = [$data];
        } elseif (is_array($data) && !empty($data)) {
            // Direct array of items
            $items = $data;
        }

        return array_map(function ($item) use ($entityClass) {
            if (!is_array($item)) {
                throw new \RuntimeException('Expected array for entity data, got: ' . gettype($item) . '. Value: ' . (is_string($item) ? substr($item, 0, 100) : var_export($item, true)));
            }
            return new $entityClass($item);
        }, $items);
    }
    
    /**
     * Check if the response represents a single entity rather than a collection
     */
    private function isSingleEntityResponse(array $data): bool
    {
        // If the response has an 'id' field and other entity-like fields, it's likely a single entity
        return isset($data['id']) && (
            isset($data['created']) || 
            isset($data['updated']) || 
            isset($data['title']) || 
            isset($data['name']) ||
            isset($data['slug'])
        );
    }

    /**
     * Extract pagination metadata from API response.
     */
    protected function extractPaginationMetadata(array $data, ?int $count = null, ?int $offset = null): array
    {
        $pagination = $data['pagination'] ?? [];

        // Add current request parameters
        if ($count !== null) {
            $pagination['count'] = $count;
            $pagination['per_page'] = $count; // For compatibility
        }

        if ($offset !== null) {
            $pagination['offset'] = $offset;
        }

        // Calculate additional metadata if possible
        if (isset($pagination['total']) && isset($pagination['count'])) {
            $total = (int) $pagination['total'];
            $perPage = (int) $pagination['count'];
            $currentOffset = (int) ($pagination['offset'] ?? 0);

            $pagination['total_pages'] = (int) ceil($total / $perPage);
            $pagination['current_page'] = (int) floor($currentOffset / $perPage) + 1;
        }

        return $pagination;
    }
    
    /**
     * Validate data for create operations.
     * Override in child classes for specific validation.
     */
    protected function validateCreateData(array $data): void
    {
        // Base validation - override in child classes
    }
    
    /**
     * Validate data for update operations.
     * Override in child classes for specific validation.
     */
    protected function validateUpdateData(array $data): void
    {
        // Base validation - override in child classes
    }
    
    /**
     * Reset the query builder for a fresh query.
     */
    public function fresh(): self
    {
        $this->queryBuilder = new QueryBuilder();
        return $this;
    }
    
    /**
     * Create a child resource instance with inherited context.
     */
    protected function createChildResource(string $resourceClass): AbstractResource
    {
        $resource = new $resourceClass($this->httpClient);
        
        // Inherit product scope if set
        if ($this->productScope !== null) {
            $resource->setProductScope($this->productScope);
        }
        
        return $resource;
    }
}