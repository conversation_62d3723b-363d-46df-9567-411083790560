<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Integration;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Webhooks\WebhookHandler;
use Freemius\SDK\Webhooks\WebhookValidator;
use Freemius\SDK\Webhooks\Events\InstallActivated;
use Freemius\SDK\Webhooks\Events\SubscriptionUpdated;
use Freemius\SDK\Webhooks\Events\PaymentCompleted;
use Freemius\SDK\Webhooks\Events\UnknownEvent;
use Freemius\SDK\Exceptions\WebhookException;

/**
 * Integration tests for Webhook processing
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Webhooks\WebhookHandler::class)]
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Webhooks\WebhookValidator::class)]
#[\PHPUnit\Framework\Attributes\Group('integration')]
class WebhookIntegrationTest extends TestCase
{
    private string $webhookSecret = 'test_webhook_secret_integration_12345';
    private WebhookHandler $webhookHandler;

    protected function setUp(): void
    {
        $this->webhookHandler = new WebhookHandler($this->webhookSecret);
    }

    public function testInstallActivatedWebhookProcessing(): void
    {
        $payload = json_encode([
            'id' => 'evt_install_activated_123',
            'type' => 'install.activated',
            'created' => time(),
            'install' => [
                'id' => 77777,
                'user_id' => 98765,
                'product_id' => 12345,
                'license_id' => 55555,
                'url' => 'https://example-site.com',
                'title' => 'Example Site',
                'language' => 'en',
                'charset' => 'UTF-8',
                'platform_version' => '6.2.2',
                'sdk_version' => '2.5.10',
                'version' => '1.2.3',
                'is_active' => true,
                'is_uninstalled' => false,
                'is_locked' => false,
                'created' => '2023-06-15 15:00:00',
                'updated' => '2023-06-15 15:00:00',
            ],
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $headers = ['X-Signature' => $signature];
        $event = $this->webhookHandler->handle($payload, $headers);
        
        $this->assertInstanceOf(InstallActivated::class, $event);
        $this->assertEquals('install.activated', $event->getEventType());
        $this->assertEquals('evt_install_activated_123', $event->getWebhookId());
        
        $installation = $event->getInstallation();
        $this->assertInstanceOf(\Freemius\SDK\Entities\Installation::class, $installation);
        $this->assertEquals(77777, $installation->getId());
        $this->assertEquals('https://example-site.com', $installation->getUrl());
    }

    public function testSubscriptionUpdatedWebhookProcessing(): void
    {
        $payload = json_encode([
            'id' => 'evt_subscription_created_456',
            'type' => 'subscription.created',
            'created' => time(),
            'subscription' => [
                'id' => 88888,
                'user_id' => 98765,
                'plugin_id' => 12345,
                'plan_id' => 22222,
                'license_id' => 55555,
                'billing_cycle' => 12,
                'amount_per_cycle' => 99.99,
                'currency' => 'USD',
                'trial_ends' => null,
                'next_payment' => '2024-06-15 14:30:00',
                'created' => '2023-06-15 14:30:00',
                'updated' => '2023-06-15 14:30:00',
            ],
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $headers = ['X-Signature' => $signature];
        $event = $this->webhookHandler->handle($payload, $headers);
        
        $this->assertInstanceOf(SubscriptionUpdated::class, $event);
        $this->assertEquals('subscription.created', $event->getEventType());
        
        $subscription = $event->getSubscription();
        $this->assertInstanceOf(\Freemius\SDK\Entities\Subscription::class, $subscription);
        $this->assertEquals(88888, $subscription->getId());
        $this->assertTrue($subscription->isActive());
        $this->assertEquals(99.99, $subscription->getAmountPerCycle());
    }

    public function testPaymentCompletedWebhookProcessing(): void
    {
        $payload = json_encode([
            'id' => 'evt_payment_created_789',
            'type' => 'payment.created',
            'created' => time(),
            'payment' => [
                'id' => 99999,
                'user_id' => 98765,
                'product_id' => 12345,
                'subscription_id' => 88888,
                'license_id' => 55555,
                'gross' => 99.99,
                'currency' => 'USD',
                'status' => 'completed',
                'gateway' => 'stripe',
                'external_id' => 'pi_1234567890abcdef',
                'created' => '2023-06-15 14:30:00',
                'updated' => '2023-06-15 14:30:00',
            ],
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $headers = ['X-Signature' => $signature];
        $event = $this->webhookHandler->handle($payload, $headers);
        
        $this->assertInstanceOf(PaymentCompleted::class, $event);
        $this->assertEquals('payment.created', $event->getEventType());
        
        $payment = $event->getPayment();
        $this->assertInstanceOf(\Freemius\SDK\Entities\Payment::class, $payment);
        $this->assertEquals(99999, $payment->getId());
        $this->assertEquals('completed', $payment->getStatus());
        $this->assertEquals('stripe', $payment->getGateway());
    }

    public function testUnknownEventTypeHandling(): void
    {
        $payload = json_encode([
            'id' => 'evt_unknown_event_999',
            'type' => 'unknown.event.type',
            'created' => time(),
            'data' => [
                'object' => [
                    'id' => 12345,
                    'some_field' => 'some_value',
                ],
            ],
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $headers = ['X-Signature' => $signature];
        $event = $this->webhookHandler->handle($payload, $headers);
        
        $this->assertInstanceOf(UnknownEvent::class, $event);
        $this->assertEquals('unknown.event.type', $event->getEventType());
        $this->assertEquals('evt_unknown_event_999', $event->getWebhookId());
    }

    public function testWebhookSignatureValidation(): void
    {
        $payload = json_encode([
            'id' => 'evt_test_123',
            'type' => 'test.event',
            'created' => time(),
            'data' => ['test' => 'data'],
        ]);

        $validSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        $invalidSignature = 'invalid_signature_12345';

        // Valid signature should work
        $validHeaders = ['X-Signature' => $validSignature];
        $event = $this->webhookHandler->handle($payload, $validHeaders);
        $this->assertInstanceOf(UnknownEvent::class, $event);

        // Invalid signature should throw exception
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $invalidHeaders = ['X-Signature' => $invalidSignature];
        $this->webhookHandler->handle($payload, $invalidHeaders);
    }

    public function testWebhookTimestampValidation(): void
    {
        $currentTime = time();
        $validTimestamp = $currentTime - 60; // 1 minute ago
        $expiredTimestamp = $currentTime - 400; // 6+ minutes ago

        $validPayload = json_encode([
            'id' => 'evt_valid_timestamp',
            'type' => 'test.event',
            'created' => $validTimestamp,
            'data' => ['test' => 'data'],
        ]);

        $expiredPayload = json_encode([
            'id' => 'evt_expired_timestamp',
            'type' => 'test.event',
            'created' => $expiredTimestamp,
            'data' => ['test' => 'data'],
        ]);

        $validSignature = hash_hmac('sha256', $validPayload, $this->webhookSecret);
        $expiredSignature = hash_hmac('sha256', $expiredPayload, $this->webhookSecret);

        // Valid timestamp should work
        $validHeaders = ['X-Signature' => $validSignature, 'X-Timestamp' => (string)$validTimestamp];
        $event = $this->webhookHandler->handle($validPayload, $validHeaders);
        $this->assertInstanceOf(UnknownEvent::class, $event);

        // Expired timestamp should throw exception
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $expiredHeaders = ['X-Signature' => $expiredSignature, 'X-Timestamp' => (string)$expiredTimestamp];
        $this->webhookHandler->handle($expiredPayload, $expiredHeaders);
    }

    public function testWebhookReplayAttackPrevention(): void
    {
        $payload = json_encode([
            'id' => 'evt_replay_test',
            'type' => 'test.event',
            'created' => time() - 1000, // Old timestamp
            'data' => ['test' => 'data'],
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        $oldTimestamp = time() - 1000;

        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $headers = ['X-Signature' => $signature, 'X-Timestamp' => (string)$oldTimestamp];
        $this->webhookHandler->handle($payload, $headers);
    }

    public function testWebhookEventDataAccess(): void
    {
        $installData = [
            'id' => 77777,
            'user_id' => 98765,
            'product_id' => 12345,
            'url' => 'https://test-site.com',
            'title' => 'Test Site',
            'is_active' => true,
        ];

        $payload = json_encode([
            'id' => 'evt_data_access_test',
            'type' => 'install.activated',
            'created' => time(),
            'install' => $installData,
        ]);

        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $headers = ['X-Signature' => $signature];
        $event = $this->webhookHandler->handle($payload, $headers);
        
        $this->assertInstanceOf(InstallActivated::class, $event);
        
        // Test raw data access
        $rawData = $event->getData();
        $this->assertIsArray($rawData);
        $this->assertArrayHasKey('install', $rawData);
        $this->assertEquals($installData, $rawData['install']);
        
        // Test entity access
        $installation = $event->getInstallation();
        $this->assertEquals(77777, $installation->getId());
        $this->assertEquals('https://test-site.com', $installation->getUrl());
        $this->assertEquals('Test Site', $installation->getTitle());
        $this->assertTrue($installation->isActive());
    }

    public function testWebhookHandlerWithDifferentSecrets(): void
    {
        $differentSecret = 'different_webhook_secret_67890';
        $differentHandler = new WebhookHandler($differentSecret);

        $payload = json_encode([
            'id' => 'evt_different_secret',
            'type' => 'test.event',
            'created' => time(),
            'data' => ['test' => 'data'],
        ]);

        $originalSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        $differentSignature = hash_hmac('sha256', $payload, $differentSecret);

        // Original handler with original signature should work
        $originalHeaders = ['X-Signature' => $originalSignature];
        $event1 = $this->webhookHandler->handle($payload, $originalHeaders);
        $this->assertInstanceOf(UnknownEvent::class, $event1);

        // Different handler with different signature should work
        $differentHeaders = ['X-Signature' => $differentSignature];
        $event2 = $differentHandler->handle($payload, $differentHeaders);
        $this->assertInstanceOf(UnknownEvent::class, $event2);

        // Cross-validation should fail
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->webhookHandler->handle($payload, $differentHeaders);
    }

    public function testCompleteWebhookWorkflow(): void
    {
        // Simulate a complete webhook workflow from Freemius
        
        // 1. Install activation
        $installPayload = json_encode([
            'id' => 'evt_workflow_install',
            'type' => 'install.activated',
            'created' => time(),
            'install' => [
                'id' => 11111,
                'user_id' => 22222,
                'product_id' => 33333,
                'url' => 'https://workflow-test.com',
                'title' => 'Workflow Test Site',
                'is_active' => true,
                'created' => '2023-06-15 15:00:00',
            ],
        ]);

        $installSignature = hash_hmac('sha256', $installPayload, $this->webhookSecret);
        $installHeaders = ['X-Signature' => $installSignature];
        $installEvent = $this->webhookHandler->handle($installPayload, $installHeaders);
        
        $this->assertInstanceOf(InstallActivated::class, $installEvent);
        $installation = $installEvent->getInstallation();
        $this->assertEquals(11111, $installation->getId());

        // 2. Subscription creation
        $subscriptionPayload = json_encode([
            'id' => 'evt_workflow_subscription',
            'type' => 'subscription.created',
            'created' => time(),
            'subscription' => [
                'id' => 44444,
                'user_id' => 22222,
                'plugin_id' => 33333,
                'plan_id' => 11111,
                'license_id' => 22222,
                'amount_per_cycle' => 49.99,
                'currency' => 'USD',
                'billing_cycle' => 1,
                'next_payment' => '2024-06-15 15:05:00',
                'created' => '2023-06-15 15:05:00',
            ],
        ]);

        $subscriptionSignature = hash_hmac('sha256', $subscriptionPayload, $this->webhookSecret);
        $subscriptionHeaders = ['X-Signature' => $subscriptionSignature];
        $subscriptionEvent = $this->webhookHandler->handle($subscriptionPayload, $subscriptionHeaders);
        
        $this->assertInstanceOf(SubscriptionUpdated::class, $subscriptionEvent);
        $subscription = $subscriptionEvent->getSubscription();
        $this->assertEquals(44444, $subscription->getId());
        $this->assertTrue($subscription->isActive());

        // 3. Payment creation
        $paymentPayload = json_encode([
            'id' => 'evt_workflow_payment',
            'type' => 'payment.created',
            'created' => time(),
            'payment' => [
                'id' => 55555,
                'user_id' => 22222,
                'subscription_id' => 44444,
                'gross' => 49.99,
                'currency' => 'USD',
                'status' => 'completed',
                'gateway' => 'stripe',
                'created' => '2023-06-15 15:10:00',
            ],
        ]);

        $paymentSignature = hash_hmac('sha256', $paymentPayload, $this->webhookSecret);
        $paymentHeaders = ['X-Signature' => $paymentSignature];
        $paymentEvent = $this->webhookHandler->handle($paymentPayload, $paymentHeaders);
        
        $this->assertInstanceOf(PaymentCompleted::class, $paymentEvent);
        $payment = $paymentEvent->getPayment();
        $this->assertEquals(55555, $payment->getId());
        $this->assertEquals('completed', $payment->getStatus());

        // Verify all events are related to the same user
        $this->assertEquals(22222, $installation->getUserId());
        $this->assertEquals(22222, $subscription->getUserId());
        $this->assertEquals(22222, $payment->getUserId());
    }
}