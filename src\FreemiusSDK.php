<?php

declare(strict_types=1);

namespace Freemius\SDK;

use InvalidArgumentException;
use Freemius\SDK\Http\HttpClient;
use Freemius\SDK\Resources\Products;
use Freemius\SDK\Resources\Users;
use Freemius\SDK\Resources\Licenses;
use Freemius\SDK\Resources\Installations;
use Freemius\SDK\Resources\Subscriptions;
use Freemius\SDK\Resources\Carts;
use Freemius\SDK\Resources\Coupons;
use Freemius\SDK\Resources\Payments;
use Freemius\SDK\Resources\Plans;
use Freemius\SDK\Resources\Tags;
use Freemius\SDK\Resources\Trials;

/**
 * Main Freemius SDK facade
 * 
 * Provides the primary entry point for all SDK functionality,
 * including configuration management and resource access.
 */
class FreemiusSDK
{
    private Configuration $config;
    private Logger $logger;
    private ?HttpClient $httpClient = null;
    private ?Products $products = null;
    private ?Users $users = null;
    private ?Licenses $licenses = null;
    private ?Installations $installations = null;
    private ?Subscriptions $subscriptions = null;
    private ?Carts $carts = null;
    private ?Coupons $coupons = null;
    private ?Payments $payments = null;
    private ?Plans $plans = null;
    private ?Tags $tags = null;
    private ?Trials $trials = null;

    /**
     * Create a new Freemius SDK instance
     *
     * @param Configuration $config SDK configuration
     * @param Logger|null $logger Optional custom logger
     */
    public function __construct(Configuration $config, ?Logger $logger = null)
    {
        $this->config = $config;
        $this->logger = $logger ?? new Logger($config);
    }



    /**
     * Create SDK instance with bearer token
     *
     * @param string $bearerToken Freemius API bearer token
     * @param array $options Additional configuration options
     * @return self
     */
    public static function create(string $bearerToken, array $options = []): self
    {
        $options['bearerToken'] = $bearerToken;
        $config = new Configuration($options);
        return new self($config);
    }

    /**
     * Create SDK instance from environment variables
     *
     * @param array $options Additional configuration options to override environment values
     * @return self
     * @throws InvalidArgumentException If required environment variables are missing
     */
    public static function fromEnvironment(array $options = []): self
    {
        $config = Configuration::fromEnvironment($options);
        return new self($config);
    }

    /**
     * Set product scope for API requests
     *
     * @param int $productId Product ID to scope requests to
     * @return self
     * @throws InvalidArgumentException If product ID is invalid
     */
    public function setProductScope(int $productId): self
    {
        $this->config->setProductScope($productId);
        
        // Reset resource instances to apply new scope
        $this->resetResourceInstances();
        
        return $this;
    }

    /**
     * Clear product scope for API requests
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->config->clearProductScope();
        
        // Reset resource instances to clear scope
        $this->resetResourceInstances();
        
        return $this;
    }

    /**
     * Get current product scope
     *
     * @return int|null
     */
    public function getProductScope(): ?int
    {
        return $this->config->getProductScope();
    }

    /**
     * Check if product scope is set
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->config->hasProductScope();
    }

    /**
     * Get the current configuration
     *
     * @return Configuration
     */
    public function getConfiguration(): Configuration
    {
        return $this->config;
    }

    /**
     * Get the logger instance
     *
     * @return Logger
     */
    public function getLogger(): Logger
    {
        return $this->logger;
    }

    /**
     * Enable sandbox mode
     *
     * @param bool $sandbox Whether to use sandbox mode
     * @return self
     */
    public function useSandbox(bool $sandbox = true): self
    {
        $this->config->useSandbox($sandbox);
        return $this;
    }

    /**
     * Enable logging with optional level
     *
     * @param string $level Log level
     * @return self
     */
    public function enableLogging(string $level = 'info'): self
    {
        $this->config->enableLogging($level);
        return $this;
    }

    /**
     * Disable logging
     *
     * @return self
     */
    public function disableLogging(): self
    {
        $this->config->disableLogging();
        return $this;
    }

    /**
     * Set request timeout
     *
     * @param int $seconds Timeout in seconds
     * @return self
     */
    public function setTimeout(int $seconds): self
    {
        $this->config->setTimeout($seconds);
        return $this;
    }

    /**
     * Set retry attempts
     *
     * @param int $attempts Number of retry attempts
     * @return self
     */
    public function setRetryAttempts(int $attempts): self
    {
        $this->config->setRetryAttempts($attempts);
        return $this;
    }

    /**
     * Set log file path
     *
     * @param string|null $logFile Path to log file, or null to disable file logging
     * @return self
     */
    public function setLogFile(?string $logFile): self
    {
        $this->config->setLogFile($logFile);
        return $this;
    }

    /**
     * Set rate limiting configuration
     *
     * @param int $maxRequests Maximum requests per time window
     * @param int $windowSeconds Time window in seconds
     * @param string $backoffStrategy Backoff strategy ('linear', 'exponential', 'fixed')
     * @return self
     */
    public function setRateLimit(int $maxRequests, int $windowSeconds, string $backoffStrategy = 'exponential'): self
    {
        $this->config->setRateLimit($maxRequests, $windowSeconds, $backoffStrategy);
        return $this;
    }

    /**
     * Set cache configuration
     *
     * @param bool $enabled Whether caching is enabled
     * @param int|null $ttl Cache TTL in seconds
     * @param int|null $maxSize Maximum cache size
     * @return self
     */
    public function setCacheConfig(bool $enabled, ?int $ttl = null, ?int $maxSize = null): self
    {
        $this->config->setCacheConfig($enabled, $ttl, $maxSize);
        
        // Update HTTP client cache settings if it exists
        if ($this->httpClient !== null) {
            $this->httpClient->setCacheEnabled($enabled);
            if ($ttl !== null) {
                $this->httpClient->setCacheTtl($ttl);
            }
        }
        
        return $this;
    }

    /**
     * Enable caching
     *
     * @return self
     */
    public function enableCache(): self
    {
        $this->config->enableCache();
        
        if ($this->httpClient !== null) {
            $this->httpClient->setCacheEnabled(true);
        }
        
        return $this;
    }

    /**
     * Disable caching
     *
     * @return self
     */
    public function disableCache(): self
    {
        $this->config->disableCache();
        
        if ($this->httpClient !== null) {
            $this->httpClient->setCacheEnabled(false);
        }
        
        return $this;
    }

    /**
     * Clear request cache
     *
     * @return self
     */
    public function clearCache(): self
    {
        if ($this->httpClient !== null) {
            $this->httpClient->clearCache();
        }
        
        return $this;
    }

    /**
     * Invalidate cache entries matching a pattern
     *
     * @param string $pattern URI pattern to match
     * @return int Number of invalidated entries
     */
    public function invalidateCache(string $pattern): int
    {
        if ($this->httpClient !== null) {
            return $this->httpClient->invalidateCache($pattern);
        }
        
        return 0;
    }

    /**
     * Get performance statistics
     *
     * @return array Performance statistics
     */
    public function getPerformanceStats(): array
    {
        if ($this->httpClient !== null) {
            return $this->httpClient->getPerformanceStats();
        }
        
        return [
            'rate_limiter' => ['status' => 'not_initialized'],
            'cache' => ['status' => 'not_initialized'],
            'connection_pool' => ['status' => 'not_initialized'],
        ];
    }

    /**
     * Get HTTP client instance
     *
     * @return HttpClient
     */
    private function getHttpClient(): HttpClient
    {
        if ($this->httpClient === null) {
            // Create Bearer authentication from configuration
            $auth = new \Freemius\SDK\Authentication\BearerAuth($this->config->getBearerToken());
            $this->httpClient = new HttpClient($this->config, $this->logger, $auth);
        }
        
        return $this->httpClient;
    }

    /**
     * Reset all resource instances to apply new configuration
     *
     * @return void
     */
    private function resetResourceInstances(): void
    {
        $this->products = null;
        $this->users = null;
        $this->licenses = null;
        $this->installations = null;
        $this->subscriptions = null;
        $this->carts = null;
        $this->coupons = null;
        $this->payments = null;
        $this->plans = null;
        $this->tags = null;
        $this->trials = null;
    }

    /**
     * Get Products resource
     *
     * @return Products
     */
    public function products(): Products
    {
        if ($this->products === null) {
            $this->products = new Products($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->products->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->products;
    }

    /**
     * Get Users resource
     *
     * @return Users
     */
    public function users(): Users
    {
        if ($this->users === null) {
            $this->users = new Users($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->users->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->users;
    }

    /**
     * Get Licenses resource
     *
     * @return Licenses
     */
    public function licenses(): Licenses
    {
        if ($this->licenses === null) {
            $this->licenses = new Licenses($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->licenses->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->licenses;
    }

    /**
     * Get Installations resource
     *
     * @return Installations
     */
    public function installations(): Installations
    {
        if ($this->installations === null) {
            $this->installations = new Installations($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->installations->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->installations;
    }

    /**
     * Get Subscriptions resource
     *
     * @return Subscriptions
     */
    public function subscriptions(): Subscriptions
    {
        if ($this->subscriptions === null) {
            $this->subscriptions = new Subscriptions($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->subscriptions->setProductScope($this->config->getProductScope());
            }
        }

        return $this->subscriptions;
    }

    /**
     * Get Carts resource
     *
     * @return Carts
     */
    public function carts(): Carts
    {
        if ($this->carts === null) {
            $this->carts = new Carts($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->carts->setProductScope($this->config->getProductScope());
            }
        }

        return $this->carts;
    }

    /**
     * Get Coupons resource
     *
     * @return Coupons
     */
    public function coupons(): Coupons
    {
        if ($this->coupons === null) {
            $this->coupons = new Coupons($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->coupons->setProductScope($this->config->getProductScope());
            }
        }

        return $this->coupons;
    }

    /**
     * Get Payments resource
     *
     * @return Payments
     */
    public function payments(): Payments
    {
        if ($this->payments === null) {
            $this->payments = new Payments($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->payments->setProductScope($this->config->getProductScope());
            }
        }

        return $this->payments;
    }

    /**
     * Get Plans resource
     *
     * @return Plans
     */
    public function plans(): Plans
    {
        if ($this->plans === null) {
            $this->plans = new Plans($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->plans->setProductScope($this->config->getProductScope());
            }
        }

        return $this->plans;
    }

    /**
     * Get Tags resource
     *
     * @return Tags
     */
    public function tags(): Tags
    {
        if ($this->tags === null) {
            $this->tags = new Tags($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->tags->setProductScope($this->config->getProductScope());
            }
        }

        return $this->tags;
    }

    /**
     * Get Trials resource
     *
     * @return Trials
     */
    public function trials(): Trials
    {
        if ($this->trials === null) {
            $this->trials = new Trials($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->trials->setProductScope($this->config->getProductScope());
            }
        }

        return $this->trials;
    }
}