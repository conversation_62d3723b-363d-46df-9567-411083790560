<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Not found exception for Freemius API resource not found errors
 * 
 * Thrown when a requested resource (product, user, license, etc.) 
 * cannot be found or does not exist.
 */
class NotFoundException extends FreemiusException
{
    /**
     * Create a new not found exception
     *
     * @param string $message Exception message
     * @param int $code Exception code (typically HTTP status code)
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Resource not found',
        int $code = 404,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for a resource not found by ID
     *
     * @param string $resourceType The type of resource (e.g., 'product', 'user', 'license')
     * @param int|string $resourceId The ID of the resource that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function resourceNotFound(
        string $resourceType,
        $resourceId,
        array $context = []
    ): self {
        $context = array_merge([
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
        ], $context);

        return new self(
            ucfirst($resourceType) . " with ID {$resourceId} not found",
            404,
            null,
            $context
        );
    }

    /**
     * Create an exception for a product not found
     *
     * @param int $productId The product ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function productNotFound(int $productId, array $context = []): self
    {
        return self::resourceNotFound('product', $productId, $context);
    }

    /**
     * Create an exception for a user not found
     *
     * @param int $userId The user ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function userNotFound(int $userId, array $context = []): self
    {
        return self::resourceNotFound('user', $userId, $context);
    }

    /**
     * Create an exception for a license not found
     *
     * @param int $licenseId The license ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function licenseNotFound(int $licenseId, array $context = []): self
    {
        return self::resourceNotFound('license', $licenseId, $context);
    }

    /**
     * Create an exception for an installation not found
     *
     * @param int $installationId The installation ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function installationNotFound(int $installationId, array $context = []): self
    {
        return self::resourceNotFound('installation', $installationId, $context);
    }

    /**
     * Create an exception for a subscription not found
     *
     * @param int $subscriptionId The subscription ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function subscriptionNotFound(int $subscriptionId, array $context = []): self
    {
        return self::resourceNotFound('subscription', $subscriptionId, $context);
    }

    /**
     * Create an exception for a payment not found
     *
     * @param int $paymentId The payment ID that was not found
     * @param array $context Additional context
     * @return self
     */
    public static function paymentNotFound(int $paymentId, array $context = []): self
    {
        return self::resourceNotFound('payment', $paymentId, $context);
    }

    /**
     * Create an exception for an API endpoint not found
     *
     * @param string $endpoint The API endpoint that was not found
     * @param string $method The HTTP method used
     * @param array $context Additional context
     * @return self
     */
    public static function endpointNotFound(
        string $endpoint = '',
        string $method = '',
        array $context = []
    ): self {
        $message = 'API endpoint not found';
        
        if ($method && $endpoint) {
            $message .= ": {$method} {$endpoint}";
        } elseif ($endpoint) {
            $message .= ": {$endpoint}";
        }

        $context = array_merge([
            'endpoint' => $endpoint,
            'method' => $method,
        ], $context);

        return new self($message, 404, null, $context);
    }
}