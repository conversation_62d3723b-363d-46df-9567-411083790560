<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use Guz<PERSON><PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Handler\CurlMultiHandler;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\Middleware;

/**
 * Connection pool manager for HTTP optimization
 * 
 * Manages HTTP connections with connection pooling, keep-alive,
 * and other performance optimizations for the Guzzle HTTP client.
 */
class ConnectionPool
{
    private array $clients = [];
    private array $clientUsage = [];
    private int $maxConnections;
    private int $maxConnectionsPerHost;
    private int $connectionTimeout;
    private int $keepAliveTimeout;
    private bool $enableCompression;

    /**
     * Create a new connection pool
     *
     * @param int $maxConnections Maximum total connections
     * @param int $maxConnectionsPerHost Maximum connections per host
     * @param int $connectionTimeout Connection timeout in seconds
     * @param int $keepAliveTimeout Keep-alive timeout in seconds
     * @param bool $enableCompression Whether to enable compression
     */
    public function __construct(
        int $maxConnections = 100,
        int $maxConnectionsPerHost = 10,
        int $connectionTimeout = 10,
        int $keepAliveTimeout = 30,
        bool $enableCompression = true
    ) {
        $this->maxConnections = $maxConnections;
        $this->maxConnectionsPerHost = $maxConnectionsPerHost;
        $this->connectionTimeout = $connectionTimeout;
        $this->keepAliveTimeout = $keepAliveTimeout;
        $this->enableCompression = $enableCompression;
    }

    /**
     * Get or create an optimized HTTP client for a base URI
     *
     * @param string $baseUri Base URI for the client
     * @param array $options Additional client options
     * @return GuzzleClient Optimized HTTP client
     */
    public function getClient(string $baseUri, array $options = []): GuzzleClient
    {
        $clientKey = $this->generateClientKey($baseUri, $options);
        
        if (isset($this->clients[$clientKey])) {
            $this->clientUsage[$clientKey] = time();
            return $this->clients[$clientKey];
        }

        // Clean up old clients if we're at the limit
        if (count($this->clients) >= $this->maxConnections) {
            $this->evictOldestClient();
        }

        $client = $this->createOptimizedClient($baseUri, $options);
        $this->clients[$clientKey] = $client;
        $this->clientUsage[$clientKey] = time();

        return $client;
    }

    /**
     * Create an optimized Guzzle client
     *
     * @param string $baseUri Base URI for the client
     * @param array $options Additional client options
     * @return GuzzleClient Optimized client
     */
    private function createOptimizedClient(string $baseUri, array $options): GuzzleClient
    {
        $handler = $this->createOptimizedHandler();
        $stack = HandlerStack::create($handler);

        // Add compression middleware if enabled
        if ($this->enableCompression) {
            $stack->push(Middleware::httpErrors(), 'http_errors');
        }

        $defaultOptions = [
            'base_uri' => $baseUri,
            'handler' => $stack,
            'connect_timeout' => $this->connectionTimeout,
            'timeout' => 30, // Default request timeout
            'headers' => [
                'Connection' => 'keep-alive',
                'Keep-Alive' => 'timeout=' . $this->keepAliveTimeout,
            ],
            'curl' => [
                CURLOPT_TCP_KEEPALIVE => 1,
                CURLOPT_TCP_KEEPIDLE => $this->keepAliveTimeout,
                CURLOPT_TCP_KEEPINTVL => 10,
                CURLOPT_MAXCONNECTS => $this->maxConnectionsPerHost,
                CURLOPT_FORBID_REUSE => false,
                CURLOPT_FRESH_CONNECT => false,
            ],
            'verify' => true,
            'http_errors' => true,
        ];

        // Enable compression if supported
        if ($this->enableCompression) {
            $defaultOptions['headers']['Accept-Encoding'] = 'gzip, deflate';
            $defaultOptions['decode_content'] = true;
        }

        // Merge with provided options (use regular merge to avoid recursive issues with scalar values)
        $clientOptions = array_merge($defaultOptions, $options);
        
        // Handle headers separately to merge them properly
        if (isset($defaultOptions['headers']) && isset($options['headers'])) {
            $clientOptions['headers'] = array_merge($defaultOptions['headers'], $options['headers']);
        }
        
        // Handle curl options separately to merge them properly
        if (isset($defaultOptions['curl']) && isset($options['curl'])) {
            $clientOptions['curl'] = array_merge($defaultOptions['curl'], $options['curl']);
        }

        return new GuzzleClient($clientOptions);
    }

    /**
     * Create an optimized handler stack
     *
     * @return CurlMultiHandler|CurlHandler Handler instance
     */
    private function createOptimizedHandler()
    {
        // Use multi-handler for better connection pooling
        if (extension_loaded('curl')) {
            $handler = new CurlMultiHandler([
                'max_handles' => $this->maxConnections,
                'select_timeout' => 1.0,
            ]);
        } else {
            $handler = new CurlHandler();
        }

        return $handler;
    }

    /**
     * Generate a unique key for client caching
     *
     * @param string $baseUri Base URI
     * @param array $options Client options
     * @return string Client key
     */
    private function generateClientKey(string $baseUri, array $options): string
    {
        // Include relevant options that affect connection behavior
        $keyOptions = array_intersect_key($options, [
            'timeout' => true,
            'connect_timeout' => true,
            'verify' => true,
            'cert' => true,
            'ssl_key' => true,
            'proxy' => true,
        ]);

        return md5($baseUri . serialize($keyOptions));
    }

    /**
     * Evict the oldest unused client
     *
     * @return void
     */
    private function evictOldestClient(): void
    {
        if (empty($this->clients)) {
            return;
        }

        $oldestKey = null;
        $oldestTime = PHP_INT_MAX;

        foreach ($this->clientUsage as $key => $lastUsed) {
            if ($lastUsed < $oldestTime) {
                $oldestTime = $lastUsed;
                $oldestKey = $key;
            }
        }

        if ($oldestKey !== null) {
            unset($this->clients[$oldestKey], $this->clientUsage[$oldestKey]);
        }
    }

    /**
     * Clean up unused clients older than specified age
     *
     * @param int $maxAge Maximum age in seconds
     * @return int Number of clients cleaned up
     */
    public function cleanup(int $maxAge = 300): int
    {
        $cutoffTime = time() - $maxAge;
        $cleaned = 0;

        foreach ($this->clientUsage as $key => $lastUsed) {
            if ($lastUsed < $cutoffTime) {
                unset($this->clients[$key], $this->clientUsage[$key]);
                $cleaned++;
            }
        }

        return $cleaned;
    }

    /**
     * Get connection pool statistics
     *
     * @return array Pool statistics
     */
    public function getStats(): array
    {
        $now = time();
        $activeClients = 0;
        $oldestAge = 0;
        $newestAge = PHP_INT_MAX;

        foreach ($this->clientUsage as $lastUsed) {
            $age = $now - $lastUsed;
            
            if ($age < 60) { // Active if used within last minute
                $activeClients++;
            }
            
            $oldestAge = max($oldestAge, $age);
            $newestAge = min($newestAge, $age);
        }

        return [
            'total_clients' => count($this->clients),
            'active_clients' => $activeClients,
            'max_connections' => $this->maxConnections,
            'max_connections_per_host' => $this->maxConnectionsPerHost,
            'connection_timeout' => $this->connectionTimeout,
            'keep_alive_timeout' => $this->keepAliveTimeout,
            'compression_enabled' => $this->enableCompression,
            'oldest_client_age' => $oldestAge,
            'newest_client_age' => $newestAge === PHP_INT_MAX ? 0 : $newestAge,
        ];
    }

    /**
     * Clear all cached clients
     *
     * @return void
     */
    public function clear(): void
    {
        $this->clients = [];
        $this->clientUsage = [];
    }

    /**
     * Set maximum connections
     *
     * @param int $maxConnections Maximum total connections
     * @return void
     */
    public function setMaxConnections(int $maxConnections): void
    {
        $this->maxConnections = max(1, $maxConnections);
        
        // Clean up excess clients if needed
        while (count($this->clients) > $this->maxConnections) {
            $this->evictOldestClient();
        }
    }

    /**
     * Set maximum connections per host
     *
     * @param int $maxConnectionsPerHost Maximum connections per host
     * @return void
     */
    public function setMaxConnectionsPerHost(int $maxConnectionsPerHost): void
    {
        $this->maxConnectionsPerHost = max(1, $maxConnectionsPerHost);
    }

    /**
     * Enable or disable compression
     *
     * @param bool $enabled Whether to enable compression
     * @return void
     */
    public function setCompressionEnabled(bool $enabled): void
    {
        if ($this->enableCompression !== $enabled) {
            $this->enableCompression = $enabled;
            // Clear clients to force recreation with new compression settings
            $this->clear();
        }
    }
}