<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Rate limit exception for Freemius API rate limiting
 * 
 * Thrown when the API rate limit has been exceeded and the client
 * needs to wait before making additional requests.
 */
class RateLimitException extends FreemiusException
{
    private ?int $retryAfter = null;
    private ?int $rateLimitLimit = null;
    private ?int $rateLimitRemaining = null;
    private ?int $rateLimitReset = null;

    /**
     * Create a new rate limit exception
     *
     * @param string $message Exception message
     * @param int $code Exception code (typically HTTP status code)
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Rate limit exceeded',
        int $code = 429,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create a rate limit exception with retry information
     *
     * @param int|null $retryAfter Seconds to wait before retrying
     * @param array $rateLimitHeaders Rate limit headers from the response
     * @param array $context Additional context
     * @return self
     */
    public static function withRetryInfo(
        ?int $retryAfter = null,
        array $rateLimitHeaders = [],
        array $context = []
    ): self {
        $message = 'Rate limit exceeded';
        
        if ($retryAfter !== null) {
            $message .= ". Retry after {$retryAfter} seconds";
        }

        $exception = new self($message, 429, null, $context);
        $exception->setRetryAfter($retryAfter);
        
        // Set rate limit information from headers
        if (isset($rateLimitHeaders['X-RateLimit-Limit'])) {
            $exception->setRateLimitLimit((int) $rateLimitHeaders['X-RateLimit-Limit']);
        }
        
        if (isset($rateLimitHeaders['X-RateLimit-Remaining'])) {
            $exception->setRateLimitRemaining((int) $rateLimitHeaders['X-RateLimit-Remaining']);
        }
        
        if (isset($rateLimitHeaders['X-RateLimit-Reset'])) {
            $exception->setRateLimitReset((int) $rateLimitHeaders['X-RateLimit-Reset']);
        }

        return $exception;
    }

    /**
     * Get the number of seconds to wait before retrying
     *
     * @return int|null Seconds to wait, or null if not specified
     */
    public function getRetryAfter(): ?int
    {
        return $this->retryAfter;
    }

    /**
     * Set the number of seconds to wait before retrying
     *
     * @param int|null $retryAfter Seconds to wait
     * @return self
     */
    public function setRetryAfter(?int $retryAfter): self
    {
        $this->retryAfter = $retryAfter;
        return $this;
    }

    /**
     * Get the rate limit (requests per time period)
     *
     * @return int|null Rate limit, or null if not available
     */
    public function getRateLimitLimit(): ?int
    {
        return $this->rateLimitLimit;
    }

    /**
     * Set the rate limit
     *
     * @param int|null $limit Rate limit
     * @return self
     */
    public function setRateLimitLimit(?int $limit): self
    {
        $this->rateLimitLimit = $limit;
        return $this;
    }

    /**
     * Get the remaining requests in the current time window
     *
     * @return int|null Remaining requests, or null if not available
     */
    public function getRateLimitRemaining(): ?int
    {
        return $this->rateLimitRemaining;
    }

    /**
     * Set the remaining requests
     *
     * @param int|null $remaining Remaining requests
     * @return self
     */
    public function setRateLimitRemaining(?int $remaining): self
    {
        $this->rateLimitRemaining = $remaining;
        return $this;
    }

    /**
     * Get the timestamp when the rate limit resets
     *
     * @return int|null Unix timestamp, or null if not available
     */
    public function getRateLimitReset(): ?int
    {
        return $this->rateLimitReset;
    }

    /**
     * Set the rate limit reset timestamp
     *
     * @param int|null $reset Unix timestamp
     * @return self
     */
    public function setRateLimitReset(?int $reset): self
    {
        $this->rateLimitReset = $reset;
        return $this;
    }

    /**
     * Check if retry information is available
     *
     * @return bool True if retry after time is available
     */
    public function hasRetryInfo(): bool
    {
        return $this->retryAfter !== null;
    }

    /**
     * Get a human-readable description of when to retry
     *
     * @return string Description of retry timing
     */
    public function getRetryDescription(): string
    {
        if ($this->retryAfter === null) {
            return 'Retry timing not available';
        }

        if ($this->retryAfter < 60) {
            return "Retry in {$this->retryAfter} seconds";
        }

        $minutes = round($this->retryAfter / 60, 1);
        return "Retry in {$minutes} minutes";
    }

    /**
     * Get rate limit status information
     *
     * @return array Rate limit status
     */
    public function getRateLimitStatus(): array
    {
        return [
            'limit' => $this->rateLimitLimit,
            'remaining' => $this->rateLimitRemaining,
            'reset' => $this->rateLimitReset,
            'retry_after' => $this->retryAfter,
        ];
    }
}