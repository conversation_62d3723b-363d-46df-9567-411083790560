<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Cart entity representing a Freemius shopping cart.
 * 
 * A cart represents a collection of items that a user intends to purchase
 * from the Freemius marketplace.
 */
class Cart extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'product_id' => 'integer',
        'plan_id' => 'integer',
        'licenses' => 'integer',
        'price' => 'float',
        'is_active' => 'boolean',
        'is_migrated' => 'boolean',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'visited',
        'completed',
    ];

    /**
     * Get the cart ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the product ID.
     */
    public function getProductId(): int
    {
        return $this->getAttribute('product_id');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the number of licenses.
     */
    public function getLicenses(): int
    {
        return $this->getAttribute('licenses', 1);
    }

    /**
     * Get the billing cycle.
     */
    public function getBillingCycle(): string
    {
        return $this->getAttribute('billing_cycle', 'annual');
    }

    /**
     * Get the currency code.
     */
    public function getCurrency(): string
    {
        return $this->getAttribute('currency', 'USD');
    }

    /**
     * Get the cart price.
     */
    public function getPrice(): float
    {
        return $this->getAttribute('price', 0.0);
    }

    /**
     * Get the coupon code if applied.
     */
    public function getCouponCode(): ?string
    {
        return $this->getAttribute('coupon_code');
    }

    /**
     * Check if the cart is active.
     */
    public function isActive(): bool
    {
        return $this->getAttribute('is_active', true);
    }

    /**
     * Check if the cart is migrated.
     */
    public function isMigrated(): bool
    {
        return $this->getAttribute('is_migrated', false);
    }

    /**
     * Get the cart status.
     */
    public function getStatus(): string
    {
        return $this->getAttribute('status', 'visit');
    }

    /**
     * Get the cart mode.
     */
    public function getMode(): string
    {
        return $this->getAttribute('mode', 'dialog');
    }

    /**
     * Get the payment method.
     */
    public function getPaymentMethod(): string
    {
        return $this->getAttribute('payment_method', 'cc');
    }

    /**
     * Check if the cart is for a trial.
     */
    public function isTrial(): bool
    {
        return $this->getAttribute('is_trial', false);
    }

    /**
     * Check if the cart is disabled.
     */
    public function isDisabled(): bool
    {
        return $this->getAttribute('is_disabled', false);
    }

    /**
     * Check if the user is unsubscribed from cart recovery.
     */
    public function isUnsubscribed(): bool
    {
        return $this->getAttribute('is_unsubscribed', false);
    }

    /**
     * Get the first name.
     */
    public function getFirst(): ?string
    {
        return $this->getAttribute('first');
    }

    /**
     * Get the last name.
     */
    public function getLast(): ?string
    {
        return $this->getAttribute('last');
    }

    /**
     * Get the page URL.
     */
    public function getUrl(): ?string
    {
        return $this->getAttribute('url');
    }

    /**
     * Get the IP address.
     */
    public function getIp(): ?string
    {
        return $this->getAttribute('ip');
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the visited date.
     */
    public function getVisited(): ?DateTime
    {
        return $this->getAttribute('visited');
    }

    /**
     * Get the completed date.
     */
    public function getCompleted(): ?DateTime
    {
        return $this->getAttribute('completed');
    }
}