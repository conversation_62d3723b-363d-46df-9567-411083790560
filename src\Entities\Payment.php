<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Payment entity representing a Freemius payment transaction.
 * 
 * A payment represents a financial transaction for a product purchase,
 * subscription payment, or refund in the Freemius system.
 */
class Payment extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'product_id' => 'integer',
        'license_id' => 'integer',
        'subscription_id' => 'integer',
        'plan_id' => 'integer',
        'gross' => 'float',
        'net' => 'float',
        'vat' => 'float',
        'commission' => 'float',
        'gateway_fee' => 'float',
        'vat_rate' => 'float',
        'is_refunded' => 'boolean',
        'is_migrated' => 'boolean',
        'external_id' => 'string',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'refunded_at',
    ];

    /**
     * Get the payment ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the product ID.
     */
    public function getProductId(): int
    {
        return $this->getAttribute('product_id');
    }

    /**
     * Get the license ID.
     */
    public function getLicenseId(): ?int
    {
        return $this->getAttribute('license_id');
    }

    /**
     * Get the subscription ID.
     */
    public function getSubscriptionId(): ?int
    {
        return $this->getAttribute('subscription_id');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the payment status.
     */
    public function getStatus(): string
    {
        return $this->getAttribute('status', 'pending');
    }

    /**
     * Get the payment gateway.
     */
    public function getGateway(): string
    {
        return $this->getAttribute('gateway', '');
    }

    /**
     * Get the external payment ID from the gateway.
     */
    public function getExternalId(): ?string
    {
        return $this->getAttribute('external_id');
    }

    /**
     * Get the gross amount.
     */
    public function getGross(): float
    {
        return $this->getAttribute('gross', 0.0);
    }

    /**
     * Get the net amount (after fees and taxes).
     */
    public function getNet(): float
    {
        return $this->getAttribute('net', 0.0);
    }

    /**
     * Get the VAT amount.
     */
    public function getVat(): float
    {
        return $this->getAttribute('vat', 0.0);
    }

    /**
     * Get the commission amount.
     */
    public function getCommission(): float
    {
        return $this->getAttribute('commission', 0.0);
    }

    /**
     * Get the gateway fee amount.
     */
    public function getGatewayFee(): float
    {
        return $this->getAttribute('gateway_fee', 0.0);
    }

    /**
     * Get the VAT rate.
     */
    public function getVatRate(): float
    {
        return $this->getAttribute('vat_rate', 0.0);
    }

    /**
     * Get the currency code.
     */
    public function getCurrency(): string
    {
        return $this->getAttribute('currency', 'USD');
    }

    /**
     * Check if the payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->getAttribute('is_refunded', false);
    }

    /**
     * Check if the payment is migrated.
     */
    public function isMigrated(): bool
    {
        return $this->getAttribute('is_migrated', false);
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->getStatus() === 'completed';
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->getStatus() === 'pending';
    }

    /**
     * Check if the payment failed.
     */
    public function isFailed(): bool
    {
        return $this->getStatus() === 'failed';
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the refund date.
     */
    public function getRefundedAt(): ?DateTime
    {
        return $this->getAttribute('refunded_at');
    }
}