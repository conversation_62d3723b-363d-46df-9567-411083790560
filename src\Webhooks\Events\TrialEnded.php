<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\Trial;
use Freemius\SDK\Entities\User;

/**
 * Trial ended webhook event
 * 
 * Triggered when a trial is cancelled, expired, or converted to paid.
 */
class TrialEnded extends WebhookEvent
{
    private ?Trial $trial = null;
    private ?User $user = null;

    /**
     * Get the trial associated with this event
     *
     * @return Trial|null The trial entity
     */
    public function getTrial(): ?Trial
    {
        if ($this->trial === null) {
            $trialData = $this->getNestedData('trial');
            if ($trialData) {
                $this->trial = $this->createEntity($trialData, Trial::class);
            }
        }

        return $this->trial;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the trial ID
     *
     * @return int|null The trial ID
     */
    public function getTrialId(): ?int
    {
        $trialId = $this->get('trial_id');
        return $trialId ? (int) $trialId : null;
    }

    /**
     * Check if trial was canceled
     *
     * @return bool True if trial was canceled
     */
    public function isCanceled(): bool
    {
        return $this->eventType === 'trial.canceled';
    }

    /**
     * Check if trial was cancelled (alias for backward compatibility)
     *
     * @return bool True if trial was cancelled
     * @deprecated Use isCanceled() instead
     */
    public function isCancelled(): bool
    {
        return $this->isCanceled();
    }

    /**
     * Check if trial expired
     *
     * @return bool True if trial expired
     */
    public function isExpired(): bool
    {
        return $this->eventType === 'trial.expired';
    }

    /**
     * Check if trial was converted to paid
     *
     * @return bool True if trial was converted
     */
    public function isConverted(): bool
    {
        return $this->eventType === 'trial.converted';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'trial.canceled' => 'canceled',
            'trial.expired' => 'expired',
            'trial.converted' => 'converted',
            default => 'ended'
        };

        $parts = ["Trial {$action}"];

        if ($trialId = $this->getTrialId()) {
            $parts[] = "trial:{$trialId}";
        }

        return implode(' ', $parts);
    }
}