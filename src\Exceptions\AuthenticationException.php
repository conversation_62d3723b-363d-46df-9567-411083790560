<?php

declare(strict_types=1);

namespace Freemius\SDK\Exceptions;

use Throwable;

/**
 * Authentication exception for Freemius API authentication failures
 * 
 * Thrown when authentication fails due to invalid credentials,
 * expired tokens, or other authentication-related issues.
 */
class AuthenticationException extends FreemiusException
{
    /**
     * Create a new authentication exception
     *
     * @param string $message Exception message
     * @param int $code Exception code (typically HTTP status code)
     * @param Throwable|null $previous Previous exception
     * @param array $context Additional context information
     */
    public function __construct(
        string $message = 'Authentication failed',
        int $code = 401,
        ?Throwable $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous, $context);
    }

    /**
     * Create an exception for invalid Bearer token
     *
     * @param string $token The invalid token (will be redacted in context)
     * @param array $additionalContext Additional context
     * @return self
     */
    public static function invalidBearerToken(string $token = '', array $additionalContext = []): self
    {
        $context = array_merge([
            'token_length' => strlen($token),
            'token_prefix' => $token ? substr($token, 0, 8) . '...' : 'empty',
        ], $additionalContext);

        return new self(
            'Invalid Bearer token provided',
            401,
            null,
            $context
        );
    }

    /**
     * Create an exception for expired token
     *
     * @param array $context Additional context
     * @return self
     */
    public static function expiredToken(array $context = []): self
    {
        return new self(
            'Bearer token has expired',
            401,
            null,
            $context
        );
    }

    /**
     * Create an exception for missing authentication
     *
     * @param array $context Additional context
     * @return self
     */
    public static function missingAuthentication(array $context = []): self
    {
        return new self(
            'Authentication credentials are required but not provided',
            401,
            null,
            $context
        );
    }

    /**
     * Create an exception for insufficient permissions
     *
     * @param string $resource The resource being accessed
     * @param string $action The action being attempted
     * @param array $context Additional context
     * @return self
     */
    public static function insufficientPermissions(
        string $resource = '',
        string $action = '',
        array $context = []
    ): self {
        $message = 'Insufficient permissions';
        
        if ($resource && $action) {
            $message .= " to {$action} {$resource}";
        } elseif ($resource) {
            $message .= " for {$resource}";
        }

        $context = array_merge([
            'resource' => $resource,
            'action' => $action,
        ], $context);

        return new self($message, 403, null, $context);
    }
}