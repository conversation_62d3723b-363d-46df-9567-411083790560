<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use <PERSON><PERSON>us\SDK\Configuration;
use <PERSON><PERSON>us\SDK\Logger;
use <PERSON>mius\SDK\Authentication\AuthenticationInterface;
use <PERSON>mius\SDK\Exceptions\FreemiusException;
use <PERSON>mius\SDK\Exceptions\RateLimitException;
use Guzzle<PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Exception;

/**
 * HTTP client wrapper with Guzzle integration
 * 
 * Provides HTTP client functionality with authentication,
 * logging, retry logic, timeout configuration, rate limiting,
 * caching, and connection pooling.
 */
class HttpClient implements HttpClientInterface
{
    private GuzzleClient $client;
    private Configuration $config;
    private Logger $logger;
    private ?AuthenticationInterface $auth;
    private RateLimiter $rateLimiter;
    private RequestCache $cache;
    private ConnectionPool $connectionPool;

    /**
     * Create a new HTTP client instance
     *
     * @param Configuration $config SDK configuration
     * @param Logger $logger Logger instance
     * @param AuthenticationInterface|null $auth Authentication handler
     */
    public function __construct(
        Configuration $config,
        Logger $logger,
        ?AuthenticationInterface $auth = null
    ) {
        $this->config = $config;
        $this->logger = $logger;
        $this->auth = $auth;
        $this->rateLimiter = new RateLimiter($config);
        $this->cache = new RequestCache(
            $config->isCacheEnabled(),
            $config->getCacheTtl(),
            $config->getCacheMaxSize()
        );
        $this->connectionPool = new ConnectionPool();
        $this->client = $this->createGuzzleClient();
    }

    /**
     * {@inheritdoc}
     */
    public function get(string $uri, array $params = [], array $headers = []): ResponseInterface
    {
        // Check cache first for GET requests
        $cachedResponse = $this->cache->get('GET', $uri, $params);
        if ($cachedResponse !== null) {
            $this->logger->logCacheHit('GET', $uri, $params);
            return $cachedResponse;
        }

        $options = [];
        
        if (!empty($params)) {
            $options['query'] = $params;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        $response = $this->sendRequest('GET', $uri, $options);
        
        // Cache successful GET responses
        if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
            $this->cache->put('GET', $uri, $params, $response);
        }

        return $response;
    }

    /**
     * {@inheritdoc}
     */
    public function post(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('POST', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function put(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('PUT', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function delete(string $uri, array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('DELETE', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function patch(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('PATCH', $uri, $options);
    }

    /**
     * Set authentication handler
     *
     * @param AuthenticationInterface $auth Authentication handler
     * @return self
     */
    public function setAuth(AuthenticationInterface $auth): self
    {
        $this->auth = $auth;
        return $this;
    }

    /**
     * Get rate limiter instance
     *
     * @return RateLimiter
     */
    public function getRateLimiter(): RateLimiter
    {
        return $this->rateLimiter;
    }

    /**
     * Get request cache instance
     *
     * @return RequestCache
     */
    public function getCache(): RequestCache
    {
        return $this->cache;
    }

    /**
     * Get connection pool instance
     *
     * @return ConnectionPool
     */
    public function getConnectionPool(): ConnectionPool
    {
        return $this->connectionPool;
    }

    /**
     * Enable or disable request caching
     *
     * @param bool $enabled Whether to enable caching
     * @return self
     */
    public function setCacheEnabled(bool $enabled): self
    {
        $this->cache->setEnabled($enabled);
        return $this;
    }

    /**
     * Set cache TTL
     *
     * @param int $ttl TTL in seconds
     * @return self
     */
    public function setCacheTtl(int $ttl): self
    {
        $this->cache->setDefaultTtl($ttl);
        return $this;
    }

    /**
     * Clear request cache
     *
     * @return self
     */
    public function clearCache(): self
    {
        $this->cache->clear();
        return $this;
    }

    /**
     * Invalidate cache entries matching a pattern
     *
     * @param string $pattern URI pattern to match
     * @return int Number of invalidated entries
     */
    public function invalidateCache(string $pattern): int
    {
        return $this->cache->invalidate($pattern);
    }

    /**
     * Get performance statistics
     *
     * @return array Performance statistics
     */
    public function getPerformanceStats(): array
    {
        return [
            'rate_limiter' => $this->rateLimiter->getStatus(),
            'cache' => $this->cache->getStats(),
            'connection_pool' => $this->connectionPool->getStats(),
        ];
    }

    /**
     * Send an HTTP request with authentication, rate limiting, and logging
     *
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @param array $options Request options
     * @return ResponseInterface
     * @throws GuzzleException
     * @throws RateLimitException
     */
    private function sendRequest(string $method, string $uri, array $options = []): ResponseInterface
    {
        $attempt = 0;
        $maxAttempts = $this->config->getRetryAttempts() + 1;

        while ($attempt < $maxAttempts) {
            try {
                // Check rate limits before making request
                $this->rateLimiter->canMakeRequest();

                $startTime = microtime(true);
                
                // Create the request
                $request = new Request($method, $uri);
                
                // Apply authentication if available
                if ($this->auth !== null) {
                    $request = $this->auth->authenticate($request);
                    
                    // Merge authentication headers with request options
                    if ($request->getHeaders()) {
                        $options['headers'] = array_merge(
                            $options['headers'] ?? [],
                            $request->getHeaders()
                        );
                    }
                }

                // Log the request
                $this->logger->logRequest($request, [
                    'options' => $this->sanitizeOptionsForLogging($options),
                    'attempt' => $attempt + 1,
                    'max_attempts' => $maxAttempts
                ]);

                // Send the request
                $response = $this->client->request($method, $uri, $options);
                
                // Calculate duration
                $duration = microtime(true) - $startTime;
                
                // Record successful request for rate limiting
                $this->rateLimiter->recordRequest();
                
                // Update rate limiter with response headers
                $responseHeaders = [];
                foreach ($response->getHeaders() as $name => $values) {
                    $responseHeaders[$name] = $values[0] ?? '';
                }
                $this->rateLimiter->updateFromHeaders($responseHeaders);
                
                // Log the response
                $this->logger->logResponse($response, $duration);
                
                return $response;
                
            } catch (RequestException $e) {
                $duration = microtime(true) - $startTime ?? 0;
                
                // Convert to SDK exception
                $sdkException = ErrorHandler::handleRequestException($e);
                
                // Handle rate limiting with backoff
                if ($sdkException instanceof RateLimitException) {
                    $retryAfter = $sdkException->getRetryAfter();
                    $backoffDelay = $this->rateLimiter->calculateBackoffDelay($attempt, $retryAfter);
                    
                    if ($attempt < $maxAttempts - 1) {
                        $this->logger->logRateLimitBackoff($backoffDelay, $attempt + 1, $maxAttempts);
                        sleep($backoffDelay);
                        $attempt++;
                        continue;
                    }
                }
                
                // Log the error
                $this->logger->logError($sdkException, [
                    'method' => $method,
                    'uri' => $uri,
                    'duration' => $duration,
                    'attempt' => $attempt + 1,
                    'max_attempts' => $maxAttempts,
                    'options' => $this->sanitizeOptionsForLogging($options)
                ]);
                
                // If we have a response, log it too
                if ($e->hasResponse()) {
                    $this->logger->logResponse($e->getResponse(), $duration);
                }
                
                throw $sdkException;
                
            } catch (RateLimitException $e) {
                // Handle client-side rate limiting
                $backoffDelay = $this->rateLimiter->calculateBackoffDelay($attempt, $e->getRetryAfter());
                
                if ($attempt < $maxAttempts - 1) {
                    $this->logger->logRateLimitBackoff($backoffDelay, $attempt + 1, $maxAttempts);
                    sleep($backoffDelay);
                    $attempt++;
                    continue;
                }
                
                throw $e;
                
            } catch (Exception $e) {
                $duration = microtime(true) - $startTime ?? 0;
                
                // Wrap unexpected errors in SDK exception
                $sdkException = new FreemiusException(
                    'Unexpected error: ' . $e->getMessage(),
                    $e->getCode(),
                    $e,
                    [
                        'method' => $method,
                        'uri' => $uri,
                        'duration' => $duration,
                        'attempt' => $attempt + 1,
                        'max_attempts' => $maxAttempts,
                        'options' => $this->sanitizeOptionsForLogging($options)
                    ]
                );
                
                // Log unexpected errors
                $this->logger->logError($sdkException);
                
                throw $sdkException;
            }
        }

        // This should never be reached, but just in case
        throw new FreemiusException('Maximum retry attempts exceeded');
    }

    /**
     * Create and configure the Guzzle HTTP client
     *
     * @return GuzzleClient
     */
    private function createGuzzleClient(): GuzzleClient
    {
        // Use connection pool for optimized client
        $options = [
            'timeout' => $this->config->getTimeout(),
            'headers' => [
                'User-Agent' => 'Freemius-PHP-SDK/1.0',
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'http_errors' => true, // Let Guzzle throw exceptions for HTTP errors
            'verify' => true, // Always verify SSL certificates
        ];

        return $this->connectionPool->getClient($this->config->getBaseUrl(), $options);
    }



    /**
     * Sanitize request options for logging (remove sensitive data)
     *
     * @param array $options Request options
     * @return array Sanitized options
     */
    private function sanitizeOptionsForLogging(array $options): array
    {
        $sanitized = $options;
        
        // Remove sensitive headers
        if (isset($sanitized['headers'])) {
            $sensitiveHeaders = ['authorization', 'x-api-key', 'x-auth-token'];
            
            foreach ($sensitiveHeaders as $header) {
                if (isset($sanitized['headers'][$header])) {
                    $sanitized['headers'][$header] = '[REDACTED]';
                }
            }
        }
        
        // Remove sensitive body data
        if (isset($sanitized['json'])) {
            $sanitized['json'] = $this->redactSensitiveData($sanitized['json']);
        }
        
        if (isset($sanitized['form_params'])) {
            $sanitized['form_params'] = $this->redactSensitiveData($sanitized['form_params']);
        }

        return $sanitized;
    }

    /**
     * Redact sensitive data from arrays
     *
     * @param array $data Data to redact
     * @return array Redacted data
     */
    private function redactSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'bearer', 'api_key', 'access_token', 'refresh_token'
        ];
        
        $redacted = [];
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower((string) $key);
            
            if (in_array($lowerKey, $sensitiveFields, true)) {
                $redacted[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $redacted[$key] = $this->redactSensitiveData($value);
            } else {
                $redacted[$key] = $value;
            }
        }

        return $redacted;
    }
}