<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Installation;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Installations resource for managing Freemius installations.
 * 
 * Handles CRUD operations and specialized methods for installation management.
 */
class Installations extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'installations';
    }
    
    protected function getEntityClass(): string
    {
        return Installation::class;
    }
    
    /**
     * Get installations by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get installations by license ID.
     */
    public function byLicense(int $licenseId): self
    {
        return $this->where('license_id', $licenseId);
    }
    
    /**
     * Get installations by site URL.
     */
    public function bySite(string $siteUrl): self
    {
        return $this->where('url', $siteUrl);
    }
    
    /**
     * Search installations by site URL pattern.
     */
    public function searchBySite(string $pattern): self
    {
        return $this->where('url', 'like', '%' . $pattern . '%');
    }
    
    /**
     * Get active installations only.
     */
    public function active(): self
    {
        return $this->where('is_active', true);
    }
    
    /**
     * Get inactive installations only.
     */
    public function inactive(): self
    {
        return $this->where('is_active', false);
    }
    
    /**
     * Get installations by version.
     */
    public function byVersion(string $version): self
    {
        return $this->where('version', $version);
    }
    
    /**
     * Get installations with version greater than specified.
     */
    public function versionGreaterThan(string $version): self
    {
        return $this->where('version', '>', $version);
    }
    
    /**
     * Get installations with version less than specified.
     */
    public function versionLessThan(string $version): self
    {
        return $this->where('version', '<', $version);
    }
    
    /**
     * Get installations created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get installations created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get installations last seen after a specific date.
     */
    public function lastSeenAfter(string $date): self
    {
        return $this->where('last_seen', '>', $date);
    }
    
    /**
     * Get installations last seen before a specific date.
     */
    public function lastSeenBefore(string $date): self
    {
        return $this->where('last_seen', '<', $date);
    }
    
    /**
     * Activate an installation.
     */
    public function activate(int $installationId, array $activationData = [])
    {
        $endpoint = $this->buildEndpoint($installationId) . '/activate';
        $response = $this->httpClient->post($endpoint, $activationData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Installation($data);
    }
    
    /**
     * Deactivate an installation.
     */
    public function deactivate(int $installationId): bool
    {
        $endpoint = $this->buildEndpoint($installationId) . '/deactivate';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Update installation version.
     */
    public function updateVersion(int $installationId, string $version)
    {
        $endpoint = $this->buildEndpoint($installationId) . '/version';
        $response = $this->httpClient->put($endpoint, ['version' => $version]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Installation($data);
    }
    
    /**
     * Record installation heartbeat (last seen).
     */
    public function heartbeat(int $installationId, array $heartbeatData = [])
    {
        $endpoint = $this->buildEndpoint($installationId) . '/heartbeat';
        $response = $this->httpClient->post($endpoint, $heartbeatData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Installation($data);
    }
    
    /**
     * Get installation events/logs.
     */
    public function events(int $installationId, array $filters = [])
    {
        $endpoint = $this->buildEndpoint($installationId) . '/events';
        $response = $this->httpClient->get($endpoint, $filters);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return $this->createCollection($data);
    }
    
    /**
     * Get installation analytics data.
     */
    public function analytics(int $installationId, array $params = []): array
    {
        $endpoint = $this->buildEndpoint($installationId) . '/analytics';
        $response = $this->httpClient->get($endpoint, $params);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Get installation user resource.
     */
    public function user(int $installationId): Users
    {
        $installation = $this->get($installationId);
        /** @var Users $users */
        $users = $this->createChildResource(Users::class);
        return $users->where('id', $installation->getUserId());
    }
    
    /**
     * Get installation license resource.
     */
    public function license(int $installationId): Licenses
    {
        $installation = $this->get($installationId);
        /** @var Licenses $licenses */
        $licenses = $this->createChildResource(Licenses::class);
        return $licenses->where('id', $installation->getLicenseId());
    }
    
    /**
     * Block an installation.
     */
    public function block(int $installationId, array $blockData = []): bool
    {
        $endpoint = $this->buildEndpoint($installationId) . '/block';
        $response = $this->httpClient->post($endpoint, $blockData);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Unblock an installation.
     */
    public function unblock(int $installationId): bool
    {
        $endpoint = $this->buildEndpoint($installationId) . '/unblock';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Transfer installation to another user.
     */
    public function transfer(int $installationId, int $newUserId)
    {
        $endpoint = $this->buildEndpoint($installationId) . '/transfer';
        $response = $this->httpClient->post($endpoint, ['user_id' => $newUserId]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Installation($data);
    }
    
    /**
     * Get installation configuration.
     */
    public function configuration(int $installationId): array
    {
        $endpoint = $this->buildEndpoint($installationId) . '/configuration';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Update installation configuration.
     */
    public function updateConfiguration(int $installationId, array $config): array
    {
        $endpoint = $this->buildEndpoint($installationId) . '/configuration';
        $response = $this->httpClient->put($endpoint, $config);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'url'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating an installation");
            }
        }
        
        // Validate user ID
        if (!is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        // Validate URL format
        if (!filter_var($data['url'], FILTER_VALIDATE_URL)) {
            throw new ValidationException("Invalid URL format");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate user ID if provided
        if (isset($data['user_id']) && !is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        // Validate URL format if provided
        if (isset($data['url']) && !filter_var($data['url'], FILTER_VALIDATE_URL)) {
            throw new ValidationException("Invalid URL format");
        }
        
        // Validate license ID if provided
        if (isset($data['license_id']) && !is_numeric($data['license_id'])) {
            throw new ValidationException("License ID must be numeric");
        }
    }
}