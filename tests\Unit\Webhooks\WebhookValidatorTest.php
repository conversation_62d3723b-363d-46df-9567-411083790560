<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Webhooks;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Webhooks\WebhookValidator;
use Freemius\SDK\Exceptions\WebhookException;

/**
 * Unit tests for WebhookValidator
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Webhooks\WebhookValidator::class)]
class WebhookValidatorTest extends TestCase
{
    private string $webhookSecret = 'test_webhook_secret_12345';
    private WebhookValidator $validator;

    protected function setUp(): void
    {
        $this->validator = new WebhookValidator($this->webhookSecret);
    }

    public function testConstructorWithValidSecret(): void
    {
        $validator = new WebhookValidator($this->webhookSecret);
        $this->assertInstanceOf(WebhookValidator::class, $validator);
    }

    public function testConstructorWithEmptySecret(): void
    {
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook secret is required for signature validation but not configured');
        
        new WebhookValidator('');
    }

    public function testValidateSignatureWithValidSignature(): void
    {
        $payload = '{"test": "data"}';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validateSignature($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateSignatureWithInvalidSignature(): void
    {
        $payload = '{"test": "data"}';
        $invalidSignature = 'invalid_signature_123';
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->validator->validateSignature($payload, $invalidSignature);
    }

    public function testValidateSignatureWithEmptyPayload(): void
    {
        $payload = '';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validateSignature($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateSignatureWithEmptySignature(): void
    {
        $payload = '{"test": "data"}';
        $signature = '';
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature is missing from request headers');
        
        $this->validator->validateSignature($payload, $signature);
    }

    public function testValidateSignatureWithModifiedPayload(): void
    {
        $originalPayload = '{"test": "data"}';
        $modifiedPayload = '{"test": "modified_data"}';
        $signature = hash_hmac('sha256', $originalPayload, $this->webhookSecret);
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->validator->validateSignature($modifiedPayload, $signature);
    }

    public function testValidateSignatureWithDifferentSecret(): void
    {
        $payload = '{"test": "data"}';
        $differentSecret = 'different_secret_67890';
        $signature = hash_hmac('sha256', $payload, $differentSecret);
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->validator->validateSignature($payload, $signature);
    }

    public function testValidateSignatureWithComplexPayload(): void
    {
        $payload = json_encode([
            'id' => 'evt_123456789',
            'type' => 'install.activated',
            'created' => 1623456789,
            'data' => [
                'object' => [
                    'id' => 77777,
                    'user_id' => 98765,
                    'product_id' => 12345,
                    'url' => 'https://example.com',
                    'title' => 'Test Site',
                    'is_active' => true,
                ],
            ],
        ]);
        
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validateSignature($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateTimestampWithValidTimestamp(): void
    {
        $currentTime = time();
        $validTimestamp = $currentTime - 30; // 30 seconds ago
        
        $result = $this->validator->validateTimestamp($validTimestamp);
        
        $this->assertTrue($result);
    }

    public function testValidateTimestampWithExpiredTimestamp(): void
    {
        $expiredTimestamp = time() - 400; // 400 seconds ago (> 5 minutes)
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $this->validator->validateTimestamp($expiredTimestamp);
    }

    public function testValidateTimestampWithFutureTimestamp(): void
    {
        $futureTimestamp = time() + 400; // 400 seconds in the future
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $this->validator->validateTimestamp($futureTimestamp);
    }

    public function testValidateTimestampWithZeroTimestamp(): void
    {
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $this->validator->validateTimestamp(0);
    }

    public function testValidateTimestampWithNegativeTimestamp(): void
    {
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $this->validator->validateTimestamp(-100);
    }

    public function testValidateTimestampWithCustomTolerance(): void
    {
        $validator = new WebhookValidator($this->webhookSecret, 600); // 10 minutes tolerance
        $timestamp = time() - 500; // 8 minutes ago
        
        $result = $validator->validateTimestamp($timestamp);
        
        $this->assertTrue($result);
    }

    public function testValidateTimestampWithZeroTolerance(): void
    {
        $validator = new WebhookValidator($this->webhookSecret, 0);
        $timestamp = time() - 1; // 1 second ago
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $validator->validateTimestamp($timestamp);
    }

    public function testValidateWithValidPayloadAndSignature(): void
    {
        $payload = '{"id": "evt_123", "type": "test.event", "created": ' . time() . '}';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateWithInvalidSignature(): void
    {
        $payload = '{"id": "evt_123", "type": "test.event", "created": ' . time() . '}';
        $invalidSignature = 'invalid_signature';
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->validator->validate($payload, $invalidSignature);
    }

    public function testValidateWithExpiredTimestamp(): void
    {
        $expiredTimestamp = time() - 400;
        $payload = '{"id": "evt_123", "type": "test.event", "created": ' . $expiredTimestamp . '}';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook timestamp validation failed');
        
        $this->validator->validate($payload, $signature, $expiredTimestamp);
    }

    public function testValidateWithMalformedJson(): void
    {
        $payload = '{"id": "evt_123", "type": "test.event", "created":'; // Malformed JSON
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // The validator doesn't parse JSON, so malformed JSON will still pass signature validation
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateWithMissingCreatedField(): void
    {
        $payload = '{"id": "evt_123", "type": "test.event"}'; // Missing 'created' field
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // The validator doesn't validate payload structure, only signature
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateWithMissingIdField(): void
    {
        $payload = '{"type": "test.event", "created": ' . time() . '}'; // Missing 'id' field
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // The validator doesn't validate payload structure, only signature
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateWithMissingTypeField(): void
    {
        $payload = '{"id": "evt_123", "created": ' . time() . '}'; // Missing 'type' field
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // The validator doesn't validate payload structure, only signature
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testValidateWithCompleteValidPayload(): void
    {
        $payload = json_encode([
            'id' => 'evt_install_activated_123',
            'type' => 'install.activated',
            'created' => time(),
            'data' => [
                'object' => [
                    'id' => 77777,
                    'user_id' => 98765,
                    'product_id' => 12345,
                    'url' => 'https://example.com',
                    'title' => 'Test Site',
                    'is_active' => true,
                ],
            ],
        ]);
        
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validate($payload, $signature);
        
        $this->assertTrue($result);
    }

    public function testSignatureAlgorithmConsistency(): void
    {
        $payload = '{"test": "consistency"}';
        
        // Generate signature using the same algorithm as the validator
        $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        // Validate using the validator
        $result = $this->validator->validateSignature($payload, $expectedSignature);
        
        $this->assertTrue($result);
    }

    public function testCaseSensitiveSignature(): void
    {
        $payload = '{"test": "case_sensitive"}';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        $uppercaseSignature = strtoupper($signature);
        
        // Should fail with uppercase signature
        $this->expectException(WebhookException::class);
        $this->expectExceptionMessage('Webhook signature validation failed');
        
        $this->validator->validateSignature($payload, $uppercaseSignature);
    }

    public function testUnicodePayloadHandling(): void
    {
        $payload = '{"message": "Hello 世界! 🌍"}';
        $signature = hash_hmac('sha256', $payload, $this->webhookSecret);
        
        $result = $this->validator->validateSignature($payload, $signature);
        
        $this->assertTrue($result);
    }
}