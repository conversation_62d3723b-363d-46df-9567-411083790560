<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Plan;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Plans resource for managing Freemius pricing plans.
 * 
 * Handles CRUD operations and specialized methods for plan management.
 */
class Plans extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'plans';
    }
    
    protected function getEntityClass(): string
    {
        return Plan::class;
    }
    
    /**
     * Get free plans only.
     */
    public function free(): self
    {
        return $this->where('is_free', true);
    }
    
    /**
     * Get paid plans only.
     */
    public function paid(): self
    {
        return $this->where('is_free', false);
    }
    
    /**
     * Get featured plans only.
     */
    public function featured(): self
    {
        return $this->where('is_featured', true);
    }
    
    /**
     * Get visible (not hidden) plans only.
     */
    public function visible(): self
    {
        return $this->where('is_hidden', false);
    }
    
    /**
     * Get hidden plans only.
     */
    public function hidden(): self
    {
        return $this->where('is_hidden', true);
    }
    
    /**
     * Get active (not deleted) plans only.
     */
    public function active(): self
    {
        return $this->where('is_deleted', false);
    }
    
    /**
     * Get deleted plans only.
     */
    public function deleted(): self
    {
        return $this->where('is_deleted', true);
    }
    
    /**
     * Get plans by pricing model.
     */
    public function byPricingModel(string $model): self
    {
        return $this->where('pricing_model', $model);
    }
    
    /**
     * Get per-site pricing plans.
     */
    public function perSite(): self
    {
        return $this->byPricingModel('per-site');
    }
    
    /**
     * Get per-user pricing plans.
     */
    public function perUser(): self
    {
        return $this->byPricingModel('per-user');
    }
    
    /**
     * Get unlimited pricing plans.
     */
    public function unlimited(): self
    {
        return $this->byPricingModel('unlimited');
    }
    
    /**
     * Get plans with trial period.
     */
    public function withTrial(): self
    {
        return $this->where('trial_period', '>', 0);
    }
    
    /**
     * Get plans without trial period.
     */
    public function withoutTrial(): self
    {
        return $this->where('trial_period', 0);
    }
    
    /**
     * Get plans with unlimited installs.
     */
    public function unlimitedInstalls(): self
    {
        return $this->where('max_installs', null);
    }
    
    /**
     * Get plans with limited installs.
     */
    public function limitedInstalls(): self
    {
        return $this->where('max_installs', '>', 0);
    }
    
    /**
     * Get plans with annual discount.
     */
    public function withAnnualDiscount(): self
    {
        return $this->where('annual_discount', '>', 0);
    }
    
    /**
     * Get plans created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get plans created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Search plans by name.
     */
    public function searchByName(string $name): self
    {
        return $this->where('name', 'like', '%' . $name . '%');
    }
    
    /**
     * Search plans by title.
     */
    public function searchByTitle(string $title): self
    {
        return $this->where('title', 'like', '%' . $title . '%');
    }
    
    /**
     * Get plan pricing information.
     */
    public function getPricing(int $planId): array
    {
        $endpoint = $this->buildEndpoint($planId) . '/pricing';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Get plan features.
     */
    public function getFeatures(int $planId): array
    {
        $endpoint = $this->buildEndpoint($planId) . '/features';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Add feature to plan.
     */
    public function addFeature(int $planId, array $featureData): array
    {
        $endpoint = $this->buildEndpoint($planId) . '/features';
        $response = $this->httpClient->post($endpoint, $featureData);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Remove feature from plan.
     */
    public function removeFeature(int $planId, int $featureId): bool
    {
        $endpoint = $this->buildEndpoint($planId) . '/features/' . $featureId;
        $response = $this->httpClient->delete($endpoint);
        
        return $response->getStatusCode() === 204;
    }
    
    /**
     * Update plan pricing.
     */
    public function updatePricing(int $planId, array $pricingData)
    {
        $endpoint = $this->buildEndpoint($planId) . '/pricing';
        $response = $this->httpClient->put($endpoint, $pricingData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Plan($data);
    }
    
    /**
     * Clone a plan.
     */
    public function clone(int $planId, array $cloneData = [])
    {
        $endpoint = $this->buildEndpoint($planId) . '/clone';
        $response = $this->httpClient->post($endpoint, $cloneData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Plan($data);
    }
    
    /**
     * Archive a plan (soft delete).
     */
    public function archive(int $planId)
    {
        $endpoint = $this->buildEndpoint($planId) . '/archive';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Plan($data);
    }
    
    /**
     * Restore an archived plan.
     */
    public function restore(int $planId)
    {
        $endpoint = $this->buildEndpoint($planId) . '/restore';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Plan($data);
    }
    
    /**
     * Get plan statistics.
     */
    public function getStatistics(int $planId): array
    {
        $endpoint = $this->buildEndpoint($planId) . '/stats';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Get plan licenses resource.
     */
    public function licenses(int $planId): Licenses
    {
        /** @var Licenses $licenses */
        $licenses = $this->createChildResource(Licenses::class);
        return $licenses->byPlan($planId);
    }
    
    /**
     * Get plan subscriptions resource.
     */
    public function subscriptions(int $planId): Subscriptions
    {
        /** @var Subscriptions $subscriptions */
        $subscriptions = $this->createChildResource(Subscriptions::class);
        return $subscriptions->byPlan($planId);
    }
    
    /**
     * Get plan payments resource.
     */
    public function payments(int $planId): Payments
    {
        /** @var Payments $payments */
        $payments = $this->createChildResource(Payments::class);
        return $payments->byPlan($planId);
    }
    
    /**
     * Get plan carts resource.
     */
    public function carts(int $planId): Carts
    {
        /** @var Carts $carts */
        $carts = $this->createChildResource(Carts::class);
        return $carts->byPlan($planId);
    }
    
    /**
     * Get plan coupons resource.
     */
    public function coupons(int $planId): Coupons
    {
        /** @var Coupons $coupons */
        $coupons = $this->createChildResource(Coupons::class);
        return $coupons->byPlan($planId);
    }
    
    /**
     * Get plan trials resource.
     */
    public function trials(int $planId): Trials
    {
        /** @var Trials $trials */
        $trials = $this->createChildResource(Trials::class);
        return $trials->byPlan($planId);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['name', 'title'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a plan");
            }
        }
        
        // Validate numeric fields
        $numericFields = ['product_id', 'parent_plan_id', 'trial_period', 'annual_discount', 'max_installs'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate trial period
        if (isset($data['trial_period']) && $data['trial_period'] < 0) {
            throw new ValidationException("Trial period must be non-negative");
        }
        
        // Validate annual discount
        if (isset($data['annual_discount'])) {
            if ($data['annual_discount'] < 0 || $data['annual_discount'] > 100) {
                throw new ValidationException("Annual discount must be between 0 and 100");
            }
        }
        
        // Validate max installs
        if (isset($data['max_installs']) && $data['max_installs'] < 0) {
            throw new ValidationException("Max installs must be non-negative");
        }
        
        // Validate pricing model if provided
        if (isset($data['pricing_model'])) {
            $validModels = ['per-site', 'per-user', 'unlimited', 'freemium'];
            if (!in_array($data['pricing_model'], $validModels)) {
                throw new ValidationException("Invalid pricing model. Must be one of: " . implode(', ', $validModels));
            }
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        $numericFields = ['product_id', 'parent_plan_id', 'trial_period', 'annual_discount', 'max_installs'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate trial period if provided
        if (isset($data['trial_period']) && $data['trial_period'] < 0) {
            throw new ValidationException("Trial period must be non-negative");
        }
        
        // Validate annual discount if provided
        if (isset($data['annual_discount'])) {
            if ($data['annual_discount'] < 0 || $data['annual_discount'] > 100) {
                throw new ValidationException("Annual discount must be between 0 and 100");
            }
        }
        
        // Validate max installs if provided
        if (isset($data['max_installs']) && $data['max_installs'] < 0) {
            throw new ValidationException("Max installs must be non-negative");
        }
        
        // Validate pricing model if provided
        if (isset($data['pricing_model'])) {
            $validModels = ['per-site', 'per-user', 'unlimited', 'freemium'];
            if (!in_array($data['pricing_model'], $validModels)) {
                throw new ValidationException("Invalid pricing model. Must be one of: " . implode(', ', $validModels));
            }
        }
    }
}