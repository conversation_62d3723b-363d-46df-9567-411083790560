<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * User entity representing a Freemius user.
 * 
 * A User is a person who is registered to Freemius through some usage of 
 * one or more products selling through Freemius.
 */
class User extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'is_marketing_allowed' => 'boolean',
        'is_beta' => 'boolean',
        'is_verified' => 'boolean',
        'gross' => 'float',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'last_login_at',
    ];

    /**
     * Get the user ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the user's email address.
     */
    public function getEmail(): string
    {
        return $this->getAttribute('email', '');
    }

    /**
     * Get the user's first name.
     */
    public function getFirstName(): string
    {
        return $this->getAttribute('first', '');
    }

    /**
     * Get the user's last name.
     */
    public function getLastName(): string
    {
        return $this->getAttribute('last', '');
    }

    /**
     * Get the user's full name.
     */
    public function getFullName(): string
    {
        $first = $this->getFirstName();
        $last = $this->getLastName();
        
        if ($first && $last) {
            return "{$first} {$last}";
        }
        
        return $first ?: $last;
    }

    /**
     * Get the user's profile picture URL.
     */
    public function getPicture(): ?string
    {
        return $this->getAttribute('picture');
    }

    /**
     * Get the user's IP address.
     */
    public function getIp(): ?string
    {
        return $this->getAttribute('ip');
    }

    /**
     * Check if the user is verified.
     */
    public function isVerified(): bool
    {
        return $this->getAttribute('is_verified', false);
    }

    /**
     * Get the authentication type.
     */
    public function getAuthType(): string
    {
        return $this->getAttribute('auth', 'password');
    }

    /**
     * Check if the user has 2FA enabled.
     */
    public function hasTwoFactorAuth(): bool
    {
        return $this->getAuthType() === 'app2fa';
    }

    /**
     * Check if marketing is allowed.
     */
    public function isMarketingAllowed(): ?bool
    {
        return $this->getAttribute('is_marketing_allowed');
    }

    /**
     * Check if the user is opted in to beta versions.
     */
    public function isBeta(): bool
    {
        return $this->getAttribute('is_beta', false);
    }

    /**
     * Get the total amount spent by the user.
     */
    public function getGross(): float
    {
        return $this->getAttribute('gross', 0.0);
    }

    /**
     * Get the last login date.
     */
    public function getLastLoginAt(): ?DateTime
    {
        return $this->getAttribute('last_login_at');
    }

    /**
     * Get the email status.
     */
    public function getEmailStatus(): ?string
    {
        return $this->getAttribute('email_status');
    }

    /**
     * Check if the user's email is deliverable.
     */
    public function isEmailDeliverable(): bool
    {
        $status = $this->getEmailStatus();
        return $status === null || $status === 'delivered';
    }

    /**
     * Get the user's note (visible only to developer).
     */
    public function getNote(): ?string
    {
        return $this->getAttribute('note');
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }

    /**
     * Get the secret key.
     */
    public function getSecretKey(): ?string
    {
        return $this->getAttribute('secret_key');
    }

    /**
     * Get the public key.
     */
    public function getPublicKey(): ?string
    {
        return $this->getAttribute('public_key');
    }
}