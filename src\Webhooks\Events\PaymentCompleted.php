<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\Payment;
use Freemius\SDK\Entities\User;
use Freemius\SDK\Entities\License;

/**
 * Payment webhook event
 * 
 * Triggered when a payment is completed, failed, refunded, or charged back.
 */
class PaymentCompleted extends WebhookEvent
{
    private ?Payment $payment = null;
    private ?User $user = null;
    private ?License $license = null;

    /**
     * Get the payment associated with this event
     *
     * @return Payment|null The payment entity
     */
    public function getPayment(): ?Payment
    {
        if ($this->payment === null) {
            $paymentData = $this->getNestedData('payment');
            if ($paymentData) {
                $this->payment = $this->createEntity($paymentData, Payment::class);
            }
        }

        return $this->payment;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the license associated with this event
     *
     * @return License|null The license entity
     */
    public function getLicense(): ?License
    {
        if ($this->license === null) {
            $licenseData = $this->getNestedData('license');
            if ($licenseData) {
                $this->license = $this->createEntity($licenseData, License::class);
            }
        }

        return $this->license;
    }

    /**
     * Get the payment ID
     *
     * @return int|null The payment ID
     */
    public function getPaymentId(): ?int
    {
        $paymentId = $this->get('payment_id');
        return $paymentId ? (int) $paymentId : null;
    }

    /**
     * Get the payment amount
     *
     * @return float|null The payment amount
     */
    public function getAmount(): ?float
    {
        $amount = $this->getNestedData('payment.amount') ?? $this->get('amount');
        return $amount ? (float) $amount : null;
    }

    /**
     * Get the payment currency
     *
     * @return string|null The currency code
     */
    public function getCurrency(): ?string
    {
        return $this->getNestedData('payment.currency') ?? $this->get('currency');
    }

    /**
     * Check if this is a successful payment
     *
     * @return bool True if payment was completed
     */
    public function isCompleted(): bool
    {
        return $this->eventType === 'payment.completed';
    }

    /**
     * Check if this is a failed payment
     *
     * @return bool True if payment failed
     */
    public function isFailed(): bool
    {
        return $this->eventType === 'payment.failed';
    }

    /**
     * Check if this is a refunded payment
     *
     * @return bool True if payment was refunded
     */
    public function isRefunded(): bool
    {
        return $this->eventType === 'payment.refunded';
    }

    /**
     * Check if this is a chargeback
     *
     * @return bool True if payment was charged back
     */
    public function isChargeback(): bool
    {
        return $this->eventType === 'payment.chargeback';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'payment.completed' => 'completed',
            'payment.failed' => 'failed',
            'payment.refunded' => 'refunded',
            'payment.chargeback' => 'charged back',
            default => 'processed'
        };

        $parts = ["Payment {$action}"];

        if ($paymentId = $this->getPaymentId()) {
            $parts[] = "payment:{$paymentId}";
        }

        if ($amount = $this->getAmount()) {
            $currency = $this->getCurrency() ?? 'USD';
            $parts[] = "{$currency}{$amount}";
        }

        return implode(' ', $parts);
    }
}