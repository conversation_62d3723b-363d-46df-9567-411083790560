<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Payment;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Payments resource for managing Freemius payments.
 * 
 * Handles CRUD operations and specialized methods for payment management.
 */
class Payments extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'payments';
    }
    
    protected function getEntityClass(): string
    {
        return Payment::class;
    }
    
    /**
     * Get payments by status.
     */
    public function byStatus(string $status): self
    {
        return $this->where('status', $status);
    }
    
    /**
     * Get completed payments only.
     */
    public function completed(): self
    {
        return $this->byStatus('completed');
    }
    
    /**
     * Get pending payments only.
     */
    public function pending(): self
    {
        return $this->byStatus('pending');
    }
    
    /**
     * Get failed payments only.
     */
    public function failed(): self
    {
        return $this->byStatus('failed');
    }
    
    /**
     * Get refunded payments only.
     */
    public function refunded(): self
    {
        return $this->where('is_refunded', true);
    }
    
    /**
     * Get payments by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get payments by license ID.
     */
    public function byLicense(int $licenseId): self
    {
        return $this->where('license_id', $licenseId);
    }
    
    /**
     * Get payments by subscription ID.
     */
    public function bySubscription(int $subscriptionId): self
    {
        return $this->where('subscription_id', $subscriptionId);
    }
    
    /**
     * Get payments by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get payments by gateway.
     */
    public function byGateway(string $gateway): self
    {
        return $this->where('gateway', $gateway);
    }
    
    /**
     * Get payments by currency.
     */
    public function byCurrency(string $currency): self
    {
        return $this->where('currency', $currency);
    }
    
    /**
     * Get payments with gross amount greater than specified value.
     */
    public function grossGreaterThan(float $amount): self
    {
        return $this->where('gross', '>', $amount);
    }
    
    /**
     * Get payments with gross amount less than specified value.
     */
    public function grossLessThan(float $amount): self
    {
        return $this->where('gross', '<', $amount);
    }
    
    /**
     * Get payments created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get payments created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get payments within a date range.
     */
    public function betweenDates(string $startDate, string $endDate): self
    {
        return $this->where('created', '>=', $startDate)
                   ->where('created', '<=', $endDate);
    }
    
    /**
     * Refund a payment.
     */
    public function refund(int $paymentId, array $refundData = [])
    {
        $endpoint = $this->buildEndpoint($paymentId) . '/refund';
        $response = $this->httpClient->post($endpoint, $refundData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Payment($data);
    }
    
    /**
     * Get payment details with related entities.
     */
    public function getWithRelations(int $paymentId): Payment
    {
        $endpoint = $this->buildEndpoint($paymentId) . '?include=user,license,subscription,plan';
        $response = $this->httpClient->get($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Payment($data);
    }
    
    /**
     * Get payment invoice.
     */
    public function getInvoice(int $paymentId): array
    {
        $endpoint = $this->buildEndpoint($paymentId) . '/invoice';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Send payment receipt email.
     */
    public function sendReceipt(int $paymentId): bool
    {
        $endpoint = $this->buildEndpoint($paymentId) . '/receipt';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Get payment statistics.
     */
    public function getStatistics(array $filters = []): array
    {
        $endpoint = $this->buildEndpoint() . '/stats';
        if (!empty($filters)) {
            $endpoint .= '?' . http_build_query($filters);
        }
        
        $response = $this->httpClient->get($endpoint);
        return json_decode($response->getBody()->getContents(), true);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'product_id', 'plan_id', 'gross'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a payment");
            }
        }
        
        // Validate numeric fields
        $numericFields = ['user_id', 'product_id', 'plan_id'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate amount fields
        $amountFields = ['gross', 'net', 'vat', 'commission', 'gateway_fee'];
        foreach ($amountFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate status if provided
        if (isset($data['status'])) {
            $validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'refunded'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new ValidationException("Invalid status. Must be one of: " . implode(', ', $validStatuses));
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        $numericFields = ['user_id', 'product_id', 'plan_id', 'license_id', 'subscription_id'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate amount fields if provided
        $amountFields = ['gross', 'net', 'vat', 'commission', 'gateway_fee'];
        foreach ($amountFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate status if provided
        if (isset($data['status'])) {
            $validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'refunded'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new ValidationException("Invalid status. Must be one of: " . implode(', ', $validStatuses));
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
}