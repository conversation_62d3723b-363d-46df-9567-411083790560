<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Unit\Authentication;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\Authentication\BearerAuth;
use GuzzleHttp\Psr7\Request;
use InvalidArgumentException;

/**
 * Unit tests for BearerAuth
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Authentication\BearerAuth::class)]
class BearerAuthTest extends TestCase
{
    private string $validToken = 'test_bearer_token_123456789';

    public function testConstructorWithValidToken(): void
    {
        $auth = new BearerAuth($this->validToken);
        $this->assertEquals($this->validToken, $auth->getToken());
    }

    public function testConstructorWithEmptyToken(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Bearer token cannot be empty');
        
        new BearerAuth('');
    }

    public function testConstructorWithWhitespaceToken(): void
    {
        // Whitespace-only tokens are actually accepted by the current implementation
        // since empty() doesn't consider whitespace as empty
        $auth = new BearerAuth('   ');
        $this->assertEquals('   ', $auth->getToken());
    }

    public function testAuthenticate(): void
    {
        $auth = new BearerAuth($this->validToken);
        $request = new Request('GET', 'https://api.example.com/test');
        
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertNotSame($request, $authenticatedRequest);
        $this->assertTrue($authenticatedRequest->hasHeader('Authorization'));
        $this->assertEquals(
            'Bearer ' . $this->validToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }

    public function testAuthenticatePreservesExistingHeaders(): void
    {
        $auth = new BearerAuth($this->validToken);
        $request = new Request('POST', 'https://api.example.com/test', [
            'Content-Type' => 'application/json',
            'X-Custom-Header' => 'custom-value'
        ]);
        
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertEquals('application/json', $authenticatedRequest->getHeaderLine('Content-Type'));
        $this->assertEquals('custom-value', $authenticatedRequest->getHeaderLine('X-Custom-Header'));
        $this->assertEquals(
            'Bearer ' . $this->validToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }

    public function testAuthenticateOverridesExistingAuthorizationHeader(): void
    {
        $auth = new BearerAuth($this->validToken);
        $request = new Request('GET', 'https://api.example.com/test', [
            'Authorization' => 'Basic old_auth_value'
        ]);
        
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertEquals(
            'Bearer ' . $this->validToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }

    public function testGetToken(): void
    {
        $auth = new BearerAuth($this->validToken);
        $this->assertEquals($this->validToken, $auth->getToken());
    }

    public function testSetTokenWithValidToken(): void
    {
        $auth = new BearerAuth($this->validToken);
        $newToken = 'new_bearer_token_987654321';
        
        $result = $auth->setToken($newToken);
        
        $this->assertSame($auth, $result);
        $this->assertEquals($newToken, $auth->getToken());
    }

    public function testSetTokenWithEmptyToken(): void
    {
        $auth = new BearerAuth($this->validToken);
        
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Bearer token cannot be empty');
        
        $auth->setToken('');
    }

    public function testSetTokenWithWhitespaceToken(): void
    {
        $auth = new BearerAuth($this->validToken);
        
        // Whitespace-only tokens are actually accepted by the current implementation
        $result = $auth->setToken('   ');
        $this->assertSame($auth, $result);
        $this->assertEquals('   ', $auth->getToken());
    }

    public function testTokenUpdateReflectedInAuthentication(): void
    {
        $auth = new BearerAuth($this->validToken);
        $newToken = 'updated_bearer_token_555';
        
        $auth->setToken($newToken);
        
        $request = new Request('GET', 'https://api.example.com/test');
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertEquals(
            'Bearer ' . $newToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }

    public function testMultipleAuthenticationCallsWithSameRequest(): void
    {
        $auth = new BearerAuth($this->validToken);
        $request = new Request('GET', 'https://api.example.com/test');
        
        $firstAuth = $auth->authenticate($request);
        $secondAuth = $auth->authenticate($request);
        
        $this->assertNotSame($firstAuth, $secondAuth);
        $this->assertEquals(
            $firstAuth->getHeaderLine('Authorization'),
            $secondAuth->getHeaderLine('Authorization')
        );
    }

    public function testAuthenticateWithDifferentHttpMethods(): void
    {
        $auth = new BearerAuth($this->validToken);
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        
        foreach ($methods as $method) {
            $request = new Request($method, 'https://api.example.com/test');
            $authenticatedRequest = $auth->authenticate($request);
            
            $this->assertEquals(
                'Bearer ' . $this->validToken,
                $authenticatedRequest->getHeaderLine('Authorization'),
                "Failed for HTTP method: {$method}"
            );
        }
    }

    public function testAuthenticateWithComplexToken(): void
    {
        $complexToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJmcmVlbWl1cyIsImF1ZCI6ImFwaSIsImlhdCI6MTYyMzQwNzIwMCwiZXhwIjoxNjIzNDkzNjAwfQ.signature';
        $auth = new BearerAuth($complexToken);
        $request = new Request('GET', 'https://api.example.com/test');
        
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertEquals(
            'Bearer ' . $complexToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }

    public function testTokenSecurityConsiderations(): void
    {
        $sensitiveToken = 'sk_live_very_secret_token_12345';
        $auth = new BearerAuth($sensitiveToken);
        
        // Ensure token is stored correctly
        $this->assertEquals($sensitiveToken, $auth->getToken());
        
        // Ensure authentication works with sensitive token
        $request = new Request('GET', 'https://api.example.com/test');
        $authenticatedRequest = $auth->authenticate($request);
        
        $this->assertEquals(
            'Bearer ' . $sensitiveToken,
            $authenticatedRequest->getHeaderLine('Authorization')
        );
    }
}