<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\Installation;
use Freemius\SDK\Entities\User;
use Freemius\SDK\Entities\License;

/**
 * Install activation/deactivation webhook event
 * 
 * Triggered when a product installation is activated, deactivated, or uninstalled.
 */
class InstallActivated extends WebhookEvent
{
    private ?Installation $installation = null;
    private ?User $user = null;
    private ?License $license = null;

    /**
     * Get the installation associated with this event
     *
     * @return Installation|null The installation entity
     */
    public function getInstallation(): ?Installation
    {
        if ($this->installation === null) {
            $installData = $this->getNestedData('install') ?? $this->getNestedData('installation');
            if ($installData) {
                $this->installation = $this->createEntity($installData, Installation::class);
            }
        }

        return $this->installation;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the license associated with this event (if applicable)
     *
     * @return License|null The license entity
     */
    public function getLicense(): ?License
    {
        if ($this->license === null) {
            $licenseData = $this->getNestedData('license');
            if ($licenseData) {
                $this->license = $this->createEntity($licenseData, License::class);
            }
        }

        return $this->license;
    }

    /**
     * Get the installation ID
     *
     * @return int|null The installation ID
     */
    public function getInstallationId(): ?int
    {
        $installId = $this->get('install_id') ?? $this->get('installation_id');
        return $installId ? (int) $installId : null;
    }

    /**
     * Get the site URL where the installation occurred
     *
     * @return string|null The site URL
     */
    public function getSiteUrl(): ?string
    {
        return $this->getNestedData('install.url') ?? 
               $this->getNestedData('installation.url') ?? 
               $this->get('site_url');
    }

    /**
     * Get the site title
     *
     * @return string|null The site title
     */
    public function getSiteTitle(): ?string
    {
        return $this->getNestedData('install.title') ?? 
               $this->getNestedData('installation.title') ?? 
               $this->get('site_title');
    }

    /**
     * Check if this is an activation event
     *
     * @return bool True if this is an activation
     */
    public function isActivation(): bool
    {
        return $this->eventType === 'install.activated';
    }

    /**
     * Check if this is a deactivation event
     *
     * @return bool True if this is a deactivation
     */
    public function isDeactivation(): bool
    {
        return $this->eventType === 'install.deactivated';
    }

    /**
     * Check if this is an uninstall event
     *
     * @return bool True if this is an uninstall
     */
    public function isUninstall(): bool
    {
        return $this->eventType === 'install.uninstalled';
    }

    /**
     * Get the product version that was installed
     *
     * @return string|null The product version
     */
    public function getProductVersion(): ?string
    {
        return $this->getNestedData('install.version') ?? 
               $this->getNestedData('installation.version') ?? 
               $this->get('version');
    }

    /**
     * Check if this installation is for a premium version
     *
     * @return bool True if premium
     */
    public function isPremium(): bool
    {
        return (bool) ($this->getNestedData('install.is_premium') ?? 
                      $this->getNestedData('installation.is_premium') ?? 
                      $this->get('is_premium') ?? 
                      false);
    }

    /**
     * Get the plan ID if this is a premium installation
     *
     * @return int|null The plan ID
     */
    public function getPlanId(): ?int
    {
        $planId = $this->getNestedData('install.plan_id') ?? 
                  $this->getNestedData('installation.plan_id') ?? 
                  $this->get('plan_id');
        return $planId ? (int) $planId : null;
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'install.activated' => 'activated',
            'install.deactivated' => 'deactivated',
            'install.uninstalled' => 'uninstalled',
            default => 'changed'
        };

        $parts = ["Installation {$action}"];

        if ($installId = $this->getInstallationId()) {
            $parts[] = "install:{$installId}";
        }

        if ($siteUrl = $this->getSiteUrl()) {
            $parts[] = "site:{$siteUrl}";
        }

        if ($this->isPremium()) {
            $parts[] = 'premium';
        }

        return implode(' ', $parts);
    }

    /**
     * Convert to array with installation-specific data
     *
     * @return array The event data
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        $data['installation_id'] = $this->getInstallationId();
        $data['site_url'] = $this->getSiteUrl();
        $data['site_title'] = $this->getSiteTitle();
        $data['is_premium'] = $this->isPremium();
        $data['product_version'] = $this->getProductVersion();
        $data['plan_id'] = $this->getPlanId();
        $data['action'] = match ($this->eventType) {
            'install.activated' => 'activated',
            'install.deactivated' => 'deactivated',
            'install.uninstalled' => 'uninstalled',
            default => 'unknown'
        };

        return $data;
    }
}