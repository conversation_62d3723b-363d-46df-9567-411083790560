<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Subscription;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Subscriptions resource for managing Freemius subscriptions.
 * 
 * Handles CRUD operations and specialized methods for subscription management.
 */
class Subscriptions extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'subscriptions';
    }
    
    protected function getEntityClass(): string
    {
        return Subscription::class;
    }
    
    /**
     * Get subscriptions by status.
     */
    public function byStatus(string $status): self
    {
        return $this->where('status', $status);
    }
    
    /**
     * Get active subscriptions only.
     */
    public function active(): self
    {
        return $this->byStatus('active');
    }
    
    /**
     * Get cancelled subscriptions only.
     */
    public function cancelled(): self
    {
        return $this->byStatus('cancelled');
    }
    
    /**
     * Get expired subscriptions only.
     */
    public function expired(): self
    {
        return $this->byStatus('expired');
    }
    
    /**
     * Get pending subscriptions only.
     */
    public function pending(): self
    {
        return $this->byStatus('pending');
    }
    
    /**
     * Get subscriptions by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get subscriptions by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get subscriptions by billing cycle.
     */
    public function byBillingCycle(string $cycle): self
    {
        return $this->where('billing_cycle', $cycle);
    }
    
    /**
     * Get monthly subscriptions.
     */
    public function monthly(): self
    {
        return $this->byBillingCycle('monthly');
    }
    
    /**
     * Get annual subscriptions.
     */
    public function annual(): self
    {
        return $this->byBillingCycle('annual');
    }
    
    /**
     * Get lifetime subscriptions.
     */
    public function lifetime(): self
    {
        return $this->byBillingCycle('lifetime');
    }
    
    /**
     * Get subscriptions expiring before a specific date.
     */
    public function expiringBefore(string $date): self
    {
        return $this->where('next_payment', '<', $date);
    }
    
    /**
     * Get subscriptions expiring after a specific date.
     */
    public function expiringAfter(string $date): self
    {
        return $this->where('next_payment', '>', $date);
    }
    
    /**
     * Get subscriptions created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get subscriptions created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Cancel a subscription.
     */
    public function cancel(int $subscriptionId, array $cancellationData = [])
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/cancel';
        $response = $this->httpClient->post($endpoint, $cancellationData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Reactivate a cancelled subscription.
     */
    public function reactivate(int $subscriptionId)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/reactivate';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Upgrade a subscription to a different plan.
     */
    public function upgrade(int $subscriptionId, int $newPlanId, array $upgradeData = [])
    {
        $upgradeData['plan_id'] = $newPlanId;
        
        $endpoint = $this->buildEndpoint($subscriptionId) . '/upgrade';
        $response = $this->httpClient->post($endpoint, $upgradeData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Downgrade a subscription to a different plan.
     */
    public function downgrade(int $subscriptionId, int $newPlanId, array $downgradeData = [])
    {
        $downgradeData['plan_id'] = $newPlanId;
        
        $endpoint = $this->buildEndpoint($subscriptionId) . '/downgrade';
        $response = $this->httpClient->post($endpoint, $downgradeData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Change subscription billing cycle.
     */
    public function changeBillingCycle(int $subscriptionId, string $newCycle)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/billing-cycle';
        $response = $this->httpClient->put($endpoint, ['billing_cycle' => $newCycle]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Get subscription invoices.
     */
    public function invoices(int $subscriptionId)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/invoices';
        $response = $this->httpClient->get($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return $this->createCollection($data);
    }
    
    /**
     * Get subscription payments resource.
     */
    public function payments(int $subscriptionId): Payments
    {
        /** @var Payments $payments */
        $payments = $this->createChildResource(Payments::class);
        return $payments->bySubscription($subscriptionId);
    }
    
    /**
     * Process immediate payment for subscription.
     */
    public function processPayment(int $subscriptionId, array $paymentData = []): array
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/charge';
        $response = $this->httpClient->post($endpoint, $paymentData);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Apply coupon to subscription.
     */
    public function applyCoupon(int $subscriptionId, string $couponCode)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/coupon';
        $response = $this->httpClient->post($endpoint, ['coupon_code' => $couponCode]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Remove coupon from subscription.
     */
    public function removeCoupon(int $subscriptionId)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/coupon';
        $response = $this->httpClient->delete($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    /**
     * Update subscription payment method.
     */
    public function updatePaymentMethod(int $subscriptionId, array $paymentMethodData)
    {
        $endpoint = $this->buildEndpoint($subscriptionId) . '/payment-method';
        $response = $this->httpClient->put($endpoint, $paymentMethodData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Subscription($data);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'plan_id'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a subscription");
            }
        }
        
        // Validate numeric fields
        if (!is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        if (!is_numeric($data['plan_id'])) {
            throw new ValidationException("Plan ID must be numeric");
        }
        
        // Validate billing cycle if provided
        if (isset($data['billing_cycle'])) {
            $validCycles = ['monthly', 'annual', 'lifetime'];
            if (!in_array($data['billing_cycle'], $validCycles)) {
                throw new ValidationException("Invalid billing cycle. Must be one of: " . implode(', ', $validCycles));
            }
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        if (isset($data['user_id']) && !is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        if (isset($data['plan_id']) && !is_numeric($data['plan_id'])) {
            throw new ValidationException("Plan ID must be numeric");
        }
        
        // Validate status if provided
        if (isset($data['status'])) {
            $validStatuses = ['active', 'cancelled', 'expired', 'pending', 'trial'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new ValidationException("Invalid status. Must be one of: " . implode(', ', $validStatuses));
            }
        }
        
        // Validate billing cycle if provided
        if (isset($data['billing_cycle'])) {
            $validCycles = ['monthly', 'annual', 'lifetime'];
            if (!in_array($data['billing_cycle'], $validCycles)) {
                throw new ValidationException("Invalid billing cycle. Must be one of: " . implode(', ', $validCycles));
            }
        }
    }
}