<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\Trial;
use Freemius\SDK\Entities\User;
use DateTime;

/**
 * Trial started webhook event
 * 
 * Triggered when a trial period is started.
 */
class TrialStarted extends WebhookEvent
{
    private ?Trial $trial = null;
    private ?User $user = null;

    /**
     * Get the trial associated with this event
     *
     * @return Trial|null The trial entity
     */
    public function getTrial(): ?Trial
    {
        if ($this->trial === null) {
            $trialData = $this->getNestedData('trial');
            if ($trialData) {
                $this->trial = $this->createEntity($trialData, Trial::class);
            }
        }

        return $this->trial;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the trial ID
     *
     * @return int|null The trial ID
     */
    public function getTrialId(): ?int
    {
        $trialId = $this->get('trial_id');
        return $trialId ? (int) $trialId : null;
    }

    /**
     * Get the trial duration in days
     *
     * @return int|null The trial duration
     */
    public function getTrialDuration(): ?int
    {
        $duration = $this->getNestedData('trial.duration') ?? $this->get('trial_duration');
        return $duration ? (int) $duration : null;
    }

    /**
     * Get the trial expiration date
     *
     * @return DateTime|null The expiration date
     */
    public function getExpirationDate(): ?DateTime
    {
        $expiration = $this->getNestedData('trial.expires_at') ?? $this->get('expires_at');

        if (!$expiration) {
            return null;
        }

        if (is_numeric($expiration)) {
            return new DateTime('@' . $expiration);
        }

        if (is_string($expiration)) {
            return new DateTime($expiration);
        }

        return null;
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $parts = ['Trial started'];

        if ($trialId = $this->getTrialId()) {
            $parts[] = "trial:{$trialId}";
        }

        if ($duration = $this->getTrialDuration()) {
            $parts[] = "{$duration} days";
        }

        return implode(' ', $parts);
    }
}