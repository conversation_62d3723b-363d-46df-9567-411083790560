<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

/**
 * Coupon webhook event
 * 
 * Triggered for coupon-related events like creation, updates, deletion.
 */
class CouponEvent extends WebhookEvent
{
    /**
     * Get the coupon ID
     *
     * @return int|null The coupon ID
     */
    public function getCouponId(): ?int
    {
        $couponId = $this->get('coupon_id');
        return $couponId ? (int) $couponId : null;
    }

    /**
     * Get the coupon code
     *
     * @return string|null The coupon code
     */
    public function getCouponCode(): ?string
    {
        return $this->getNestedData('coupon.code') ?? $this->get('coupon_code');
    }

    /**
     * Get the coupon discount amount
     *
     * @return float|null The discount amount
     */
    public function getDiscountAmount(): ?float
    {
        $amount = $this->getNestedData('coupon.discount_amount') ?? $this->get('discount_amount');
        return $amount ? (float) $amount : null;
    }

    /**
     * Get the coupon discount percentage
     *
     * @return float|null The discount percentage
     */
    public function getDiscountPercentage(): ?float
    {
        $percentage = $this->getNestedData('coupon.discount_percentage') ?? $this->get('discount_percentage');
        return $percentage ? (float) $percentage : null;
    }

    /**
     * Check if this is a coupon creation
     *
     * @return bool True if coupon was created
     */
    public function isCreation(): bool
    {
        return $this->eventType === 'coupon.created';
    }

    /**
     * Check if this is a coupon update
     *
     * @return bool True if coupon was updated
     */
    public function isUpdate(): bool
    {
        return $this->eventType === 'coupon.updated';
    }

    /**
     * Check if this is a coupon deletion
     *
     * @return bool True if coupon was deleted
     */
    public function isDeletion(): bool
    {
        return $this->eventType === 'coupon.deleted';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'coupon.created' => 'created',
            'coupon.deleted' => 'deleted',
            'coupon.updated' => 'updated',
            default => 'changed'
        };

        $parts = ["Coupon {$action}"];

        if ($couponId = $this->getCouponId()) {
            $parts[] = "coupon:{$couponId}";
        }

        if ($code = $this->getCouponCode()) {
            $parts[] = "code:{$code}";
        }

        return implode(' ', $parts);
    }
}
