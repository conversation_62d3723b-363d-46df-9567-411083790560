<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

/**
 * Query builder for constructing API request parameters.
 * 
 * Supports filtering, pagination, and ordering for Freemius API requests.
 */
class QueryBuilder
{
    private array $wheres = [];
    private ?int $count = null;
    private ?int $offset = null;
    private array $orderBy = [];
    private ?string $search = null;
    private ?string $email = null;
    private ?string $filter = null;
    private ?string $currency = null;
    private ?int $couponId = null;
    private ?int $billingCycle = null;
    private ?string $gateway = null;
    private ?string $sort = null;
    private ?string $code = null;
    private ?string $prefix = null;
    private ?string $version = null;
    private ?string $title = null;
    private ?string $url = null;
    private ?string $reasonId = null;
    private ?string $source = null;
    private bool $enriched = false;
    private bool $extended = false;
    private bool $all = false;
    private ?int $userId = null;
    private ?int $planId = null;
    private ?int $licenseId = null;
    private ?int $subscriptionId = null;
    private ?string $status = null;
    private ?bool $isActive = null;
    private ?bool $isPremium = null;
    private ?bool $isUninstalled = null;
    private ?bool $isDisconnected = null;
    private ?string $countryCode = null;
    private ?string $language = null;
    private ?string $platformVersion = null;
    private ?string $type = null;
    private ?string $externalId = null;
    private ?string $environment = null;
    
    /**
     * Add a where condition.
     */
    public function where(string $field, string $operator, $value): self
    {
        $this->wheres[] = [
            'field' => $field,
            'operator' => $operator,
            'value' => $value,
        ];
        
        return $this;
    }
    
    /**
     * Set the count (number of records to return).
     * Freemius API uses 'count' parameter instead of 'limit'.
     */
    public function count(int $count): self
    {
        $this->count = $count;
        return $this;
    }

    /**
     * Set the limit (alias for count for backward compatibility).
     */
    public function limit(int $count): self
    {
        return $this->count($count);
    }
    
    /**
     * Set the offset.
     */
    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * Add an order by clause.
     */
    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->orderBy[] = [
            'field' => $field,
            'direction' => strtolower($direction),
        ];

        return $this;
    }

    /**
     * Set search parameter for general text search.
     * Freemius API supports searching by ID, name, email, etc.
     */
    public function search(string $search): self
    {
        $this->search = $search;
        return $this;
    }

    /**
     * Set email filter for filtering by email address.
     */
    public function email(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    /**
     * Add a where condition with simplified syntax.
     */
    public function whereEquals(string $field, $value): self
    {
        return $this->where($field, '=', $value);
    }

    /**
     * Add a where condition for filtering by status.
     */
    public function whereStatus(string $status): self
    {
        return $this->where('status', '=', $status);
    }

    /**
     * Add a where condition for filtering by plan ID.
     */
    public function wherePlan(int $planId): self
    {
        return $this->where('plan_id', '=', $planId);
    }

    /**
     * Add a where condition for filtering by user ID.
     */
    public function whereUser(int $userId): self
    {
        return $this->where('user_id', '=', $userId);
    }

    /**
     * Set filter parameter for different resource types.
     *
     * For payments: 'all', 'refunds', 'not_refunded', 'disputed', 'won_disputes', 'chargebacks'
     * For subscriptions: status-based filtering
     * For licenses: status-based filtering
     * For installs: status-based filtering
     */
    public function filter(string $filter): self
    {
        $this->filter = $filter;
        return $this;
    }

    /**
     * Filter by currency (for payments).
     */
    public function currency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * Filter by coupon ID (for payments).
     */
    public function couponId(int $couponId): self
    {
        $this->couponId = $couponId;
        return $this;
    }

    /**
     * Filter by billing cycle.
     * 0 = lifetime, 1 = monthly, 12 = annual, etc.
     */
    public function billingCycle(int $cycle): self
    {
        $this->billingCycle = $cycle;
        return $this;
    }

    /**
     * Filter by payment gateway.
     */
    public function gateway(string $gateway): self
    {
        $this->gateway = $gateway;
        return $this;
    }

    /**
     * Set sort order (for subscriptions and other endpoints).
     * Example: '-id' for descending by ID, 'created' for ascending by created date.
     */
    public function sort(string $sort): self
    {
        $this->sort = $sort;
        return $this;
    }

    /**
     * Filter by coupon code (for coupons).
     */
    public function code(string $code): self
    {
        $this->code = $code;
        return $this;
    }

    /**
     * Filter by coupon code prefix (for coupons).
     */
    public function prefix(string $prefix): self
    {
        $this->prefix = $prefix;
        return $this;
    }

    /**
     * Filter by version (for installs/deployments).
     */
    public function version(string $version): self
    {
        $this->version = $version;
        return $this;
    }

    /**
     * Filter by title (for installs).
     */
    public function title(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    /**
     * Filter by URL (for installs).
     */
    public function url(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    /**
     * Filter by reason ID (for uninstalls).
     */
    public function reasonId(string $reasonId): self
    {
        $this->reasonId = $reasonId;
        return $this;
    }

    /**
     * Filter by source (migration source).
     */
    public function source(string $source): self
    {
        $this->source = $source;
        return $this;
    }

    /**
     * Enable enriched data (additional fields).
     */
    public function enriched(bool $enriched = true): self
    {
        $this->enriched = $enriched;
        return $this;
    }

    /**
     * Enable extended data.
     */
    public function extended(bool $extended = true): self
    {
        $this->extended = $extended;
        return $this;
    }

    /**
     * Get all records (bypass pagination).
     */
    public function all(bool $all = true): self
    {
        $this->all = $all;
        return $this;
    }

    /**
     * Filter by user ID.
     */
    public function userId(int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     * Filter by plan ID.
     */
    public function planId(int $planId): self
    {
        $this->planId = $planId;
        return $this;
    }

    /**
     * Filter by license ID.
     */
    public function licenseId(int $licenseId): self
    {
        $this->licenseId = $licenseId;
        return $this;
    }

    /**
     * Filter by subscription ID.
     */
    public function subscriptionId(int $subscriptionId): self
    {
        $this->subscriptionId = $subscriptionId;
        return $this;
    }

    /**
     * Filter by status.
     */
    public function status(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Filter by active status.
     */
    public function isActive(bool $isActive = true): self
    {
        $this->isActive = $isActive;
        return $this;
    }

    /**
     * Filter by premium status.
     */
    public function isPremium(bool $isPremium = true): self
    {
        $this->isPremium = $isPremium;
        return $this;
    }

    /**
     * Filter by uninstalled status.
     */
    public function isUninstalled(bool $isUninstalled = true): self
    {
        $this->isUninstalled = $isUninstalled;
        return $this;
    }

    /**
     * Filter by disconnected status.
     */
    public function isDisconnected(bool $isDisconnected = true): self
    {
        $this->isDisconnected = $isDisconnected;
        return $this;
    }

    /**
     * Filter by country code.
     */
    public function countryCode(string $countryCode): self
    {
        $this->countryCode = $countryCode;
        return $this;
    }

    /**
     * Filter by language.
     */
    public function language(string $language): self
    {
        $this->language = $language;
        return $this;
    }

    /**
     * Filter by platform version.
     */
    public function platformVersion(string $platformVersion): self
    {
        $this->platformVersion = $platformVersion;
        return $this;
    }

    /**
     * Filter by type (for coupons, reviews, etc.).
     */
    public function type(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Filter by external ID.
     */
    public function externalId(string $externalId): self
    {
        $this->externalId = $externalId;
        return $this;
    }

    /**
     * Filter by environment (0 = production, 1 = sandbox).
     */
    public function environment(string $environment): self
    {
        $this->environment = $environment;
        return $this;
    }

    /**
     * Advanced date range filtering.
     */
    public function createdBetween(string $startDate, string $endDate): self
    {
        return $this->where('created', '>=', $startDate)
                   ->where('created', '<=', $endDate);
    }

    /**
     * Advanced date range filtering for updated dates.
     */
    public function updatedBetween(string $startDate, string $endDate): self
    {
        return $this->where('updated', '>=', $startDate)
                   ->where('updated', '<=', $endDate);
    }

    /**
     * Filter by created date (after).
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }

    /**
     * Filter by created date (before).
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }

    /**
     * Filter by multiple plan IDs.
     */
    public function planIds(array $planIds): self
    {
        return $this->where('plan_id', 'in', $planIds);
    }

    /**
     * Filter by multiple user IDs.
     */
    public function userIds(array $userIds): self
    {
        return $this->where('user_id', 'in', $userIds);
    }

    /**
     * Filter by multiple statuses.
     */
    public function statuses(array $statuses): self
    {
        return $this->where('status', 'in', $statuses);
    }

    /**
     * Exclude specific IDs.
     */
    public function excludeIds(array $ids): self
    {
        return $this->where('id', 'not_in', $ids);
    }

    /**
     * Filter by gross amount range (for payments/subscriptions).
     */
    public function grossBetween(float $min, float $max): self
    {
        return $this->where('gross', '>=', $min)
                   ->where('gross', '<=', $max);
    }

    /**
     * Filter by minimum gross amount.
     */
    public function grossMin(float $amount): self
    {
        return $this->where('gross', '>=', $amount);
    }

    /**
     * Filter by maximum gross amount.
     */
    public function grossMax(float $amount): self
    {
        return $this->where('gross', '<=', $amount);
    }
    
    /**
     * Convert the query to an array of parameters.
     */
    public function toArray(): array
    {
        $params = [];
        
        // Add where conditions
        foreach ($this->wheres as $where) {
            $key = $where['field'];
            
            // Handle different operators
            switch ($where['operator']) {
                case '=':
                case 'eq':
                    $params[$key] = $where['value'];
                    break;
                case '!=':
                case 'ne':
                    $params[$key . '[ne]'] = $where['value'];
                    break;
                case '>':
                case 'gt':
                    $params[$key . '[gt]'] = $where['value'];
                    break;
                case '>=':
                case 'gte':
                    $params[$key . '[gte]'] = $where['value'];
                    break;
                case '<':
                case 'lt':
                    $params[$key . '[lt]'] = $where['value'];
                    break;
                case '<=':
                case 'lte':
                    $params[$key . '[lte]'] = $where['value'];
                    break;
                case 'like':
                    $params[$key . '[like]'] = $where['value'];
                    break;
                case 'in':
                    if (is_array($where['value'])) {
                        $params[$key . '[in]'] = implode(',', $where['value']);
                    } else {
                        $params[$key . '[in]'] = $where['value'];
                    }
                    break;
                case 'not_in':
                    if (is_array($where['value'])) {
                        $params[$key . '[not_in]'] = implode(',', $where['value']);
                    } else {
                        $params[$key . '[not_in]'] = $where['value'];
                    }
                    break;
                default:
                    $params[$key] = $where['value'];
            }
        }
        
        // Add search parameters
        if ($this->search !== null) {
            $params['search'] = $this->search;
        }

        if ($this->email !== null) {
            $params['email'] = $this->email;
        }

        // Add filtering parameters
        if ($this->filter !== null) {
            $params['filter'] = $this->filter;
        }

        if ($this->currency !== null) {
            $params['currency'] = $this->currency;
        }

        if ($this->couponId !== null) {
            $params['coupon_id'] = $this->couponId;
        }

        if ($this->billingCycle !== null) {
            $params['billing_cycle'] = $this->billingCycle;
        }

        if ($this->gateway !== null) {
            $params['gateway'] = $this->gateway;
        }

        if ($this->sort !== null) {
            $params['sort'] = $this->sort;
        }

        if ($this->code !== null) {
            $params['code'] = $this->code;
        }

        if ($this->prefix !== null) {
            $params['prefix'] = $this->prefix;
        }

        if ($this->version !== null) {
            $params['version'] = $this->version;
        }

        if ($this->title !== null) {
            $params['title'] = $this->title;
        }

        if ($this->url !== null) {
            $params['url'] = $this->url;
        }

        if ($this->reasonId !== null) {
            $params['reason_id'] = $this->reasonId;
        }

        if ($this->source !== null) {
            $params['source'] = $this->source;
        }

        if ($this->enriched) {
            $params['enriched'] = 'true';
        }

        if ($this->extended) {
            $params['extended'] = 'true';
        }

        if ($this->all) {
            $params['all'] = 'true';
        }

        if ($this->userId !== null) {
            $params['user_id'] = $this->userId;
        }

        if ($this->planId !== null) {
            $params['plan_id'] = $this->planId;
        }

        if ($this->licenseId !== null) {
            $params['license_id'] = $this->licenseId;
        }

        if ($this->subscriptionId !== null) {
            $params['subscription_id'] = $this->subscriptionId;
        }

        if ($this->status !== null) {
            $params['status'] = $this->status;
        }

        if ($this->isActive !== null) {
            $params['is_active'] = $this->isActive ? 'true' : 'false';
        }

        if ($this->isPremium !== null) {
            $params['is_premium'] = $this->isPremium ? 'true' : 'false';
        }

        if ($this->isUninstalled !== null) {
            $params['is_uninstalled'] = $this->isUninstalled ? 'true' : 'false';
        }

        if ($this->isDisconnected !== null) {
            $params['is_disconnected'] = $this->isDisconnected ? 'true' : 'false';
        }

        if ($this->countryCode !== null) {
            $params['country_code'] = $this->countryCode;
        }

        if ($this->language !== null) {
            $params['language'] = $this->language;
        }

        if ($this->platformVersion !== null) {
            $params['platform_version'] = $this->platformVersion;
        }

        if ($this->type !== null) {
            $params['type'] = $this->type;
        }

        if ($this->externalId !== null) {
            $params['external_id'] = $this->externalId;
        }

        if ($this->environment !== null) {
            $params['environment'] = $this->environment;
        }

        // Add pagination (Freemius uses 'count' instead of 'limit')
        if ($this->count !== null) {
            $params['count'] = $this->count;
        }

        if ($this->offset !== null) {
            $params['offset'] = $this->offset;
        }
        
        // Add ordering
        if (!empty($this->orderBy)) {
            $orderStrings = [];
            foreach ($this->orderBy as $order) {
                $orderStrings[] = $order['field'] . ':' . $order['direction'];
            }
            $params['order'] = implode(',', $orderStrings);
        }
        
        return $params;
    }
    
    /**
     * Check if the query has any conditions.
     */
    public function hasConditions(): bool
    {
        return !empty($this->wheres) ||
               $this->count !== null ||
               $this->offset !== null ||
               !empty($this->orderBy) ||
               $this->search !== null ||
               $this->email !== null ||
               $this->filter !== null ||
               $this->currency !== null ||
               $this->couponId !== null ||
               $this->billingCycle !== null ||
               $this->gateway !== null ||
               $this->sort !== null ||
               $this->code !== null ||
               $this->prefix !== null ||
               $this->version !== null ||
               $this->title !== null ||
               $this->url !== null ||
               $this->reasonId !== null ||
               $this->source !== null ||
               $this->enriched ||
               $this->extended ||
               $this->all ||
               $this->userId !== null ||
               $this->planId !== null ||
               $this->licenseId !== null ||
               $this->subscriptionId !== null ||
               $this->status !== null ||
               $this->isActive !== null ||
               $this->isPremium !== null ||
               $this->isUninstalled !== null ||
               $this->isDisconnected !== null ||
               $this->countryCode !== null ||
               $this->language !== null ||
               $this->platformVersion !== null ||
               $this->type !== null ||
               $this->externalId !== null ||
               $this->environment !== null;
    }
    
    /**
     * Get the where conditions.
     */
    public function getWheres(): array
    {
        return $this->wheres;
    }
    
    /**
     * Get the count.
     */
    public function getCount(): ?int
    {
        return $this->count;
    }

    /**
     * Get the limit (alias for count for backward compatibility).
     */
    public function getLimit(): ?int
    {
        return $this->count;
    }
    
    /**
     * Get the offset.
     */
    public function getOffset(): ?int
    {
        return $this->offset;
    }
    
    /**
     * Get the order by clauses.
     */
    public function getOrderBy(): array
    {
        return $this->orderBy;
    }

    /**
     * Get the search parameter.
     */
    public function getSearch(): ?string
    {
        return $this->search;
    }

    /**
     * Get the email filter.
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * Reset all query conditions.
     */
    public function reset(): self
    {
        $this->wheres = [];
        $this->count = null;
        $this->offset = null;
        $this->orderBy = [];
        $this->search = null;
        $this->email = null;
        $this->filter = null;
        $this->currency = null;
        $this->couponId = null;
        $this->billingCycle = null;
        $this->gateway = null;
        $this->sort = null;
        $this->code = null;
        $this->prefix = null;
        $this->version = null;
        $this->title = null;
        $this->url = null;
        $this->reasonId = null;
        $this->source = null;
        $this->enriched = false;
        $this->extended = false;
        $this->all = false;
        $this->userId = null;
        $this->planId = null;
        $this->licenseId = null;
        $this->subscriptionId = null;
        $this->status = null;
        $this->isActive = null;
        $this->isPremium = null;
        $this->isUninstalled = null;
        $this->isDisconnected = null;
        $this->countryCode = null;
        $this->language = null;
        $this->platformVersion = null;
        $this->type = null;
        $this->externalId = null;
        $this->environment = null;

        return $this;
    }

    /**
     * Clone the query builder.
     */
    public function clone(): self
    {
        $clone = new static();
        $clone->wheres = $this->wheres;
        $clone->count = $this->count;
        $clone->offset = $this->offset;
        $clone->orderBy = $this->orderBy;
        $clone->search = $this->search;
        $clone->email = $this->email;
        $clone->filter = $this->filter;
        $clone->currency = $this->currency;
        $clone->couponId = $this->couponId;
        $clone->billingCycle = $this->billingCycle;
        $clone->gateway = $this->gateway;
        $clone->sort = $this->sort;
        $clone->code = $this->code;
        $clone->prefix = $this->prefix;
        $clone->version = $this->version;
        $clone->title = $this->title;
        $clone->url = $this->url;
        $clone->reasonId = $this->reasonId;
        $clone->source = $this->source;
        $clone->enriched = $this->enriched;
        $clone->extended = $this->extended;
        $clone->all = $this->all;
        $clone->userId = $this->userId;
        $clone->planId = $this->planId;
        $clone->licenseId = $this->licenseId;
        $clone->subscriptionId = $this->subscriptionId;
        $clone->status = $this->status;
        $clone->isActive = $this->isActive;
        $clone->isPremium = $this->isPremium;
        $clone->isUninstalled = $this->isUninstalled;
        $clone->isDisconnected = $this->isDisconnected;
        $clone->countryCode = $this->countryCode;
        $clone->language = $this->language;
        $clone->platformVersion = $this->platformVersion;
        $clone->type = $this->type;
        $clone->externalId = $this->externalId;
        $clone->environment = $this->environment;

        return $clone;
    }

    /**
     * Get the filter parameter.
     */
    public function getFilter(): ?string
    {
        return $this->filter;
    }

    /**
     * Get the currency parameter.
     */
    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    /**
     * Get the coupon ID parameter.
     */
    public function getCouponId(): ?int
    {
        return $this->couponId;
    }

    /**
     * Get the billing cycle parameter.
     */
    public function getBillingCycle(): ?int
    {
        return $this->billingCycle;
    }

    /**
     * Get the gateway parameter.
     */
    public function getGateway(): ?string
    {
        return $this->gateway;
    }

    /**
     * Get the sort parameter.
     */
    public function getSort(): ?string
    {
        return $this->sort;
    }

    /**
     * Get the enriched flag.
     */
    public function isEnriched(): bool
    {
        return $this->enriched;
    }

    /**
     * Get the extended flag.
     */
    public function isExtended(): bool
    {
        return $this->extended;
    }

    /**
     * Get the all flag.
     */
    public function isAll(): bool
    {
        return $this->all;
    }

    /**
     * Get the user ID parameter.
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Get the plan ID parameter.
     */
    public function getPlanId(): ?int
    {
        return $this->planId;
    }

    /**
     * Get the license ID parameter.
     */
    public function getLicenseId(): ?int
    {
        return $this->licenseId;
    }

    /**
     * Get the subscription ID parameter.
     */
    public function getSubscriptionId(): ?int
    {
        return $this->subscriptionId;
    }

    /**
     * Get the status parameter.
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }
}