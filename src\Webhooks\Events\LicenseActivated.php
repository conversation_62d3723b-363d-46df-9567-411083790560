<?php

declare(strict_types=1);

namespace Freemius\SDK\Webhooks\Events;

use Freemius\SDK\Entities\License;
use Freemius\SDK\Entities\User;
use Freemius\SDK\Entities\Installation;

/**
 * License activation webhook event
 * 
 * Triggered when a license is activated, deactivated, or expired.
 */
class LicenseActivated extends WebhookEvent
{
    private ?License $license = null;
    private ?User $user = null;
    private ?Installation $installation = null;

    /**
     * Get the license associated with this event
     *
     * @return License|null The license entity
     */
    public function getLicense(): ?License
    {
        if ($this->license === null) {
            $licenseData = $this->getNestedData('license');
            if ($licenseData) {
                $this->license = $this->createEntity($licenseData, License::class);
            }
        }

        return $this->license;
    }

    /**
     * Get the user associated with this event
     *
     * @return User|null The user entity
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userData = $this->getNestedData('user');
            if ($userData) {
                $this->user = $this->createEntity($userData, User::class);
            }
        }

        return $this->user;
    }

    /**
     * Get the installation associated with this event
     *
     * @return Installation|null The installation entity
     */
    public function getInstallation(): ?Installation
    {
        if ($this->installation === null) {
            $installData = $this->getNestedData('install') ?? $this->getNestedData('installation');
            if ($installData) {
                $this->installation = $this->createEntity($installData, Installation::class);
            }
        }

        return $this->installation;
    }

    /**
     * Get the license ID
     *
     * @return int|null The license ID
     */
    public function getLicenseId(): ?int
    {
        $licenseId = $this->get('license_id');
        return $licenseId ? (int) $licenseId : null;
    }

    /**
     * Check if this is a license activation
     *
     * @return bool True if license was activated
     */
    public function isActivation(): bool
    {
        return $this->eventType === 'license.activated';
    }

    /**
     * Check if this is a license deactivation
     *
     * @return bool True if license was deactivated
     */
    public function isDeactivation(): bool
    {
        return $this->eventType === 'license.deactivated';
    }

    /**
     * Check if this is a license expiration
     *
     * @return bool True if license expired
     */
    public function isExpiration(): bool
    {
        return $this->eventType === 'license.expired';
    }

    /**
     * Get event summary for logging
     *
     * @return string A brief summary of the event
     */
    public function getSummary(): string
    {
        $action = match ($this->eventType) {
            'license.activated' => 'activated',
            'license.deactivated' => 'deactivated',
            'license.expired' => 'expired',
            default => 'changed'
        };

        $parts = ["License {$action}"];

        if ($licenseId = $this->getLicenseId()) {
            $parts[] = "license:{$licenseId}";
        }

        return implode(' ', $parts);
    }
}