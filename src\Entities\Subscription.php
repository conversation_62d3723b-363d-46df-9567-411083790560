<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;

/**
 * Subscription entity representing a Freemius subscription.
 * 
 * A subscription is created when a user uses Freemius Checkout to purchase 
 * or subscribe to a plan of a product.
 */
class Subscription extends AbstractEntity
{
    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'install_id' => 'integer',
        'plan_id' => 'integer',
        'pricing_id' => 'integer',
        'license_id' => 'integer',
        'coupon_id' => 'integer',
        'user_card_id' => 'integer',
        'plugin_id' => 'integer',
        'environment' => 'integer',
        'source' => 'integer',
        'tax_rate' => 'float',
        'total_gross' => 'float',
        'amount_per_cycle' => 'float',
        'initial_amount' => 'float',
        'renewal_amount' => 'float',
        'renewals_discount' => 'integer',
        'billing_cycle' => 'integer',
        'outstanding_balance' => 'float',
        'failed_payments' => 'integer',
    ];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [
        'created',
        'updated',
        'trial_ends',
        'next_payment',
        'canceled_at',
    ];

    /**
     * Get the subscription ID.
     */
    public function getId(): int
    {
        return $this->getAttribute('id');
    }

    /**
     * Get the user ID.
     */
    public function getUserId(): int
    {
        return $this->getAttribute('user_id');
    }

    /**
     * Get the installation ID.
     */
    public function getInstallId(): ?int
    {
        return $this->getAttribute('install_id');
    }

    /**
     * Get the plan ID.
     */
    public function getPlanId(): int
    {
        return $this->getAttribute('plan_id');
    }

    /**
     * Get the pricing ID.
     */
    public function getPricingId(): ?int
    {
        return $this->getAttribute('pricing_id');
    }

    /**
     * Get the license ID.
     */
    public function getLicenseId(): int
    {
        return $this->getAttribute('license_id');
    }

    /**
     * Get the product ID.
     */
    public function getPluginId(): int
    {
        return $this->getAttribute('plugin_id');
    }

    /**
     * Get the external ID from the payment gateway.
     */
    public function getExternalId(): ?string
    {
        return $this->getAttribute('external_id');
    }

    /**
     * Get the payment gateway used.
     */
    public function getGateway(): ?string
    {
        return $this->getAttribute('gateway');
    }

    /**
     * Get the IP address.
     */
    public function getIp(): ?string
    {
        return $this->getAttribute('ip');
    }

    /**
     * Get the country code.
     */
    public function getCountryCode(): string
    {
        return $this->getAttribute('country_code', '');
    }

    /**
     * Get the postal/zip code.
     */
    public function getZipPostalCode(): ?string
    {
        return $this->getAttribute('zip_postal_code');
    }

    /**
     * Get the VAT ID.
     */
    public function getVatId(): ?string
    {
        return $this->getAttribute('vat_id');
    }

    /**
     * Get the coupon ID.
     */
    public function getCouponId(): ?int
    {
        return $this->getAttribute('coupon_id');
    }

    /**
     * Check if a coupon was used.
     */
    public function hasCoupon(): bool
    {
        return $this->getCouponId() !== null;
    }

    /**
     * Get the user card ID.
     */
    public function getUserCardId(): ?int
    {
        return $this->getAttribute('user_card_id');
    }

    /**
     * Get the currency.
     */
    public function getCurrency(): string
    {
        return $this->getAttribute('currency', 'usd');
    }

    /**
     * Get the tax rate as a fraction.
     */
    public function getTaxRate(): float
    {
        return $this->getAttribute('tax_rate', 0.0);
    }

    /**
     * Get the total gross amount including taxes.
     */
    public function getTotalGross(): float
    {
        return $this->getAttribute('total_gross', 0.0);
    }

    /**
     * Get the plan's original amount per cycle (excluding taxes).
     */
    public function getAmountPerCycle(): float
    {
        return $this->getAttribute('amount_per_cycle', 0.0);
    }

    /**
     * Get the initial payment amount (excluding taxes).
     */
    public function getInitialAmount(): float
    {
        return $this->getAttribute('initial_amount', 0.0);
    }

    /**
     * Get the renewal amount (excluding taxes).
     */
    public function getRenewalAmount(): float
    {
        return $this->getAttribute('renewal_amount', 0.0);
    }

    /**
     * Get the renewals discount percentage.
     */
    public function getRenewalsDiscount(): int
    {
        return $this->getAttribute('renewals_discount', 0);
    }

    /**
     * Get the renewals discount type.
     */
    public function getRenewalsDiscountType(): string
    {
        return $this->getAttribute('renewals_discount_type', 'percentage');
    }

    /**
     * Get the billing cycle in months.
     */
    public function getBillingCycle(): int
    {
        return $this->getAttribute('billing_cycle', 1);
    }

    /**
     * Check if this is a monthly subscription.
     */
    public function isMonthly(): bool
    {
        return $this->getBillingCycle() === 1;
    }

    /**
     * Check if this is an annual subscription.
     */
    public function isAnnual(): bool
    {
        return $this->getBillingCycle() === 12;
    }

    /**
     * Check if this is a lifetime subscription.
     */
    public function isLifetime(): bool
    {
        return $this->getBillingCycle() === 0;
    }

    /**
     * Get the outstanding balance.
     */
    public function getOutstandingBalance(): float
    {
        return $this->getAttribute('outstanding_balance', 0.0);
    }

    /**
     * Get the number of failed payments.
     */
    public function getFailedPayments(): int
    {
        return $this->getAttribute('failed_payments', 0);
    }

    /**
     * Check if there are failed payments.
     */
    public function hasFailedPayments(): bool
    {
        return $this->getFailedPayments() > 0;
    }

    /**
     * Get the trial end date.
     */
    public function getTrialEnds(): ?DateTime
    {
        return $this->getAttribute('trial_ends');
    }

    /**
     * Check if the subscription is in trial.
     */
    public function isInTrial(): bool
    {
        $trialEnds = $this->getTrialEnds();
        return $trialEnds !== null && $trialEnds->getTimestamp() > time();
    }

    /**
     * Check if the trial has expired.
     */
    public function isTrialExpired(): bool
    {
        $trialEnds = $this->getTrialEnds();
        return $trialEnds !== null && $trialEnds->getTimestamp() <= time();
    }

    /**
     * Get the next payment date.
     */
    public function getNextPayment(): ?DateTime
    {
        return $this->getAttribute('next_payment');
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->getNextPayment() !== null && !$this->isCanceled();
    }

    /**
     * Get the cancellation date.
     */
    public function getCanceledAt(): ?DateTime
    {
        return $this->getAttribute('canceled_at');
    }

    /**
     * Check if the subscription is canceled.
     */
    public function isCanceled(): bool
    {
        return $this->getCanceledAt() !== null;
    }

    /**
     * Get the environment (0 = production, 1 = sandbox).
     */
    public function getEnvironment(): int
    {
        return $this->getAttribute('environment', 0);
    }

    /**
     * Check if this is a sandbox subscription.
     */
    public function isSandbox(): bool
    {
        return $this->getEnvironment() === 1;
    }

    /**
     * Get the migration source.
     */
    public function getSource(): int
    {
        return $this->getAttribute('source', 0);
    }

    /**
     * Check if this subscription was migrated from another platform.
     */
    public function isMigrated(): bool
    {
        return $this->getSource() !== 0;
    }

    /**
     * Get the creation date.
     */
    public function getCreatedAt(): DateTime
    {
        return $this->getAttribute('created');
    }

    /**
     * Get the last update date.
     */
    public function getUpdatedAt(): ?DateTime
    {
        return $this->getAttribute('updated');
    }
}