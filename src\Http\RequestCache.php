<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use Psr\Http\Message\ResponseInterface;

/**
 * Request cache for read-only operations
 * 
 * Provides in-memory caching of GET requests to reduce API calls
 * and improve performance for frequently accessed data.
 */
class RequestCache
{
    private array $cache = [];
    private array $cacheTtl = [];
    private int $defaultTtl;
    private int $maxCacheSize;
    private bool $enabled;

    /**
     * Create a new request cache instance
     *
     * @param bool $enabled Whether caching is enabled
     * @param int $defaultTtl Default TTL in seconds
     * @param int $maxCacheSize Maximum number of cached items
     */
    public function __construct(bool $enabled = true, int $defaultTtl = 300, int $maxCacheSize = 1000)
    {
        $this->enabled = $enabled;
        $this->defaultTtl = $defaultTtl;
        $this->maxCacheSize = $maxCacheSize;
    }

    /**
     * Get cached response if available and not expired
     *
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @param array $params Request parameters
     * @return ResponseInterface|null Cached response or null if not found/expired
     */
    public function get(string $method, string $uri, array $params = []): ?ResponseInterface
    {
        if (!$this->enabled || !$this->isCacheable($method)) {
            return null;
        }

        $key = $this->generateCacheKey($method, $uri, $params);
        
        if (!isset($this->cache[$key])) {
            return null;
        }

        // Check if expired
        if (isset($this->cacheTtl[$key]) && $this->cacheTtl[$key] < time()) {
            $this->delete($key);
            return null;
        }

        return $this->cache[$key];
    }

    /**
     * Store response in cache
     *
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @param array $params Request parameters
     * @param ResponseInterface $response Response to cache
     * @param int|null $ttl TTL in seconds, null for default
     * @return void
     */
    public function put(string $method, string $uri, array $params, ResponseInterface $response, ?int $ttl = null): void
    {
        if (!$this->enabled || !$this->isCacheable($method) || !$this->isResponseCacheable($response)) {
            return;
        }

        $key = $this->generateCacheKey($method, $uri, $params);
        $ttl = $ttl ?? $this->defaultTtl;

        // Ensure we don't exceed max cache size
        if (count($this->cache) >= $this->maxCacheSize && !isset($this->cache[$key])) {
            $this->evictOldest();
        }

        $this->cache[$key] = $response;
        $this->cacheTtl[$key] = time() + $ttl;
    }

    /**
     * Delete cached item
     *
     * @param string $key Cache key
     * @return void
     */
    public function delete(string $key): void
    {
        unset($this->cache[$key], $this->cacheTtl[$key]);
    }

    /**
     * Clear all cached items
     *
     * @return void
     */
    public function clear(): void
    {
        $this->cache = [];
        $this->cacheTtl = [];
    }

    /**
     * Invalidate cache entries matching a pattern
     *
     * @param string $pattern URI pattern to match (supports wildcards)
     * @return int Number of invalidated entries
     */
    public function invalidate(string $pattern): int
    {
        $count = 0;
        $regex = $this->patternToRegex($pattern);

        foreach (array_keys($this->cache) as $key) {
            if (preg_match($regex, $key)) {
                $this->delete($key);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function getStats(): array
    {
        $now = time();
        $expired = 0;
        $valid = 0;

        foreach ($this->cacheTtl as $ttl) {
            if ($ttl < $now) {
                $expired++;
            } else {
                $valid++;
            }
        }

        return [
            'enabled' => $this->enabled,
            'total_items' => count($this->cache),
            'valid_items' => $valid,
            'expired_items' => $expired,
            'max_size' => $this->maxCacheSize,
            'default_ttl' => $this->defaultTtl,
            'memory_usage' => $this->estimateMemoryUsage(),
        ];
    }

    /**
     * Enable or disable caching
     *
     * @param bool $enabled Whether to enable caching
     * @return void
     */
    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
        
        if (!$enabled) {
            $this->clear();
        }
    }

    /**
     * Set default TTL
     *
     * @param int $ttl TTL in seconds
     * @return void
     */
    public function setDefaultTtl(int $ttl): void
    {
        $this->defaultTtl = max(1, $ttl);
    }

    /**
     * Set maximum cache size
     *
     * @param int $maxSize Maximum number of cached items
     * @return void
     */
    public function setMaxCacheSize(int $maxSize): void
    {
        $this->maxCacheSize = max(1, $maxSize);
        
        // Evict items if we're over the new limit
        while (count($this->cache) > $this->maxCacheSize) {
            $this->evictOldest();
        }
    }

    /**
     * Check if HTTP method is cacheable
     *
     * @param string $method HTTP method
     * @return bool True if cacheable
     */
    private function isCacheable(string $method): bool
    {
        return strtoupper($method) === 'GET';
    }

    /**
     * Check if response is cacheable
     *
     * @param ResponseInterface $response HTTP response
     * @return bool True if cacheable
     */
    private function isResponseCacheable(ResponseInterface $response): bool
    {
        $statusCode = $response->getStatusCode();
        
        // Only cache successful responses
        if ($statusCode < 200 || $statusCode >= 300) {
            return false;
        }

        // Check Cache-Control header
        $cacheControl = $response->getHeaderLine('Cache-Control');
        if (stripos($cacheControl, 'no-cache') !== false || stripos($cacheControl, 'no-store') !== false) {
            return false;
        }

        return true;
    }

    /**
     * Generate cache key for request
     *
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @param array $params Request parameters
     * @return string Cache key
     */
    private function generateCacheKey(string $method, string $uri, array $params): string
    {
        // Sort parameters for consistent keys
        ksort($params);
        
        $key = strtoupper($method) . ':' . $uri;
        
        if (!empty($params)) {
            $key .= '?' . http_build_query($params);
        }
        
        return $key;
    }

    /**
     * Evict the oldest cached item
     *
     * @return void
     */
    private function evictOldest(): void
    {
        if (empty($this->cache)) {
            return;
        }

        // Find the item with the earliest TTL
        $oldestKey = null;
        $oldestTtl = PHP_INT_MAX;

        foreach ($this->cacheTtl as $key => $ttl) {
            if ($ttl < $oldestTtl) {
                $oldestTtl = $ttl;
                $oldestKey = $key;
            }
        }

        if ($oldestKey !== null) {
            $this->delete($oldestKey);
        }
    }

    /**
     * Convert wildcard pattern to regex
     *
     * @param string $pattern Wildcard pattern
     * @return string Regex pattern
     */
    private function patternToRegex(string $pattern): string
    {
        $escaped = preg_quote($pattern, '/');
        $regex = str_replace('\*', '.*', $escaped);
        return '/^' . $regex . '$/i';
    }

    /**
     * Estimate memory usage of cache
     *
     * @return int Estimated memory usage in bytes
     */
    private function estimateMemoryUsage(): int
    {
        $size = 0;
        
        foreach ($this->cache as $key => $response) {
            $size += strlen($key);
            $size += strlen($response->getBody()->getContents());
            $response->getBody()->rewind(); // Reset stream position
        }
        
        return $size;
    }
}