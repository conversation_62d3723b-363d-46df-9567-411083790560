# Project Structure & Organization

## Root Directory Layout
```
├── .git/                    # Git repository
├── .kiro/                   # Kiro IDE configuration
│   └── steering/           # AI assistant steering rules
├── docs/                   # Documentation and specifications
│   ├── openapi-Freemius.json  # Freemius API specification
│   └── specifications.md   # Project requirements
├── logs/                   # Application logs (gitignored)
├── src/                    # Main source code (PSR-4: Freemius\SDK\)
├── vendor/                 # Composer dependencies
├── composer.json           # PHP dependencies and autoloading
├── phpunit.xml            # PHPUnit configuration
└── README.md              # Project documentation
```

## Source Code Organization (`src/`)
- **Namespace**: `Freemius\SDK\`
- **PSR-4 Autoloading**: All classes under `src/` map to `Freemius\SDK\` namespace
- **Architecture**: Follow production-grade PHP patterns and SOLID principles

## Key Configuration Files
- **`.swagger-mcp`**: Points to Freemius API specification for MCP tools
- **`composer.json`**: Defines dependencies, autoloading, and project metadata
- **`phpunit.xml`**: PHPUnit 11.5+ configuration with strict settings
- **`.gitignore`**: Excludes vendor/, logs/, and other generated files

## Documentation Standards
- **API Reference**: Use OpenAPI specification in `docs/openapi-Freemius.json`
- **Specifications**: Detailed requirements in `docs/specifications.md`
- **Code Documentation**: PHPDoc blocks for all public methods and classes

## Naming Conventions
- **Classes**: PascalCase (e.g., `FreemiusClient`, `WebhookHandler`)
- **Methods**: camelCase with method chaining support
- **Constants**: UPPER_SNAKE_CASE
- **Files**: Match class names exactly

## Quality Standards
- All code must be production-grade and commercial-ready
- Follow PSR-12 coding standards
- Implement proper error handling and logging
- Support method chaining where applicable