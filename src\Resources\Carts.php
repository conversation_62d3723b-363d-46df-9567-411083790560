<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Cart;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Carts resource for managing Freemius shopping carts.
 * 
 * Handles CRUD operations and specialized methods for cart management.
 */
class Carts extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'carts';
    }
    
    protected function getEntityClass(): string
    {
        return Cart::class;
    }
    
    /**
     * Get active carts only.
     */
    public function active(): self
    {
        return $this->where('is_active', true);
    }
    
    /**
     * Get inactive carts only.
     */
    public function inactive(): self
    {
        return $this->where('is_active', false);
    }
    
    /**
     * Get expired carts only.
     */
    public function expired(): self
    {
        return $this->where('expires_at', '<', date('Y-m-d H:i:s'));
    }
    
    /**
     * Get carts by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get carts by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get carts by billing cycle.
     */
    public function byBillingCycle(string $cycle): self
    {
        return $this->where('billing_cycle', $cycle);
    }
    
    /**
     * Get monthly carts.
     */
    public function monthly(): self
    {
        return $this->byBillingCycle('monthly');
    }
    
    /**
     * Get annual carts.
     */
    public function annual(): self
    {
        return $this->byBillingCycle('annual');
    }
    
    /**
     * Get lifetime carts.
     */
    public function lifetime(): self
    {
        return $this->byBillingCycle('lifetime');
    }
    
    /**
     * Get carts by currency.
     */
    public function byCurrency(string $currency): self
    {
        return $this->where('currency', $currency);
    }
    
    /**
     * Get carts with price greater than specified value.
     */
    public function priceGreaterThan(float $amount): self
    {
        return $this->where('price', '>', $amount);
    }
    
    /**
     * Get carts with price less than specified value.
     */
    public function priceLessThan(float $amount): self
    {
        return $this->where('price', '<', $amount);
    }
    
    /**
     * Get carts created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get carts created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get carts by status.
     */
    public function byStatus(string $status): self
    {
        return $this->where('status', $status);
    }
    
    /**
     * Get completed carts.
     */
    public function completed(): self
    {
        return $this->byStatus('completed');
    }
    
    /**
     * Get abandoned carts.
     */
    public function abandoned(): self
    {
        return $this->byStatus('abandoned');
    }
    
    /**
     * Get recovered carts.
     */
    public function recovered(): self
    {
        return $this->byStatus('recovered');
    }
    
    /**
     * Apply coupon to cart.
     */
    public function applyCoupon(int $cartId, string $couponCode)
    {
        $endpoint = $this->buildEndpoint($cartId) . '/coupon';
        $response = $this->httpClient->post($endpoint, ['coupon_code' => $couponCode]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Cart($data);
    }
    
    /**
     * Remove coupon from cart.
     */
    public function removeCoupon(int $cartId)
    {
        $endpoint = $this->buildEndpoint($cartId) . '/coupon';
        $response = $this->httpClient->delete($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Cart($data);
    }
    
    /**
     * Update cart quantities.
     */
    public function updateQuantity(int $cartId, int $licenses)
    {
        $endpoint = $this->buildEndpoint($cartId) . '/quantity';
        $response = $this->httpClient->put($endpoint, ['licenses' => $licenses]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Cart($data);
    }
    
    /**
     * Change cart billing cycle.
     */
    public function changeBillingCycle(int $cartId, string $billingCycle)
    {
        $endpoint = $this->buildEndpoint($cartId) . '/billing-cycle';
        $response = $this->httpClient->put($endpoint, ['billing_cycle' => $billingCycle]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Cart($data);
    }
    
    /**
     * Checkout cart and create payment.
     */
    public function checkout(int $cartId, array $checkoutData = []): array
    {
        $endpoint = $this->buildEndpoint($cartId) . '/checkout';
        $response = $this->httpClient->post($endpoint, $checkoutData);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Abandon cart (mark as inactive).
     */
    public function abandon(int $cartId): bool
    {
        $endpoint = $this->buildEndpoint($cartId) . '/abandon';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Restore abandoned cart.
     */
    public function restore(int $cartId)
    {
        $endpoint = $this->buildEndpoint($cartId) . '/restore';
        $response = $this->httpClient->post($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new Cart($data);
    }
    
    /**
     * Get cart totals breakdown.
     */
    public function getTotals(int $cartId): array
    {
        $endpoint = $this->buildEndpoint($cartId) . '/totals';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'product_id', 'plan_id'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a cart");
            }
        }
        
        // Validate numeric fields
        $numericFields = ['user_id', 'product_id', 'plan_id', 'licenses'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate licenses count
        if (isset($data['licenses']) && $data['licenses'] < 1) {
            throw new ValidationException("Licenses count must be at least 1");
        }
        
        // Validate billing cycle if provided
        if (isset($data['billing_cycle'])) {
            $validCycles = ['monthly', 'annual', 'lifetime'];
            if (!in_array($data['billing_cycle'], $validCycles)) {
                throw new ValidationException("Invalid billing cycle. Must be one of: " . implode(', ', $validCycles));
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        $numericFields = ['user_id', 'product_id', 'plan_id', 'licenses'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                throw new ValidationException("Field '{$field}' must be numeric");
            }
        }
        
        // Validate licenses count if provided
        if (isset($data['licenses']) && $data['licenses'] < 1) {
            throw new ValidationException("Licenses count must be at least 1");
        }
        
        // Validate billing cycle if provided
        if (isset($data['billing_cycle'])) {
            $validCycles = ['monthly', 'annual', 'lifetime'];
            if (!in_array($data['billing_cycle'], $validCycles)) {
                throw new ValidationException("Invalid billing cycle. Must be one of: " . implode(', ', $validCycles));
            }
        }
        
        // Validate currency if provided
        if (isset($data['currency']) && strlen($data['currency']) !== 3) {
            throw new ValidationException("Currency must be a 3-letter ISO code");
        }
    }
}